module.exports = {

"[project]/.next-internal/server/app/api/sync-vehicles/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/mobile-api.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "MobileDeApiService": ()=>MobileDeApiService,
    "mobileDeApi": ()=>mobileDeApi
});
// Mobile.de API Configuration
const MOBILE_API_BASE_URL = 'https://services.mobile.de/search-api/search';
const MOBILE_SELLER_API_URL = 'https://services.mobile.de/seller-api';
// Convert mobile.de vehicle to our Vehicle type
function convertMobileDeVehicle(mobileVehicle) {
    const primaryImage = mobileVehicle.images[0];
    return {
        id: mobileVehicle.id,
        make: mobileVehicle.make,
        model: mobileVehicle.model,
        variant: mobileVehicle.variant,
        year: new Date(mobileVehicle.firstRegistration).getFullYear(),
        firstRegistration: mobileVehicle.firstRegistration,
        mileage: mobileVehicle.mileage,
        price: mobileVehicle.price.consumerPriceAmount,
        netPrice: mobileVehicle.price.dealerPriceAmount,
        vatIncluded: !!mobileVehicle.price.vatRate,
        power: {
            kw: mobileVehicle.power.kw,
            ps: mobileVehicle.power.hp
        },
        fuel: mapFuelType(mobileVehicle.fuel),
        transmission: mapTransmissionType(mobileVehicle.transmission),
        bodyType: mapBodyType(mobileVehicle.category),
        condition: mapConditionType(mobileVehicle.condition),
        images: mobileVehicle.images.map((img, index)=>({
                id: `${mobileVehicle.id}-${index}`,
                url: img.uri,
                alt: img.description || `${mobileVehicle.make} ${mobileVehicle.model}`,
                isPrimary: index === 0,
                order: index + 1
            })),
        features: mobileVehicle.features || [],
        description: mobileVehicle.description,
        exteriorColor: mobileVehicle.exteriorColor,
        interiorColor: mobileVehicle.interiorColor,
        doors: mobileVehicle.doors,
        seats: mobileVehicle.seats,
        previousOwners: mobileVehicle.previousOwners,
        co2Emissions: mobileVehicle.co2Emission,
        fuelConsumption: mobileVehicle.fuelConsumption ? {
            combined: mobileVehicle.fuelConsumption.combinedConsumption,
            city: mobileVehicle.fuelConsumption.cityConsumption,
            highway: mobileVehicle.fuelConsumption.highwayConsumption
        } : undefined,
        energyEfficiencyClass: mobileVehicle.energyEfficiencyClass,
        location: mobileVehicle.location ? {
            city: mobileVehicle.location.city,
            postalCode: mobileVehicle.location.zipCode,
            country: mobileVehicle.location.countryCode === 'DE' ? 'Deutschland' : mobileVehicle.location.countryCode
        } : undefined,
        dealer: mobileVehicle.seller ? {
            name: mobileVehicle.seller.name,
            address: {
                street: mobileVehicle.seller.address.street,
                city: mobileVehicle.seller.address.city,
                postalCode: mobileVehicle.seller.address.zipCode,
                country: mobileVehicle.seller.address.countryCode === 'DE' ? 'Deutschland' : mobileVehicle.seller.address.countryCode
            },
            contact: {
                phone: mobileVehicle.seller.phone,
                email: mobileVehicle.seller.email
            }
        } : undefined,
        createdAt: mobileVehicle.creationDate,
        updatedAt: mobileVehicle.modificationDate,
        isAvailable: true,
        isFeatured: false
    };
}
// Mapping functions for mobile.de data
function mapFuelType(mobileFuel) {
    const fuelMap = {
        'PETROL': 'Benzin',
        'DIESEL': 'Diesel',
        'ELECTRIC': 'Elektro',
        'HYBRID_PETROL': 'Hybrid',
        'HYBRID_DIESEL': 'Hybrid',
        'PLUGIN_HYBRID': 'Plug-in-Hybrid',
        'CNG': 'Erdgas',
        'LPG': 'Autogas',
        'HYDROGEN': 'Wasserstoff'
    };
    return fuelMap[mobileFuel] || 'Andere';
}
function mapTransmissionType(mobileTransmission) {
    const transmissionMap = {
        'MANUAL_GEAR': 'Schaltgetriebe',
        'AUTOMATIC_GEAR': 'Automatik',
        'SEMI_AUTOMATIC_GEAR': 'Halbautomatik',
        'CVT': 'CVT'
    };
    return transmissionMap[mobileTransmission] || 'Schaltgetriebe';
}
function mapBodyType(mobileCategory) {
    const bodyTypeMap = {
        'SEDAN': 'Limousine',
        'STATION_WAGON': 'Kombi',
        'SUV': 'SUV',
        'COUPE': 'Coupe',
        'CONVERTIBLE': 'Cabrio',
        'SMALL_CAR': 'Kleinwagen',
        'COMPACT': 'Kompaktklasse',
        'VAN': 'Van',
        'PICKUP': 'Pickup',
        'SPORTS_CAR': 'Sportwagen'
    };
    return bodyTypeMap[mobileCategory] || 'Andere';
}
function mapConditionType(mobileCondition) {
    const conditionMap = {
        'NEW': 'Neuwagen',
        'USED': 'Gebrauchtwagen',
        'DEMONSTRATION': 'Vorführwagen',
        'EMPLOYEE_CAR': 'Jahreswagen',
        'ACCIDENT': 'Unfallfahrzeug'
    };
    return conditionMap[mobileCondition] || 'Gebrauchtwagen';
}
class MobileDeApiService {
    apiKey;
    sellerId;
    constructor(apiKey, sellerId){
        this.apiKey = apiKey;
        this.sellerId = sellerId;
    }
    // Fetch vehicles from mobile.de seller API
    async fetchVehicles() {
        try {
            // Note: This is a mock implementation
            // In a real implementation, you would make actual API calls to mobile.de
            console.log('Fetching vehicles from mobile.de API...');
            // For now, return mock data
            // In production, replace this with actual API call:
            // const response = await fetch(`${MOBILE_SELLER_API_URL}/vehicles`, {
            //   headers: {
            //     'Authorization': `Bearer ${this.apiKey}`,
            //     'Content-Type': 'application/json'
            //   }
            // });
            // Mock response simulation
            await new Promise((resolve)=>setTimeout(resolve, 1000));
            // Return mock vehicles (in production, this would be converted from API response)
            return this.getMockVehicles();
        } catch (error) {
            console.error('Error fetching vehicles from mobile.de:', error);
            throw new Error('Failed to fetch vehicles from mobile.de API');
        }
    }
    // Mock data for development
    getMockVehicles() {
        // This would be replaced with actual API data conversion
        return [];
    }
    // Sync vehicles with mobile.de
    async syncVehicles() {
        try {
            const vehicles = await this.fetchVehicles();
            // Here you would typically save the vehicles to your database
            console.log(`Successfully synced ${vehicles.length} vehicles from mobile.de`);
            return {
                success: true,
                count: vehicles.length
            };
        } catch (error) {
            console.error('Error syncing vehicles:', error);
            return {
                success: false,
                count: 0,
                errors: [
                    error instanceof Error ? error.message : 'Unknown error'
                ]
            };
        }
    }
}
const mobileDeApi = new MobileDeApiService(process.env.MOBILE_DE_API_KEY || 'mock-api-key', process.env.MOBILE_DE_SELLER_ID || 'mock-seller-id');
}),
"[project]/src/app/api/sync-vehicles/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>GET,
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mobile$2d$api$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mobile-api.ts [app-route] (ecmascript)");
;
;
async function POST(request) {
    try {
        // In production, you might want to add authentication here
        // const authHeader = request.headers.get('authorization');
        // if (!authHeader || !isValidApiKey(authHeader)) {
        //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        // }
        console.log('Starting vehicle sync from mobile.de...');
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mobile$2d$api$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mobileDeApi"].syncVehicles();
        if (result.success) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                message: `Successfully synced ${result.count} vehicles from mobile.de`,
                count: result.count,
                timestamp: new Date().toISOString()
            });
        } else {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: 'Failed to sync vehicles',
                errors: result.errors,
                timestamp: new Date().toISOString()
            }, {
                status: 500
            });
        }
    } catch (error) {
        console.error('Error in sync-vehicles API:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            message: 'Internal server error during vehicle sync',
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date().toISOString()
        }, {
            status: 500
        });
    }
}
async function GET(request) {
    try {
        // Get sync status or last sync information
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            status: 'ready',
            message: 'Vehicle sync API is ready',
            lastSync: null,
            endpoint: '/api/sync-vehicles',
            method: 'POST',
            description: 'Synchronizes vehicles from mobile.de API'
        });
    } catch (error) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to get sync status',
            message: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__4bdbbce1._.js.map