{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/automobilenordneu/automobile-nord-neu/src/lib/mobile-api.ts"], "sourcesContent": ["import { Vehicle } from '@/types/vehicle';\n\n// Mobile.de API Configuration\nconst MOBILE_API_BASE_URL = 'https://services.mobile.de/search-api/search';\nconst MOBILE_SELLER_API_URL = 'https://services.mobile.de/seller-api';\n\n// Types for mobile.de API responses\ninterface MobileDeVehicle {\n  id: string;\n  make: string;\n  model: string;\n  variant?: string;\n  firstRegistration: string;\n  mileage: number;\n  price: {\n    consumerPriceAmount: number;\n    dealerPriceAmount?: number;\n    currency: string;\n    vatRate?: number;\n  };\n  power: {\n    kw: number;\n    hp: number;\n  };\n  fuel: string;\n  transmission: string;\n  category: string;\n  condition: string;\n  images: Array<{\n    uri: string;\n    description?: string;\n  }>;\n  features: string[];\n  description?: string;\n  exteriorColor?: string;\n  interiorColor?: string;\n  doors?: number;\n  seats?: number;\n  previousOwners?: number;\n  co2Emission?: number;\n  fuelConsumption?: {\n    combinedConsumption?: number;\n    cityConsumption?: number;\n    highwayConsumption?: number;\n  };\n  energyEfficiencyClass?: string;\n  location?: {\n    city: string;\n    zipCode: string;\n    countryCode: string;\n  };\n  seller?: {\n    name: string;\n    address: {\n      street: string;\n      city: string;\n      zipCode: string;\n      countryCode: string;\n    };\n    phone: string;\n    email: string;\n  };\n  creationDate: string;\n  modificationDate: string;\n}\n\ninterface MobileDeSearchResponse {\n  searchResults: MobileDeVehicle[];\n  totalResultCount: number;\n  pageNumber: number;\n  pageSize: number;\n}\n\n// Convert mobile.de vehicle to our Vehicle type\nfunction convertMobileDeVehicle(mobileVehicle: MobileDeVehicle): Vehicle {\n  const primaryImage = mobileVehicle.images[0];\n  \n  return {\n    id: mobileVehicle.id,\n    make: mobileVehicle.make,\n    model: mobileVehicle.model,\n    variant: mobileVehicle.variant,\n    year: new Date(mobileVehicle.firstRegistration).getFullYear(),\n    firstRegistration: mobileVehicle.firstRegistration,\n    mileage: mobileVehicle.mileage,\n    price: mobileVehicle.price.consumerPriceAmount,\n    netPrice: mobileVehicle.price.dealerPriceAmount,\n    vatIncluded: !!mobileVehicle.price.vatRate,\n    power: {\n      kw: mobileVehicle.power.kw,\n      ps: mobileVehicle.power.hp\n    },\n    fuel: mapFuelType(mobileVehicle.fuel),\n    transmission: mapTransmissionType(mobileVehicle.transmission),\n    bodyType: mapBodyType(mobileVehicle.category),\n    condition: mapConditionType(mobileVehicle.condition),\n    images: mobileVehicle.images.map((img, index) => ({\n      id: `${mobileVehicle.id}-${index}`,\n      url: img.uri,\n      alt: img.description || `${mobileVehicle.make} ${mobileVehicle.model}`,\n      isPrimary: index === 0,\n      order: index + 1\n    })),\n    features: mobileVehicle.features || [],\n    description: mobileVehicle.description,\n    exteriorColor: mobileVehicle.exteriorColor,\n    interiorColor: mobileVehicle.interiorColor,\n    doors: mobileVehicle.doors,\n    seats: mobileVehicle.seats,\n    previousOwners: mobileVehicle.previousOwners,\n    co2Emissions: mobileVehicle.co2Emission,\n    fuelConsumption: mobileVehicle.fuelConsumption ? {\n      combined: mobileVehicle.fuelConsumption.combinedConsumption,\n      city: mobileVehicle.fuelConsumption.cityConsumption,\n      highway: mobileVehicle.fuelConsumption.highwayConsumption\n    } : undefined,\n    energyEfficiencyClass: mobileVehicle.energyEfficiencyClass,\n    location: mobileVehicle.location ? {\n      city: mobileVehicle.location.city,\n      postalCode: mobileVehicle.location.zipCode,\n      country: mobileVehicle.location.countryCode === 'DE' ? 'Deutschland' : mobileVehicle.location.countryCode\n    } : undefined,\n    dealer: mobileVehicle.seller ? {\n      name: mobileVehicle.seller.name,\n      address: {\n        street: mobileVehicle.seller.address.street,\n        city: mobileVehicle.seller.address.city,\n        postalCode: mobileVehicle.seller.address.zipCode,\n        country: mobileVehicle.seller.address.countryCode === 'DE' ? 'Deutschland' : mobileVehicle.seller.address.countryCode\n      },\n      contact: {\n        phone: mobileVehicle.seller.phone,\n        email: mobileVehicle.seller.email\n      }\n    } : undefined,\n    createdAt: mobileVehicle.creationDate,\n    updatedAt: mobileVehicle.modificationDate,\n    isAvailable: true,\n    isFeatured: false\n  };\n}\n\n// Mapping functions for mobile.de data\nfunction mapFuelType(mobileFuel: string): any {\n  const fuelMap: Record<string, any> = {\n    'PETROL': 'Benzin',\n    'DIESEL': 'Diesel',\n    'ELECTRIC': 'Elektro',\n    'HYBRID_PETROL': 'Hybrid',\n    'HYBRID_DIESEL': 'Hybrid',\n    'PLUGIN_HYBRID': 'Plug-in-Hybrid',\n    'CNG': 'Erdgas',\n    'LPG': 'Autogas',\n    'HYDROGEN': 'Wasserstoff'\n  };\n  return fuelMap[mobileFuel] || 'Andere';\n}\n\nfunction mapTransmissionType(mobileTransmission: string): any {\n  const transmissionMap: Record<string, any> = {\n    'MANUAL_GEAR': 'Schaltgetriebe',\n    'AUTOMATIC_GEAR': 'Automatik',\n    'SEMI_AUTOMATIC_GEAR': 'Halbautomatik',\n    'CVT': 'CVT'\n  };\n  return transmissionMap[mobileTransmission] || 'Schaltgetriebe';\n}\n\nfunction mapBodyType(mobileCategory: string): any {\n  const bodyTypeMap: Record<string, any> = {\n    'SEDAN': 'Limousine',\n    'STATION_WAGON': 'Kombi',\n    'SUV': 'SUV',\n    'COUPE': 'Coupe',\n    'CONVERTIBLE': 'Cabrio',\n    'SMALL_CAR': 'Kleinwagen',\n    'COMPACT': 'Kompaktklasse',\n    'VAN': 'Van',\n    'PICKUP': 'Pickup',\n    'SPORTS_CAR': 'Sportwagen'\n  };\n  return bodyTypeMap[mobileCategory] || 'Andere';\n}\n\nfunction mapConditionType(mobileCondition: string): any {\n  const conditionMap: Record<string, any> = {\n    'NEW': 'Neuwagen',\n    'USED': 'Gebrauchtwagen',\n    'DEMONSTRATION': 'Vorführwagen',\n    'EMPLOYEE_CAR': 'Jahreswagen',\n    'ACCIDENT': 'Unfallfahrzeug'\n  };\n  return conditionMap[mobileCondition] || 'Gebrauchtwagen';\n}\n\n// API Service Class\nexport class MobileDeApiService {\n  private apiKey: string;\n  private sellerId: string;\n\n  constructor(apiKey: string, sellerId: string) {\n    this.apiKey = apiKey;\n    this.sellerId = sellerId;\n  }\n\n  // Fetch vehicles from mobile.de seller API\n  async fetchVehicles(): Promise<Vehicle[]> {\n    try {\n      // Note: This is a mock implementation\n      // In a real implementation, you would make actual API calls to mobile.de\n      console.log('Fetching vehicles from mobile.de API...');\n      \n      // For now, return mock data\n      // In production, replace this with actual API call:\n      // const response = await fetch(`${MOBILE_SELLER_API_URL}/vehicles`, {\n      //   headers: {\n      //     'Authorization': `Bearer ${this.apiKey}`,\n      //     'Content-Type': 'application/json'\n      //   }\n      // });\n      \n      // Mock response simulation\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      // Return mock vehicles (in production, this would be converted from API response)\n      return this.getMockVehicles();\n      \n    } catch (error) {\n      console.error('Error fetching vehicles from mobile.de:', error);\n      throw new Error('Failed to fetch vehicles from mobile.de API');\n    }\n  }\n\n  // Mock data for development\n  private getMockVehicles(): Vehicle[] {\n    // This would be replaced with actual API data conversion\n    return [\n      // Mock vehicles based on the current website data\n      // In production, this would use convertMobileDeVehicle()\n    ];\n  }\n\n  // Sync vehicles with mobile.de\n  async syncVehicles(): Promise<{ success: boolean; count: number; errors?: string[] }> {\n    try {\n      const vehicles = await this.fetchVehicles();\n      \n      // Here you would typically save the vehicles to your database\n      console.log(`Successfully synced ${vehicles.length} vehicles from mobile.de`);\n      \n      return {\n        success: true,\n        count: vehicles.length\n      };\n    } catch (error) {\n      console.error('Error syncing vehicles:', error);\n      return {\n        success: false,\n        count: 0,\n        errors: [error instanceof Error ? error.message : 'Unknown error']\n      };\n    }\n  }\n}\n\n// Export a default instance (would use environment variables in production)\nexport const mobileDeApi = new MobileDeApiService(\n  process.env.MOBILE_DE_API_KEY || 'mock-api-key',\n  process.env.MOBILE_DE_SELLER_ID || 'mock-seller-id'\n);\n"], "names": [], "mappings": ";;;;AAEA,8BAA8B;AAC9B,MAAM,sBAAsB;AAC5B,MAAM,wBAAwB;AAqE9B,gDAAgD;AAChD,SAAS,uBAAuB,aAA8B;IAC5D,MAAM,eAAe,cAAc,MAAM,CAAC,EAAE;IAE5C,OAAO;QACL,IAAI,cAAc,EAAE;QACpB,MAAM,cAAc,IAAI;QACxB,OAAO,cAAc,KAAK;QAC1B,SAAS,cAAc,OAAO;QAC9B,MAAM,IAAI,KAAK,cAAc,iBAAiB,EAAE,WAAW;QAC3D,mBAAmB,cAAc,iBAAiB;QAClD,SAAS,cAAc,OAAO;QAC9B,OAAO,cAAc,KAAK,CAAC,mBAAmB;QAC9C,UAAU,cAAc,KAAK,CAAC,iBAAiB;QAC/C,aAAa,CAAC,CAAC,cAAc,KAAK,CAAC,OAAO;QAC1C,OAAO;YACL,IAAI,cAAc,KAAK,CAAC,EAAE;YAC1B,IAAI,cAAc,KAAK,CAAC,EAAE;QAC5B;QACA,MAAM,YAAY,cAAc,IAAI;QACpC,cAAc,oBAAoB,cAAc,YAAY;QAC5D,UAAU,YAAY,cAAc,QAAQ;QAC5C,WAAW,iBAAiB,cAAc,SAAS;QACnD,QAAQ,cAAc,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,QAAU,CAAC;gBAChD,IAAI,GAAG,cAAc,EAAE,CAAC,CAAC,EAAE,OAAO;gBAClC,KAAK,IAAI,GAAG;gBACZ,KAAK,IAAI,WAAW,IAAI,GAAG,cAAc,IAAI,CAAC,CAAC,EAAE,cAAc,KAAK,EAAE;gBACtE,WAAW,UAAU;gBACrB,OAAO,QAAQ;YACjB,CAAC;QACD,UAAU,cAAc,QAAQ,IAAI,EAAE;QACtC,aAAa,cAAc,WAAW;QACtC,eAAe,cAAc,aAAa;QAC1C,eAAe,cAAc,aAAa;QAC1C,OAAO,cAAc,KAAK;QAC1B,OAAO,cAAc,KAAK;QAC1B,gBAAgB,cAAc,cAAc;QAC5C,cAAc,cAAc,WAAW;QACvC,iBAAiB,cAAc,eAAe,GAAG;YAC/C,UAAU,cAAc,eAAe,CAAC,mBAAmB;YAC3D,MAAM,cAAc,eAAe,CAAC,eAAe;YACnD,SAAS,cAAc,eAAe,CAAC,kBAAkB;QAC3D,IAAI;QACJ,uBAAuB,cAAc,qBAAqB;QAC1D,UAAU,cAAc,QAAQ,GAAG;YACjC,MAAM,cAAc,QAAQ,CAAC,IAAI;YACjC,YAAY,cAAc,QAAQ,CAAC,OAAO;YAC1C,SAAS,cAAc,QAAQ,CAAC,WAAW,KAAK,OAAO,gBAAgB,cAAc,QAAQ,CAAC,WAAW;QAC3G,IAAI;QACJ,QAAQ,cAAc,MAAM,GAAG;YAC7B,MAAM,cAAc,MAAM,CAAC,IAAI;YAC/B,SAAS;gBACP,QAAQ,cAAc,MAAM,CAAC,OAAO,CAAC,MAAM;gBAC3C,MAAM,cAAc,MAAM,CAAC,OAAO,CAAC,IAAI;gBACvC,YAAY,cAAc,MAAM,CAAC,OAAO,CAAC,OAAO;gBAChD,SAAS,cAAc,MAAM,CAAC,OAAO,CAAC,WAAW,KAAK,OAAO,gBAAgB,cAAc,MAAM,CAAC,OAAO,CAAC,WAAW;YACvH;YACA,SAAS;gBACP,OAAO,cAAc,MAAM,CAAC,KAAK;gBACjC,OAAO,cAAc,MAAM,CAAC,KAAK;YACnC;QACF,IAAI;QACJ,WAAW,cAAc,YAAY;QACrC,WAAW,cAAc,gBAAgB;QACzC,aAAa;QACb,YAAY;IACd;AACF;AAEA,uCAAuC;AACvC,SAAS,YAAY,UAAkB;IACrC,MAAM,UAA+B;QACnC,UAAU;QACV,UAAU;QACV,YAAY;QACZ,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,OAAO;QACP,OAAO;QACP,YAAY;IACd;IACA,OAAO,OAAO,CAAC,WAAW,IAAI;AAChC;AAEA,SAAS,oBAAoB,kBAA0B;IACrD,MAAM,kBAAuC;QAC3C,eAAe;QACf,kBAAkB;QAClB,uBAAuB;QACvB,OAAO;IACT;IACA,OAAO,eAAe,CAAC,mBAAmB,IAAI;AAChD;AAEA,SAAS,YAAY,cAAsB;IACzC,MAAM,cAAmC;QACvC,SAAS;QACT,iBAAiB;QACjB,OAAO;QACP,SAAS;QACT,eAAe;QACf,aAAa;QACb,WAAW;QACX,OAAO;QACP,UAAU;QACV,cAAc;IAChB;IACA,OAAO,WAAW,CAAC,eAAe,IAAI;AACxC;AAEA,SAAS,iBAAiB,eAAuB;IAC/C,MAAM,eAAoC;QACxC,OAAO;QACP,QAAQ;QACR,iBAAiB;QACjB,gBAAgB;QAChB,YAAY;IACd;IACA,OAAO,YAAY,CAAC,gBAAgB,IAAI;AAC1C;AAGO,MAAM;IACH,OAAe;IACf,SAAiB;IAEzB,YAAY,MAAc,EAAE,QAAgB,CAAE;QAC5C,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,QAAQ,GAAG;IAClB;IAEA,2CAA2C;IAC3C,MAAM,gBAAoC;QACxC,IAAI;YACF,sCAAsC;YACtC,yEAAyE;YACzE,QAAQ,GAAG,CAAC;YAEZ,4BAA4B;YAC5B,oDAAoD;YACpD,sEAAsE;YACtE,eAAe;YACf,gDAAgD;YAChD,yCAAyC;YACzC,MAAM;YACN,MAAM;YAEN,2BAA2B;YAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,kFAAkF;YAClF,OAAO,IAAI,CAAC,eAAe;QAE7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,4BAA4B;IACpB,kBAA6B;QACnC,yDAAyD;QACzD,OAAO,EAGN;IACH;IAEA,+BAA+B;IAC/B,MAAM,eAAgF;QACpF,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,aAAa;YAEzC,8DAA8D;YAC9D,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,wBAAwB,CAAC;YAE5E,OAAO;gBACL,SAAS;gBACT,OAAO,SAAS,MAAM;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,QAAQ;oBAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG;iBAAgB;YACpE;QACF;IACF;AACF;AAGO,MAAM,cAAc,IAAI,mBAC7B,QAAQ,GAAG,CAAC,iBAAiB,IAAI,gBACjC,QAAQ,GAAG,CAAC,mBAAmB,IAAI", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/automobilenordneu/automobile-nord-neu/src/app/api/sync-vehicles/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { mobileDeApi } from '@/lib/mobile-api';\n\nexport async function POST(request: NextRequest) {\n  try {\n    // In production, you might want to add authentication here\n    // const authHeader = request.headers.get('authorization');\n    // if (!authHeader || !isValid<PERSON><PERSON><PERSON>(authHeader)) {\n    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    // }\n\n    console.log('Starting vehicle sync from mobile.de...');\n    \n    const result = await mobileDeApi.syncVehicles();\n    \n    if (result.success) {\n      return NextResponse.json({\n        success: true,\n        message: `Successfully synced ${result.count} vehicles from mobile.de`,\n        count: result.count,\n        timestamp: new Date().toISOString()\n      });\n    } else {\n      return NextResponse.json({\n        success: false,\n        message: 'Failed to sync vehicles',\n        errors: result.errors,\n        timestamp: new Date().toISOString()\n      }, { status: 500 });\n    }\n  } catch (error) {\n    console.error('Error in sync-vehicles API:', error);\n    \n    return NextResponse.json({\n      success: false,\n      message: 'Internal server error during vehicle sync',\n      error: error instanceof Error ? error.message : 'Unknown error',\n      timestamp: new Date().toISOString()\n    }, { status: 500 });\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    // Get sync status or last sync information\n    return NextResponse.json({\n      status: 'ready',\n      message: 'Vehicle sync API is ready',\n      lastSync: null, // In production, this would come from database\n      endpoint: '/api/sync-vehicles',\n      method: 'POST',\n      description: 'Synchronizes vehicles from mobile.de API'\n    });\n  } catch (error) {\n    return NextResponse.json({\n      error: 'Failed to get sync status',\n      message: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,2DAA2D;QAC3D,2DAA2D;QAC3D,mDAAmD;QACnD,0EAA0E;QAC1E,IAAI;QAEJ,QAAQ,GAAG,CAAC;QAEZ,MAAM,SAAS,MAAM,6HAAA,CAAA,cAAW,CAAC,YAAY;QAE7C,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS,CAAC,oBAAoB,EAAE,OAAO,KAAK,CAAC,wBAAwB,CAAC;gBACtE,OAAO,OAAO,KAAK;gBACnB,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,OAAO;YACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;gBACT,QAAQ,OAAO,MAAM;gBACrB,WAAW,IAAI,OAAO,WAAW;YACnC,GAAG;gBAAE,QAAQ;YAAI;QACnB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAE7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,WAAW,IAAI,OAAO,WAAW;QACnC,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,2CAA2C;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ;YACR,SAAS;YACT,UAAU;YACV,UAAU;YACV,QAAQ;YACR,aAAa;QACf;IACF,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}