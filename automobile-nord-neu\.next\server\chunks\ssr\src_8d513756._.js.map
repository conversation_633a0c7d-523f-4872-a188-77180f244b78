{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/automobilenordneu/automobile-nord-neu/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-2xl border border-secondary-200 bg-white shadow-soft transition-all duration-200 hover:shadow-medium\",\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = \"Card\";\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n));\nCardHeader.displayName = \"CardHeader\";\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-xl font-semibold leading-none tracking-tight text-secondary-900\",\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = \"CardTitle\";\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-secondary-600\", className)}\n    {...props}\n  />\n));\nCardDescription.displayName = \"CardDescription\";\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n));\nCardContent.displayName = \"CardContent\";\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n));\nCardFooter.displayName = \"CardFooter\";\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,qMAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gHACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/automobilenordneu/automobile-nord-neu/src/lib/mock-data.ts"], "sourcesContent": ["import { Vehicle } from '@/types/vehicle';\n\nexport const mockVehicles: Vehicle[] = [\n  {\n    id: '1',\n    make: 'Audi',\n    model: 'R8',\n    variant: 'Spyder 5.2 FSI Quattro Performance Carbon',\n    year: 2024,\n    firstRegistration: '2024-05-01',\n    mileage: 499,\n    price: 216000,\n    netPrice: 181513,\n    vatIncluded: true,\n    power: {\n      kw: 456,\n      ps: 620\n    },\n    fuel: 'Benzin',\n    transmission: 'Automatik',\n    bodyType: 'Cabrio',\n    condition: 'Gebrauchtwagen',\n    images: [\n      {\n        id: '1',\n        url: 'https://img.classistatic.de/api/v1/mo-prod/images/01/01292fa3-86c7-4894-a5b5-e5175f74d8a8?rule=mo-640.jpg',\n        alt: 'Audi R8 Spyder 5.2 FSI Quattro Performance Carbon',\n        isPrimary: true,\n        order: 1\n      }\n    ],\n    features: ['Carbon-Paket', 'Performance', 'Quattro Allrad'],\n    exteriorColor: '<PERSON>hwarz',\n    doors: 2,\n    seats: 2,\n    co2Emissions: 297,\n    fuelConsumption: {\n      combined: 13.0\n    },\n    energyEfficiencyClass: 'E',\n    location: {\n      city: 'Harrislee',\n      postalCode: '24955',\n      country: 'Deutschland'\n    },\n    dealer: {\n      name: 'Automobile Nord GmbH',\n      address: {\n        street: 'Grönfahrtweg 22',\n        city: 'Harrislee',\n        postalCode: '24955',\n        country: 'Deutschland'\n      },\n      contact: {\n        phone: '0461-66353453',\n        email: '<EMAIL>'\n      }\n    },\n    createdAt: '2024-01-01T00:00:00Z',\n    updatedAt: '2024-01-01T00:00:00Z',\n    isAvailable: true,\n    isFeatured: true\n  },\n  {\n    id: '2',\n    make: 'Mercedes-Benz',\n    model: 'GLE 63 S AMG',\n    variant: '4Matic+ Coupe Brabus 700',\n    year: 2023,\n    firstRegistration: '2023-12-01',\n    mileage: 7852,\n    price: 176000,\n    netPrice: 147899,\n    vatIncluded: true,\n    power: {\n      kw: 450,\n      ps: 612\n    },\n    fuel: 'Benzin',\n    transmission: 'Automatik',\n    bodyType: 'SUV',\n    condition: 'Gebrauchtwagen',\n    images: [\n      {\n        id: '2',\n        url: 'https://img.classistatic.de/api/v1/mo-prod/images/14/149a897a-22b0-4770-8905-4fb1d8c64726?rule=mo-640.jpg',\n        alt: 'Mercedes-Benz GLE 63 S AMG 4Matic+ Coupe Brabus 700',\n        isPrimary: true,\n        order: 1\n      }\n    ],\n    features: ['Brabus Tuning', 'AMG Performance', '4Matic Allrad'],\n    exteriorColor: 'Grau',\n    doors: 5,\n    seats: 5,\n    location: {\n      city: 'Harrislee',\n      postalCode: '24955',\n      country: 'Deutschland'\n    },\n    dealer: {\n      name: 'Automobile Nord GmbH',\n      address: {\n        street: 'Grönfahrtweg 22',\n        city: 'Harrislee',\n        postalCode: '24955',\n        country: 'Deutschland'\n      },\n      contact: {\n        phone: '0461-66353453',\n        email: '<EMAIL>'\n      }\n    },\n    createdAt: '2024-01-01T00:00:00Z',\n    updatedAt: '2024-01-01T00:00:00Z',\n    isAvailable: true,\n    isFeatured: true\n  },\n  {\n    id: '3',\n    make: 'Porsche',\n    model: 'Cayenne',\n    variant: 'Coupe GTS*Pano*Head Up*Soft Close',\n    year: 2025,\n    firstRegistration: '2025-03-01',\n    mileage: 4647,\n    price: 139500,\n    netPrice: 117227,\n    vatIncluded: true,\n    power: {\n      kw: 368,\n      ps: 500\n    },\n    fuel: 'Benzin',\n    transmission: 'Automatik',\n    bodyType: 'SUV',\n    condition: 'Gebrauchtwagen',\n    images: [\n      {\n        id: '3',\n        url: 'https://img.classistatic.de/api/v1/mo-prod/images/63/6339fcba-efe9-4ebf-aa3d-2ce424302fc2?rule=mo-640.jpg',\n        alt: 'Porsche Cayenne Coupe GTS*Pano*Head Up*Soft Close',\n        isPrimary: true,\n        order: 1\n      }\n    ],\n    features: ['Panoramadach', 'Head-Up Display', 'Soft Close'],\n    exteriorColor: 'Weiß',\n    doors: 5,\n    seats: 5,\n    location: {\n      city: 'Harrislee',\n      postalCode: '24955',\n      country: 'Deutschland'\n    },\n    dealer: {\n      name: 'Automobile Nord GmbH',\n      address: {\n        street: 'Grönfahrtweg 22',\n        city: 'Harrislee',\n        postalCode: '24955',\n        country: 'Deutschland'\n      },\n      contact: {\n        phone: '0461-66353453',\n        email: '<EMAIL>'\n      }\n    },\n    createdAt: '2024-01-01T00:00:00Z',\n    updatedAt: '2024-01-01T00:00:00Z',\n    isAvailable: true\n  },\n  {\n    id: '4',\n    make: 'Audi',\n    model: 'Q7',\n    variant: '50 TDI Quattro S-line Plus*Pano*Head Up*360',\n    year: 2025,\n    firstRegistration: '2025-03-01',\n    mileage: 4985,\n    price: 89250,\n    netPrice: 75000,\n    vatIncluded: true,\n    power: {\n      kw: 210,\n      ps: 286\n    },\n    fuel: 'Diesel',\n    transmission: 'Automatik',\n    bodyType: 'SUV',\n    condition: 'Gebrauchtwagen',\n    images: [\n      {\n        id: '4',\n        url: 'https://img.classistatic.de/api/v1/mo-prod/images/c5/c5a8c2b8-c2f3-4cce-be80-8b85c264d817?rule=mo-640.jpg',\n        alt: 'Audi Q7 50 TDI Quattro S-line Plus*Pano*Head Up*360',\n        isPrimary: true,\n        order: 1\n      }\n    ],\n    features: ['S-Line', 'Panoramadach', 'Head-Up Display', '360° Kamera'],\n    exteriorColor: 'Schwarz',\n    doors: 5,\n    seats: 7,\n    location: {\n      city: 'Harrislee',\n      postalCode: '24955',\n      country: 'Deutschland'\n    },\n    dealer: {\n      name: 'Automobile Nord GmbH',\n      address: {\n        street: 'Grönfahrtweg 22',\n        city: 'Harrislee',\n        postalCode: '24955',\n        country: 'Deutschland'\n      },\n      contact: {\n        phone: '0461-66353453',\n        email: '<EMAIL>'\n      }\n    },\n    createdAt: '2024-01-01T00:00:00Z',\n    updatedAt: '2024-01-01T00:00:00Z',\n    isAvailable: true,\n    priceRating: 'guter Preis'\n  },\n  {\n    id: '5',\n    make: 'SKODA',\n    model: 'Kodiaq',\n    variant: '2.0 TDI 193 PS 4x4 Sport*Head Up*7 Sitzer',\n    year: 2024,\n    firstRegistration: '2024-12-01',\n    mileage: 11367,\n    price: 45650,\n    netPrice: 38361,\n    vatIncluded: true,\n    power: {\n      kw: 142,\n      ps: 193\n    },\n    fuel: 'Diesel',\n    transmission: 'Automatik',\n    bodyType: 'SUV',\n    condition: 'Gebrauchtwagen',\n    images: [\n      {\n        id: '5',\n        url: 'https://img.classistatic.de/api/v1/mo-prod/images/11/1128fbbf-da4b-4cfa-a1cf-2e313e2234a7?rule=mo-640.jpg',\n        alt: 'SKODA Kodiaq 2.0 TDI 193 PS 4x4 Sport*Head Up*7 Sitzer',\n        isPrimary: true,\n        order: 1\n      }\n    ],\n    features: ['4x4 Allrad', 'Head-Up Display', '7 Sitzer', 'Sport-Paket'],\n    exteriorColor: 'Grau',\n    doors: 5,\n    seats: 7,\n    location: {\n      city: 'Harrislee',\n      postalCode: '24955',\n      country: 'Deutschland'\n    },\n    dealer: {\n      name: 'Automobile Nord GmbH',\n      address: {\n        street: 'Grönfahrtweg 22',\n        city: 'Harrislee',\n        postalCode: '24955',\n        country: 'Deutschland'\n      },\n      contact: {\n        phone: '0461-66353453',\n        email: '<EMAIL>'\n      }\n    },\n    createdAt: '2024-01-01T00:00:00Z',\n    updatedAt: '2024-01-01T00:00:00Z',\n    isAvailable: true,\n    priceRating: 'guter Preis'\n  }\n];\n"], "names": [], "mappings": ";;;AAEO,MAAM,eAA0B;IACrC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,MAAM;QACN,mBAAmB;QACnB,SAAS;QACT,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;YACL,IAAI;YACJ,IAAI;QACN;QACA,MAAM;QACN,cAAc;QACd,UAAU;QACV,WAAW;QACX,QAAQ;YACN;gBACE,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,WAAW;gBACX,OAAO;YACT;SACD;QACD,UAAU;YAAC;YAAgB;YAAe;SAAiB;QAC3D,eAAe;QACf,OAAO;QACP,OAAO;QACP,cAAc;QACd,iBAAiB;YACf,UAAU;QACZ;QACA,uBAAuB;QACvB,UAAU;YACR,MAAM;YACN,YAAY;YACZ,SAAS;QACX;QACA,QAAQ;YACN,MAAM;YACN,SAAS;gBACP,QAAQ;gBACR,MAAM;gBACN,YAAY;gBACZ,SAAS;YACX;YACA,SAAS;gBACP,OAAO;gBACP,OAAO;YACT;QACF;QACA,WAAW;QACX,WAAW;QACX,aAAa;QACb,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,MAAM;QACN,mBAAmB;QACnB,SAAS;QACT,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;YACL,IAAI;YACJ,IAAI;QACN;QACA,MAAM;QACN,cAAc;QACd,UAAU;QACV,WAAW;QACX,QAAQ;YACN;gBACE,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,WAAW;gBACX,OAAO;YACT;SACD;QACD,UAAU;YAAC;YAAiB;YAAmB;SAAgB;QAC/D,eAAe;QACf,OAAO;QACP,OAAO;QACP,UAAU;YACR,MAAM;YACN,YAAY;YACZ,SAAS;QACX;QACA,QAAQ;YACN,MAAM;YACN,SAAS;gBACP,QAAQ;gBACR,MAAM;gBACN,YAAY;gBACZ,SAAS;YACX;YACA,SAAS;gBACP,OAAO;gBACP,OAAO;YACT;QACF;QACA,WAAW;QACX,WAAW;QACX,aAAa;QACb,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,MAAM;QACN,mBAAmB;QACnB,SAAS;QACT,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;YACL,IAAI;YACJ,IAAI;QACN;QACA,MAAM;QACN,cAAc;QACd,UAAU;QACV,WAAW;QACX,QAAQ;YACN;gBACE,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,WAAW;gBACX,OAAO;YACT;SACD;QACD,UAAU;YAAC;YAAgB;YAAmB;SAAa;QAC3D,eAAe;QACf,OAAO;QACP,OAAO;QACP,UAAU;YACR,MAAM;YACN,YAAY;YACZ,SAAS;QACX;QACA,QAAQ;YACN,MAAM;YACN,SAAS;gBACP,QAAQ;gBACR,MAAM;gBACN,YAAY;gBACZ,SAAS;YACX;YACA,SAAS;gBACP,OAAO;gBACP,OAAO;YACT;QACF;QACA,WAAW;QACX,WAAW;QACX,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,MAAM;QACN,mBAAmB;QACnB,SAAS;QACT,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;YACL,IAAI;YACJ,IAAI;QACN;QACA,MAAM;QACN,cAAc;QACd,UAAU;QACV,WAAW;QACX,QAAQ;YACN;gBACE,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,WAAW;gBACX,OAAO;YACT;SACD;QACD,UAAU;YAAC;YAAU;YAAgB;YAAmB;SAAc;QACtE,eAAe;QACf,OAAO;QACP,OAAO;QACP,UAAU;YACR,MAAM;YACN,YAAY;YACZ,SAAS;QACX;QACA,QAAQ;YACN,MAAM;YACN,SAAS;gBACP,QAAQ;gBACR,MAAM;gBACN,YAAY;gBACZ,SAAS;YACX;YACA,SAAS;gBACP,OAAO;gBACP,OAAO;YACT;QACF;QACA,WAAW;QACX,WAAW;QACX,aAAa;QACb,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,MAAM;QACN,mBAAmB;QACnB,SAAS;QACT,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;YACL,IAAI;YACJ,IAAI;QACN;QACA,MAAM;QACN,cAAc;QACd,UAAU;QACV,WAAW;QACX,QAAQ;YACN;gBACE,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,WAAW;gBACX,OAAO;YACT;SACD;QACD,UAAU;YAAC;YAAc;YAAmB;YAAY;SAAc;QACtE,eAAe;QACf,OAAO;QACP,OAAO;QACP,UAAU;YACR,MAAM;YACN,YAAY;YACZ,SAAS;QACX;QACA,QAAQ;YACN,MAAM;YACN,SAAS;gBACP,QAAQ;gBACR,MAAM;gBACN,YAAY;gBACZ,SAAS;YACX;YACA,SAAS;gBACP,OAAO;gBACP,OAAO;YACT;QACF;QACA,WAAW;QACX,WAAW;QACX,aAAa;QACb,aAAa;IACf;CACD", "debugId": null}}, {"offset": {"line": 402, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/automobilenordneu/automobile-nord-neu/src/app/fahrzeuge/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useMemo } from 'react';\nimport { motion } from 'framer-motion';\nimport { Search, Filter, Grid, List, SlidersHorizontal } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport VehicleCard from '@/components/ui/vehicle-card';\nimport { mockVehicles } from '@/lib/mock-data';\nimport { Vehicle, VehicleFilter } from '@/types/vehicle';\n\nconst FahrzeugePage = () => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');\n  const [showFilters, setShowFilters] = useState(false);\n  const [filters, setFilters] = useState<VehicleFilter>({});\n\n  // Filter vehicles based on search and filters\n  const filteredVehicles = useMemo(() => {\n    let filtered = mockVehicles;\n\n    // Search filter\n    if (searchQuery) {\n      filtered = filtered.filter(vehicle =>\n        `${vehicle.make} ${vehicle.model} ${vehicle.variant || ''}`\n          .toLowerCase()\n          .includes(searchQuery.toLowerCase())\n      );\n    }\n\n    // Make filter\n    if (filters.make && filters.make.length > 0) {\n      filtered = filtered.filter(vehicle => filters.make!.includes(vehicle.make));\n    }\n\n    // Price filter\n    if (filters.priceMin) {\n      filtered = filtered.filter(vehicle => vehicle.price >= filters.priceMin!);\n    }\n    if (filters.priceMax) {\n      filtered = filtered.filter(vehicle => vehicle.price <= filters.priceMax!);\n    }\n\n    // Year filter\n    if (filters.yearMin) {\n      filtered = filtered.filter(vehicle => vehicle.year >= filters.yearMin!);\n    }\n    if (filters.yearMax) {\n      filtered = filtered.filter(vehicle => vehicle.year <= filters.yearMax!);\n    }\n\n    // Fuel filter\n    if (filters.fuel && filters.fuel.length > 0) {\n      filtered = filtered.filter(vehicle => filters.fuel!.includes(vehicle.fuel));\n    }\n\n    return filtered;\n  }, [searchQuery, filters]);\n\n  // Get unique makes for filter\n  const uniqueMakes = Array.from(new Set(mockVehicles.map(v => v.make))).sort();\n  const uniqueFuelTypes = Array.from(new Set(mockVehicles.map(v => v.fuel))).sort();\n\n  const handleMakeFilter = (make: string) => {\n    setFilters(prev => ({\n      ...prev,\n      make: prev.make?.includes(make)\n        ? prev.make.filter(m => m !== make)\n        : [...(prev.make || []), make]\n    }));\n  };\n\n  const handleFuelFilter = (fuel: string) => {\n    setFilters(prev => ({\n      ...prev,\n      fuel: prev.fuel?.includes(fuel as any)\n        ? prev.fuel.filter(f => f !== fuel)\n        : [...(prev.fuel || []), fuel as any]\n    }));\n  };\n\n  const clearFilters = () => {\n    setFilters({});\n    setSearchQuery('');\n  };\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Header */}\n      <section className=\"relative py-24 overflow-hidden\">\n        <div className=\"absolute inset-0 gradient-hero\"></div>\n        <div className=\"absolute inset-0\">\n          <div className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-violet-500/20 rounded-full blur-3xl animate-float\"></div>\n          <div className=\"absolute bottom-1/4 right-1/4 w-96 h-96 bg-cyan-500/20 rounded-full blur-3xl animate-float\" style={{ animationDelay: '2s' }}></div>\n        </div>\n\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"text-center space-y-6\"\n          >\n            <h1 className=\"text-4xl lg:text-6xl font-black text-white\">\n              Premium{' '}\n              <span className=\"bg-gradient-to-r from-violet-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent text-glow\">\n                Fahrzeuge\n              </span>\n            </h1>\n            <p className=\"text-xl lg:text-2xl text-white/80 max-w-4xl mx-auto font-light\">\n              Entdecken Sie unsere exklusive Auswahl an Premium-Fahrzeugen.\n              Aktuell haben wir {mockVehicles.length} außergewöhnliche Fahrzeuge für Sie im Angebot.\n            </p>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Search and Filter Bar */}\n      <section className=\"relative py-12\">\n        <div className=\"absolute inset-0 bg-slate-900/80\"></div>\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n          <div className=\"flex flex-col lg:flex-row gap-6 items-center justify-between\">\n            {/* Search */}\n            <div className=\"relative flex-1 max-w-md\">\n              <Search className=\"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60\" />\n              <input\n                type=\"text\"\n                placeholder=\"Marke, Modell oder Variante suchen...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-12 pr-4 py-4 glass-dark border border-white/20 rounded-2xl focus:ring-2 focus:ring-violet-500 focus:border-violet-500 text-white placeholder-white/60 font-medium\"\n              />\n            </div>\n\n            {/* Controls */}\n            <div className=\"flex items-center space-x-4\">\n              <Button\n                variant=\"outline\"\n                onClick={() => setShowFilters(!showFilters)}\n                className=\"flex items-center space-x-2\"\n              >\n                <SlidersHorizontal className=\"h-4 w-4\" />\n                <span>Filter</span>\n              </Button>\n\n              <div className=\"flex items-center space-x-2 border border-secondary-300 rounded-lg p-1\">\n                <Button\n                  variant={viewMode === 'grid' ? 'default' : 'ghost'}\n                  size=\"sm\"\n                  onClick={() => setViewMode('grid')}\n                >\n                  <Grid className=\"h-4 w-4\" />\n                </Button>\n                <Button\n                  variant={viewMode === 'list' ? 'default' : 'ghost'}\n                  size=\"sm\"\n                  onClick={() => setViewMode('list')}\n                >\n                  <List className=\"h-4 w-4\" />\n                </Button>\n              </div>\n\n              <div className=\"text-sm text-secondary-600\">\n                {filteredVehicles.length} von {mockVehicles.length} Fahrzeugen\n              </div>\n            </div>\n          </div>\n\n          {/* Filter Panel */}\n          {showFilters && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              transition={{ duration: 0.3 }}\n              className=\"mt-6 p-6 bg-secondary-50 rounded-xl\"\n            >\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                {/* Make Filter */}\n                <div>\n                  <h3 className=\"font-medium text-secondary-900 mb-3\">Marke</h3>\n                  <div className=\"space-y-2\">\n                    {uniqueMakes.map(make => (\n                      <label key={make} className=\"flex items-center space-x-2\">\n                        <input\n                          type=\"checkbox\"\n                          checked={filters.make?.includes(make) || false}\n                          onChange={() => handleMakeFilter(make)}\n                          className=\"rounded border-secondary-300 text-primary-600 focus:ring-primary-500\"\n                        />\n                        <span className=\"text-sm text-secondary-700\">{make}</span>\n                      </label>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Fuel Filter */}\n                <div>\n                  <h3 className=\"font-medium text-secondary-900 mb-3\">Kraftstoff</h3>\n                  <div className=\"space-y-2\">\n                    {uniqueFuelTypes.map(fuel => (\n                      <label key={fuel} className=\"flex items-center space-x-2\">\n                        <input\n                          type=\"checkbox\"\n                          checked={filters.fuel?.includes(fuel as any) || false}\n                          onChange={() => handleFuelFilter(fuel)}\n                          className=\"rounded border-secondary-300 text-primary-600 focus:ring-primary-500\"\n                        />\n                        <span className=\"text-sm text-secondary-700\">{fuel}</span>\n                      </label>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Price Filter */}\n                <div>\n                  <h3 className=\"font-medium text-secondary-900 mb-3\">Preis</h3>\n                  <div className=\"space-y-3\">\n                    <div>\n                      <label className=\"block text-sm text-secondary-700 mb-1\">Von (€)</label>\n                      <input\n                        type=\"number\"\n                        placeholder=\"Min. Preis\"\n                        value={filters.priceMin || ''}\n                        onChange={(e) => setFilters(prev => ({ ...prev, priceMin: Number(e.target.value) || undefined }))}\n                        className=\"w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm text-secondary-700 mb-1\">Bis (€)</label>\n                      <input\n                        type=\"number\"\n                        placeholder=\"Max. Preis\"\n                        value={filters.priceMax || ''}\n                        onChange={(e) => setFilters(prev => ({ ...prev, priceMax: Number(e.target.value) || undefined }))}\n                        className=\"w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                      />\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"mt-6 flex justify-end\">\n                <Button variant=\"outline\" onClick={clearFilters}>\n                  Filter zurücksetzen\n                </Button>\n              </div>\n            </motion.div>\n          )}\n        </div>\n      </section>\n\n      {/* Vehicle Grid */}\n      <section className=\"py-12\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          {filteredVehicles.length > 0 ? (\n            <div className={`grid gap-8 ${\n              viewMode === 'grid' \n                ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' \n                : 'grid-cols-1'\n            }`}>\n              {filteredVehicles.map((vehicle, index) => (\n                <motion.div\n                  key={vehicle.id}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\n                >\n                  <VehicleCard vehicle={vehicle} />\n                </motion.div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-12\">\n              <div className=\"w-24 h-24 bg-secondary-100 rounded-2xl flex items-center justify-center mx-auto mb-4\">\n                <Search className=\"h-12 w-12 text-secondary-400\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-secondary-900 mb-2\">\n                Keine Fahrzeuge gefunden\n              </h3>\n              <p className=\"text-secondary-600 mb-6\">\n                Versuchen Sie es mit anderen Suchbegriffen oder passen Sie Ihre Filter an.\n              </p>\n              <Button onClick={clearFilters}>\n                Filter zurücksetzen\n              </Button>\n            </div>\n          )}\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default FahrzeugePage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AARA;;;;;;;;AAWA,MAAM,gBAAgB;IACpB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,CAAC;IAEvD,8CAA8C;IAC9C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,IAAI,WAAW,0HAAA,CAAA,eAAY;QAE3B,gBAAgB;QAChB,IAAI,aAAa;YACf,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,KAAK,CAAC,CAAC,EAAE,QAAQ,OAAO,IAAI,IAAI,CACxD,WAAW,GACX,QAAQ,CAAC,YAAY,WAAW;QAEvC;QAEA,cAAc;QACd,IAAI,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,GAAG;YAC3C,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAE,QAAQ,CAAC,QAAQ,IAAI;QAC3E;QAEA,eAAe;QACf,IAAI,QAAQ,QAAQ,EAAE;YACpB,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,IAAI,QAAQ,QAAQ;QACzE;QACA,IAAI,QAAQ,QAAQ,EAAE;YACpB,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,IAAI,QAAQ,QAAQ;QACzE;QAEA,cAAc;QACd,IAAI,QAAQ,OAAO,EAAE;YACnB,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,IAAI,IAAI,QAAQ,OAAO;QACvE;QACA,IAAI,QAAQ,OAAO,EAAE;YACnB,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,IAAI,IAAI,QAAQ,OAAO;QACvE;QAEA,cAAc;QACd,IAAI,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,GAAG;YAC3C,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAE,QAAQ,CAAC,QAAQ,IAAI;QAC3E;QAEA,OAAO;IACT,GAAG;QAAC;QAAa;KAAQ;IAEzB,8BAA8B;IAC9B,MAAM,cAAc,MAAM,IAAI,CAAC,IAAI,IAAI,0HAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,IAAI;IAC3E,MAAM,kBAAkB,MAAM,IAAI,CAAC,IAAI,IAAI,0HAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,IAAI;IAE/E,MAAM,mBAAmB,CAAC;QACxB,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,MAAM,KAAK,IAAI,EAAE,SAAS,QACtB,KAAK,IAAI,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM,QAC5B;uBAAK,KAAK,IAAI,IAAI,EAAE;oBAAG;iBAAK;YAClC,CAAC;IACH;IAEA,MAAM,mBAAmB,CAAC;QACxB,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,MAAM,KAAK,IAAI,EAAE,SAAS,QACtB,KAAK,IAAI,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM,QAC5B;uBAAK,KAAK,IAAI,IAAI,EAAE;oBAAG;iBAAY;YACzC,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,WAAW,CAAC;QACZ,eAAe;IACjB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;gCAA6F,OAAO;oCAAE,gBAAgB;gCAAK;;;;;;;;;;;;kCAG5I,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;;wCAA6C;wCACjD;sDACR,8OAAC;4CAAK,WAAU;sDAAsG;;;;;;;;;;;;8CAIxH,8OAAC;oCAAE,WAAU;;wCAAiE;wCAEzD,0HAAA,CAAA,eAAY,CAAC,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;0BAO/C,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;kDAKd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS,IAAM,eAAe,CAAC;gDAC/B,WAAU;;kEAEV,8OAAC,gOAAA,CAAA,oBAAiB;wDAAC,WAAU;;;;;;kEAC7B,8OAAC;kEAAK;;;;;;;;;;;;0DAGR,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAS,aAAa,SAAS,YAAY;wDAC3C,MAAK;wDACL,SAAS,IAAM,YAAY;kEAE3B,cAAA,8OAAC,yMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAS,aAAa,SAAS,YAAY;wDAC3C,MAAK;wDACL,SAAS,IAAM,YAAY;kEAE3B,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAIpB,8OAAC;gDAAI,WAAU;;oDACZ,iBAAiB,MAAM;oDAAC;oDAAM,0HAAA,CAAA,eAAY,CAAC,MAAM;oDAAC;;;;;;;;;;;;;;;;;;;4BAMxD,6BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,QAAQ;gCAAE;gCACjC,SAAS;oCAAE,SAAS;oCAAG,QAAQ;gCAAO;gCACtC,MAAM;oCAAE,SAAS;oCAAG,QAAQ;gCAAE;gCAC9B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,8OAAC;wDAAI,WAAU;kEACZ,YAAY,GAAG,CAAC,CAAA,qBACf,8OAAC;gEAAiB,WAAU;;kFAC1B,8OAAC;wEACC,MAAK;wEACL,SAAS,QAAQ,IAAI,EAAE,SAAS,SAAS;wEACzC,UAAU,IAAM,iBAAiB;wEACjC,WAAU;;;;;;kFAEZ,8OAAC;wEAAK,WAAU;kFAA8B;;;;;;;+DAPpC;;;;;;;;;;;;;;;;0DAclB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,8OAAC;wDAAI,WAAU;kEACZ,gBAAgB,GAAG,CAAC,CAAA,qBACnB,8OAAC;gEAAiB,WAAU;;kFAC1B,8OAAC;wEACC,MAAK;wEACL,SAAS,QAAQ,IAAI,EAAE,SAAS,SAAgB;wEAChD,UAAU,IAAM,iBAAiB;wEACjC,WAAU;;;;;;kFAEZ,8OAAC;wEAAK,WAAU;kFAA8B;;;;;;;+DAPpC;;;;;;;;;;;;;;;;0DAclB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAAwC;;;;;;kFACzD,8OAAC;wEACC,MAAK;wEACL,aAAY;wEACZ,OAAO,QAAQ,QAAQ,IAAI;wEAC3B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;oFAAE,GAAG,IAAI;oFAAE,UAAU,OAAO,EAAE,MAAM,CAAC,KAAK,KAAK;gFAAU,CAAC;wEAC/F,WAAU;;;;;;;;;;;;0EAGd,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAAwC;;;;;;kFACzD,8OAAC;wEACC,MAAK;wEACL,aAAY;wEACZ,OAAO,QAAQ,QAAQ,IAAI;wEAC3B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;oFAAE,GAAG,IAAI;oFAAE,UAAU,OAAO,EAAE,MAAM,CAAC,KAAK,KAAK;gFAAU,CAAC;wEAC/F,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOpB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS;sDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU3D,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,MAAM,GAAG,kBACzB,8OAAC;wBAAI,WAAW,CAAC,WAAW,EAC1B,aAAa,SACT,8CACA,eACJ;kCACC,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;0CAEhD,cAAA,8OAAC,2IAAA,CAAA,UAAW;oCAAC,SAAS;;;;;;+BALjB,QAAQ,EAAE;;;;;;;;;iFAUrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAEpB,8OAAC;gCAAG,WAAU;0CAAgD;;;;;;0CAG9D,8OAAC;gCAAE,WAAU;0CAA0B;;;;;;0CAGvC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS;0CAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7C;uCAEe", "debugId": null}}]}