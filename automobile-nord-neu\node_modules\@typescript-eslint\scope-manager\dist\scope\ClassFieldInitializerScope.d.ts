import type { TSESTree } from '@typescript-eslint/types';
import type { ScopeManager } from '../ScopeManager';
import type { Scope } from './Scope';
import { ScopeBase } from './ScopeBase';
import { ScopeType } from './ScopeType';
export declare class ClassFieldInitializerScope extends ScopeBase<ScopeType.classFieldInitializer, TSESTree.Expression, Scope> {
    constructor(scopeManager: ScopeManager, upperScope: ClassFieldInitializerScope['upper'], block: ClassFieldInitializerScope['block']);
}
//# sourceMappingURL=ClassFieldInitializerScope.d.ts.map