{"version": 3, "file": "ScopeBase.d.ts", "sourceRoot": "", "sources": ["../../src/scope/ScopeBase.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AAIzD,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAChD,OAAO,KAAK,EAAE,uBAAuB,EAAE,MAAM,yBAAyB,CAAC;AACvE,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AACpD,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AACrD,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AACjD,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AACjD,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AACrC,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAKrD,OAAO,EACL,SAAS,EACT,aAAa,EAEd,MAAM,yBAAyB,CAAC;AACjC,OAAO,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAC;AACvC,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAuGxC,KAAK,aAAa,GAAG,aAAa,GAAG,WAAW,GAAG,WAAW,GAAG,aAAa,CAAC;AAW/E,8BAAsB,SAAS,CAC7B,IAAI,SAAS,SAAS,EACtB,KAAK,SAAS,QAAQ,CAAC,IAAI,EAC3B,KAAK,SAAS,KAAK,GAAG,IAAI;;IAE1B;;OAEG;IACH,SAAgB,GAAG,EAAE,MAAM,CAAe;IAE1C;;;OAGG;IACH,SAAgB,KAAK,EAAE,KAAK,CAAC;IAC7B;;;OAGG;IACH,SAAgB,WAAW,EAAE,KAAK,EAAE,CAAM;IAa1C;;;OAGG;IACH,SAAgB,uBAAuB,EAAE,OAAO,CAAS;IACzD;;;OAGG;IACI,QAAQ,EAAE,OAAO,CAAC;IACzB;;;OAGG;IACH,SAAS,CAAC,aAAa,EAAE,SAAS,EAAE,GAAG,IAAI,CAAM;IACjD;;;;;;OAMG;IACH,SAAgB,UAAU,EAAE,SAAS,EAAE,CAAM;IAC7C;;;OAGG;IACH,SAAgB,GAAG,wBAA+B;IAClD;;;OAGG;IACH,SAAgB,OAAO,EAAE,SAAS,EAAE,CAAM;IAC1C,SAAgB,IAAI,EAAE,IAAI,CAAC;IAC3B;;;OAGG;IACH,SAAgB,KAAK,EAAE,KAAK,CAAC;IAC7B;;;;;;OAMG;IACH,SAAgB,SAAS,EAAE,QAAQ,EAAE,CAAM;IA6D3C,SAAgB,aAAa,EAAE,aAAa,CAAC;gBAG3C,YAAY,EAAE,YAAY,EAC1B,IAAI,EAAE,IAAI,EACV,UAAU,EAAE,KAAK,EACjB,KAAK,EAAE,KAAK,EACZ,kBAAkB,EAAE,OAAO;IA4B7B,OAAO,CAAC,eAAe;IAIvB,OAAO,CAAC,8BAA8B;IAkC/B,KAAK,CAAC,YAAY,EAAE,YAAY,GAAG,KAAK,GAAG,IAAI;IAmB/C,qBAAqB,IAAI,OAAO;IAIvC;;;OAGG;IACH,SAAS,CAAC,cAAc,CACtB,cAAc,EAAE,MAAM,GAAG,QAAQ,EACjC,GAAG,EAAE,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,EAC1B,SAAS,EAAE,QAAQ,EAAE,EACrB,IAAI,EAAE,QAAQ,CAAC,UAAU,GAAG,IAAI,EAChC,GAAG,EAAE,UAAU,GAAG,IAAI,GACrB,IAAI;IAuBP,SAAS,CAAC,oBAAoB,CAAC,GAAG,EAAE,SAAS,GAAG,IAAI;IAKpD,SAAS,CAAC,iBAAiB,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,GAAG,OAAO;IAI1E,OAAO,CAAC,0BAA0B;IAmB3B,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,UAAU,EAAE,GAAG,EAAE,UAAU,GAAG,IAAI;IAIlE,uBAAuB,CAC5B,IAAI,EAAE,QAAQ,CAAC,aAAa,EAC5B,GAAG,EAAE,UAAU,GACd,IAAI;IAIA,sBAAsB,CAAC,IAAI,EAAE,QAAQ,CAAC,UAAU,GAAG,IAAI;IAevD,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,UAAU,GAAG,IAAI;IAe9C,cAAc,CACnB,IAAI,EAAE,QAAQ,CAAC,UAAU,GAAG,QAAQ,CAAC,aAAa,EAClD,MAAM,GAAE,aAAkC,EAC1C,SAAS,CAAC,EAAE,QAAQ,CAAC,UAAU,GAAG,IAAI,EACtC,mBAAmB,CAAC,EAAE,uBAAuB,GAAG,IAAI,EACpD,IAAI,UAAQ,GACX,IAAI;CAcR"}