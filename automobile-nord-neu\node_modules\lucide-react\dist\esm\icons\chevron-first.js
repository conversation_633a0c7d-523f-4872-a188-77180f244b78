/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "m17 18-6-6 6-6", key: "1yerx2" }],
  ["path", { d: "M7 6v12", key: "1p53r6" }]
];
const ChevronFirst = createLucideIcon("chevron-first", __iconNode);

export { __iconNode, ChevronFirst as default };
//# sourceMappingURL=chevron-first.js.map
