{"version": 3, "sources": ["../../src/diagnostics/build-diagnostics.ts"], "sourcesContent": ["import { mkdir, readFile, writeFile } from 'fs/promises'\nimport { join } from 'path'\nimport { traceGlobals } from '../trace/shared'\nimport type { ExportAppResult } from '../export/types'\nimport type { FetchMetrics } from '../server/base-http'\n\nconst DIAGNOSTICS_DIR = 'diagnostics'\nconst DIAGNOSTICS_FILE = 'build-diagnostics.json'\nconst FETCH_METRICS_FILE = 'fetch-metrics.json'\nconst INCREMENTAL_BUILDS_FILE = 'incremental-build-diagnostics.json'\nconst FRAMEWORK_VERSION_FILE = 'framework.json'\n\ninterface BuildDiagnostics {\n  // The current stage of the build process. This should be updated as the\n  // build progresses so it's what stage the build was in when an error\n  // happened.\n  buildStage?: string\n  // Additional debug information about the configuration for the build.\n  buildOptions?: Record<string, string>\n}\n\nasync function getDiagnosticsDir(): Promise<string> {\n  const distDir = traceGlobals.get('distDir')\n  const diagnosticsDir = join(distDir, DIAGNOSTICS_DIR)\n  await mkdir(diagnosticsDir, { recursive: true })\n  return diagnosticsDir\n}\n\n/**\n * Saves the exact version of Next.js that was used to build the app to a diagnostics file.\n */\nexport async function recordFrameworkVersion(version: string): Promise<void> {\n  const diagnosticsDir = await getDiagnosticsDir()\n  const frameworkVersionFile = join(diagnosticsDir, FRAMEWORK_VERSION_FILE)\n  await writeFile(\n    frameworkVersionFile,\n    JSON.stringify({ name: 'Next.js', version })\n  )\n}\n\n/**\n * Saves build diagnostics information to a file. This method can be called\n * multiple times during a build to save additional information that can help\n * debug a build such as what stage the build was in when a failure happened.\n * Each time this method is called, the new information will be merged with any\n * existing build diagnostics that previously existed.\n */\nexport async function updateBuildDiagnostics(\n  diagnostics: BuildDiagnostics\n): Promise<void> {\n  const diagnosticsDir = await getDiagnosticsDir()\n  const diagnosticsFile = join(diagnosticsDir, DIAGNOSTICS_FILE)\n\n  const existingDiagnostics: BuildDiagnostics = JSON.parse(\n    await readFile(diagnosticsFile, 'utf8').catch(() => '{}')\n  ) as BuildDiagnostics\n  const updatedBuildOptions = {\n    ...(existingDiagnostics.buildOptions ?? {}),\n    ...(diagnostics.buildOptions ?? {}),\n  }\n  const updatedDiagnostics = {\n    ...existingDiagnostics,\n    ...diagnostics,\n    buildOptions: updatedBuildOptions,\n  }\n  await writeFile(diagnosticsFile, JSON.stringify(updatedDiagnostics, null, 2))\n}\n\n/**\n * Writes fetch metrics collected during static generation to a file.\n */\nexport async function recordFetchMetrics(\n  exportResult: ExportAppResult\n): Promise<void> {\n  const diagnosticsDir = await getDiagnosticsDir()\n  const diagnosticsFile = join(diagnosticsDir, FETCH_METRICS_FILE)\n  const fetchMetricsByPath: Record<string, FetchMetrics> = {}\n\n  for (const [appPath, { fetchMetrics }] of exportResult.byPath) {\n    if (fetchMetrics) {\n      fetchMetricsByPath[appPath] = fetchMetrics\n    }\n  }\n\n  return writeFile(diagnosticsFile, JSON.stringify(fetchMetricsByPath, null, 2))\n}\n\ninterface IncrementalBuildDiagnostics {\n  changedAppPaths?: string[]\n  unchangedAppPaths?: string[]\n  changedPagePaths?: string[]\n  unchangedPagePaths?: string[]\n  changedDependencies?: Record<string, string>\n  shuttleGitSha?: string\n  currentGitSha?: string\n}\n\n/**\n * Writes incremental build metrics to a file.\n */\nexport async function updateIncrementalBuildMetrics(\n  diagnostics: IncrementalBuildDiagnostics\n): Promise<void> {\n  const diagnosticsDir = await getDiagnosticsDir()\n  const diagnosticsFile = join(diagnosticsDir, INCREMENTAL_BUILDS_FILE)\n\n  const existingDiagnostics: IncrementalBuildDiagnostics = JSON.parse(\n    await readFile(diagnosticsFile, 'utf8').catch(() => '{}')\n  ) as IncrementalBuildDiagnostics\n\n  const updatedDiagnostics = {\n    ...existingDiagnostics,\n    ...diagnostics,\n  }\n  await writeFile(diagnosticsFile, JSON.stringify(updatedDiagnostics, null, 2))\n}\n"], "names": ["recordFetchMetrics", "recordFrameworkVersion", "updateBuildDiagnostics", "updateIncrementalBuildMetrics", "DIAGNOSTICS_DIR", "DIAGNOSTICS_FILE", "FETCH_METRICS_FILE", "INCREMENTAL_BUILDS_FILE", "FRAMEWORK_VERSION_FILE", "getDiagnosticsDir", "distDir", "traceGlobals", "get", "diagnosticsDir", "join", "mkdir", "recursive", "version", "frameworkVersionFile", "writeFile", "JSON", "stringify", "name", "diagnostics", "diagnosticsFile", "existingDiagnostics", "parse", "readFile", "catch", "updatedBuildOptions", "buildOptions", "updatedDiagnostics", "exportResult", "fetchMetricsByPath", "appPath", "fetchMetrics", "by<PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;IAuEsBA,kBAAkB;eAAlBA;;IAxCAC,sBAAsB;eAAtBA;;IAgBAC,sBAAsB;eAAtBA;;IAqDAC,6BAA6B;eAA7BA;;;0BApGqB;sBACtB;wBACQ;AAI7B,MAAMC,kBAAkB;AACxB,MAAMC,mBAAmB;AACzB,MAAMC,qBAAqB;AAC3B,MAAMC,0BAA0B;AAChC,MAAMC,yBAAyB;AAW/B,eAAeC;IACb,MAAMC,UAAUC,oBAAY,CAACC,GAAG,CAAC;IACjC,MAAMC,iBAAiBC,IAAAA,UAAI,EAACJ,SAASN;IACrC,MAAMW,IAAAA,eAAK,EAACF,gBAAgB;QAAEG,WAAW;IAAK;IAC9C,OAAOH;AACT;AAKO,eAAeZ,uBAAuBgB,OAAe;IAC1D,MAAMJ,iBAAiB,MAAMJ;IAC7B,MAAMS,uBAAuBJ,IAAAA,UAAI,EAACD,gBAAgBL;IAClD,MAAMW,IAAAA,mBAAS,EACbD,sBACAE,KAAKC,SAAS,CAAC;QAAEC,MAAM;QAAWL;IAAQ;AAE9C;AASO,eAAef,uBACpBqB,WAA6B;IAE7B,MAAMV,iBAAiB,MAAMJ;IAC7B,MAAMe,kBAAkBV,IAAAA,UAAI,EAACD,gBAAgBR;IAE7C,MAAMoB,sBAAwCL,KAAKM,KAAK,CACtD,MAAMC,IAAAA,kBAAQ,EAACH,iBAAiB,QAAQI,KAAK,CAAC,IAAM;IAEtD,MAAMC,sBAAsB;QAC1B,GAAIJ,oBAAoBK,YAAY,IAAI,CAAC,CAAC;QAC1C,GAAIP,YAAYO,YAAY,IAAI,CAAC,CAAC;IACpC;IACA,MAAMC,qBAAqB;QACzB,GAAGN,mBAAmB;QACtB,GAAGF,WAAW;QACdO,cAAcD;IAChB;IACA,MAAMV,IAAAA,mBAAS,EAACK,iBAAiBJ,KAAKC,SAAS,CAACU,oBAAoB,MAAM;AAC5E;AAKO,eAAe/B,mBACpBgC,YAA6B;IAE7B,MAAMnB,iBAAiB,MAAMJ;IAC7B,MAAMe,kBAAkBV,IAAAA,UAAI,EAACD,gBAAgBP;IAC7C,MAAM2B,qBAAmD,CAAC;IAE1D,KAAK,MAAM,CAACC,SAAS,EAAEC,YAAY,EAAE,CAAC,IAAIH,aAAaI,MAAM,CAAE;QAC7D,IAAID,cAAc;YAChBF,kBAAkB,CAACC,QAAQ,GAAGC;QAChC;IACF;IAEA,OAAOhB,IAAAA,mBAAS,EAACK,iBAAiBJ,KAAKC,SAAS,CAACY,oBAAoB,MAAM;AAC7E;AAeO,eAAe9B,8BACpBoB,WAAwC;IAExC,MAAMV,iBAAiB,MAAMJ;IAC7B,MAAMe,kBAAkBV,IAAAA,UAAI,EAACD,gBAAgBN;IAE7C,MAAMkB,sBAAmDL,KAAKM,KAAK,CACjE,MAAMC,IAAAA,kBAAQ,EAACH,iBAAiB,QAAQI,KAAK,CAAC,IAAM;IAGtD,MAAMG,qBAAqB;QACzB,GAAGN,mBAAmB;QACtB,GAAGF,WAAW;IAChB;IACA,MAAMJ,IAAAA,mBAAS,EAACK,iBAAiBJ,KAAKC,SAAS,CAACU,oBAAoB,MAAM;AAC5E", "ignoreList": [0]}