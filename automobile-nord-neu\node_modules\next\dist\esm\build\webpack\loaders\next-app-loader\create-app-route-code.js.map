{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-app-loader/create-app-route-code.ts"], "sourcesContent": ["import path from 'path'\nimport { stringify } from 'querystring'\nimport { WEBPACK_RESOURCE_QUERIES } from '../../../../lib/constants'\nimport {\n  DEFAULT_METADATA_ROUTE_EXTENSIONS,\n  isMetadataRouteFile,\n} from '../../../../lib/metadata/is-metadata-route'\nimport type { NextConfig } from '../../../../server/config-shared'\nimport { AppBundlePathNormalizer } from '../../../../server/normalizers/built/app/app-bundle-path-normalizer'\nimport { AppPathnameNormalizer } from '../../../../server/normalizers/built/app/app-pathname-normalizer'\nimport { loadEntrypoint } from '../../../load-entrypoint'\nimport type { PageExtensions } from '../../../page-extensions-type'\nimport { getFilenameAndExtension } from '../next-metadata-route-loader'\n\nexport async function createAppRouteCode({\n  appDir,\n  name,\n  page,\n  pagePath,\n  resolveAppRoute,\n  pageExtensions,\n  nextConfigOutput,\n}: {\n  appDir: string\n  name: string\n  page: string\n  pagePath: string\n  resolveAppRoute: (\n    pathname: string\n  ) => Promise<string | undefined> | string | undefined\n  pageExtensions: PageExtensions\n  nextConfigOutput: NextConfig['output']\n}): Promise<string> {\n  // routePath is the path to the route handler file,\n  // but could be aliased e.g. private-next-app-dir/favicon.ico\n  const routePath = pagePath.replace(/[\\\\/]/, '/')\n\n  // This, when used with the resolver will give us the pathname to the built\n  // route handler file.\n  let resolvedPagePath = await resolveAppRoute(routePath)\n  if (!resolvedPagePath) {\n    throw new Error(\n      `Invariant: could not resolve page path for ${name} at ${routePath}`\n    )\n  }\n\n  // If this is a metadata route file, then we need to use the metadata-loader\n  // for the route to ensure that the route is generated.\n  const fileBaseName = path.parse(resolvedPagePath).name\n  const appDirRelativePath = resolvedPagePath.slice(appDir.length)\n  const isMetadataEntryFile = isMetadataRouteFile(\n    appDirRelativePath,\n    DEFAULT_METADATA_ROUTE_EXTENSIONS,\n    true\n  )\n  if (isMetadataEntryFile) {\n    const { ext } = getFilenameAndExtension(resolvedPagePath)\n    const isDynamicRouteExtension = pageExtensions.includes(ext)\n\n    resolvedPagePath = `next-metadata-route-loader?${stringify({\n      filePath: resolvedPagePath,\n      isDynamicRouteExtension: isDynamicRouteExtension ? '1' : '0',\n    })}!?${WEBPACK_RESOURCE_QUERIES.metadataRoute}`\n  }\n\n  const pathname = new AppPathnameNormalizer().normalize(page)\n  const bundlePath = new AppBundlePathNormalizer().normalize(page)\n\n  return await loadEntrypoint(\n    'app-route',\n    {\n      VAR_USERLAND: resolvedPagePath,\n      VAR_DEFINITION_PAGE: page,\n      VAR_DEFINITION_PATHNAME: pathname,\n      VAR_DEFINITION_FILENAME: fileBaseName,\n      VAR_DEFINITION_BUNDLE_PATH: bundlePath,\n      VAR_RESOLVED_PAGE_PATH: resolvedPagePath,\n    },\n    {\n      nextConfigOutput: JSON.stringify(nextConfigOutput),\n    }\n  )\n}\n"], "names": ["path", "stringify", "WEBPACK_RESOURCE_QUERIES", "DEFAULT_METADATA_ROUTE_EXTENSIONS", "isMetadataRouteFile", "AppBundlePathNormalizer", "AppPathnameNormalizer", "loadEntrypoint", "getFilenameAndExtension", "createAppRouteCode", "appDir", "name", "page", "pagePath", "resolveAppRoute", "pageExtensions", "nextConfigOutput", "routePath", "replace", "resolvedPagePath", "Error", "fileBaseName", "parse", "appDirRelativePath", "slice", "length", "isMetadataEntryFile", "ext", "isDynamicRouteExtension", "includes", "filePath", "metadataRoute", "pathname", "normalize", "bundlePath", "VAR_USERLAND", "VAR_DEFINITION_PAGE", "VAR_DEFINITION_PATHNAME", "VAR_DEFINITION_FILENAME", "VAR_DEFINITION_BUNDLE_PATH", "VAR_RESOLVED_PAGE_PATH", "JSON"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,SAASC,SAAS,QAAQ,cAAa;AACvC,SAASC,wBAAwB,QAAQ,4BAA2B;AACpE,SACEC,iCAAiC,EACjCC,mBAAmB,QACd,6CAA4C;AAEnD,SAASC,uBAAuB,QAAQ,sEAAqE;AAC7G,SAASC,qBAAqB,QAAQ,mEAAkE;AACxG,SAASC,cAAc,QAAQ,2BAA0B;AAEzD,SAASC,uBAAuB,QAAQ,gCAA+B;AAEvE,OAAO,eAAeC,mBAAmB,EACvCC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,eAAe,EACfC,cAAc,EACdC,gBAAgB,EAWjB;IACC,mDAAmD;IACnD,6DAA6D;IAC7D,MAAMC,YAAYJ,SAASK,OAAO,CAAC,SAAS;IAE5C,2EAA2E;IAC3E,sBAAsB;IACtB,IAAIC,mBAAmB,MAAML,gBAAgBG;IAC7C,IAAI,CAACE,kBAAkB;QACrB,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,2CAA2C,EAAET,KAAK,IAAI,EAAEM,WAAW,GADhE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,4EAA4E;IAC5E,uDAAuD;IACvD,MAAMI,eAAerB,KAAKsB,KAAK,CAACH,kBAAkBR,IAAI;IACtD,MAAMY,qBAAqBJ,iBAAiBK,KAAK,CAACd,OAAOe,MAAM;IAC/D,MAAMC,sBAAsBtB,oBAC1BmB,oBACApB,mCACA;IAEF,IAAIuB,qBAAqB;QACvB,MAAM,EAAEC,GAAG,EAAE,GAAGnB,wBAAwBW;QACxC,MAAMS,0BAA0Bb,eAAec,QAAQ,CAACF;QAExDR,mBAAmB,CAAC,2BAA2B,EAAElB,UAAU;YACzD6B,UAAUX;YACVS,yBAAyBA,0BAA0B,MAAM;QAC3D,GAAG,EAAE,EAAE1B,yBAAyB6B,aAAa,EAAE;IACjD;IAEA,MAAMC,WAAW,IAAI1B,wBAAwB2B,SAAS,CAACrB;IACvD,MAAMsB,aAAa,IAAI7B,0BAA0B4B,SAAS,CAACrB;IAE3D,OAAO,MAAML,eACX,aACA;QACE4B,cAAchB;QACdiB,qBAAqBxB;QACrByB,yBAAyBL;QACzBM,yBAAyBjB;QACzBkB,4BAA4BL;QAC5BM,wBAAwBrB;IAC1B,GACA;QACEH,kBAAkByB,KAAKxC,SAAS,CAACe;IACnC;AAEJ", "ignoreList": [0]}