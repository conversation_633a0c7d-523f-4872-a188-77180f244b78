{"version": 3, "sources": ["../../src/client/app-next-turbopack.ts"], "sourcesContent": ["import { appBootstrap } from './app-bootstrap'\nimport { isRecoverableError } from './react-client-callbacks/on-recoverable-error'\n\nwindow.next.version += '-turbo'\n;(self as any).__webpack_hash__ = ''\n\n// eslint-disable-next-line @next/internal/typechecked-require\nconst instrumentationHooks = require('../lib/require-instrumentation-client')\n\nappBootstrap(() => {\n  const { hydrate } = require('./app-index') as typeof import('./app-index')\n  try {\n    hydrate(instrumentationHooks)\n  } finally {\n    if (process.env.NODE_ENV !== 'production') {\n      const { getComponentStack, getOwnerStack } =\n        require('../next-devtools/userspace/app/errors/stitched-error') as typeof import('../next-devtools/userspace/app/errors/stitched-error')\n      const { renderAppDevOverlay } =\n        require('next/dist/compiled/next-devtools') as typeof import('next/dist/compiled/next-devtools')\n      renderAppDevOverlay(getComponentStack, getOwnerStack, isRecoverableError)\n    }\n  }\n})\n"], "names": ["appBootstrap", "isRecoverableError", "window", "next", "version", "self", "__webpack_hash__", "<PERSON><PERSON><PERSON><PERSON>", "require", "hydrate", "process", "env", "NODE_ENV", "getComponentStack", "getOwnerStack", "renderAppDevOverlay"], "mappings": "AAAA,SAASA,YAAY,QAAQ,kBAAiB;AAC9C,SAASC,kBAAkB,QAAQ,gDAA+C;AAElFC,OAAOC,IAAI,CAACC,OAAO,IAAI;AACrBC,KAAaC,gBAAgB,GAAG;AAElC,8DAA8D;AAC9D,MAAMC,uBAAuBC,QAAQ;AAErCR,aAAa;IACX,MAAM,EAAES,OAAO,EAAE,GAAGD,QAAQ;IAC5B,IAAI;QACFC,QAAQF;IACV,SAAU;QACR,IAAIG,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,MAAM,EAAEC,iBAAiB,EAAEC,aAAa,EAAE,GACxCN,QAAQ;YACV,MAAM,EAAEO,mBAAmB,EAAE,GAC3BP,QAAQ;YACVO,oBAAoBF,mBAAmBC,eAAeb;QACxD;IACF;AACF", "ignoreList": [0]}