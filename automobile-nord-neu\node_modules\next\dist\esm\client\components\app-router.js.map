{"version": 3, "sources": ["../../../src/client/components/app-router.tsx"], "sourcesContent": ["'use client'\n\nimport React, {\n  useEffect,\n  useMemo,\n  startTransition,\n  useInsertionEffect,\n  useDeferredValue,\n} from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  GlobalLayoutRouterContext,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport type { CacheNode } from '../../shared/lib/app-router-context.shared-runtime'\nimport { ACTION_RESTORE } from './router-reducer/router-reducer-types'\nimport type { AppRouterState } from './router-reducer/router-reducer-types'\nimport { createHrefFromUrl } from './router-reducer/create-href-from-url'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { dispatchAppRouterAction, useActionQueue } from './use-action-queue'\nimport { ErrorBoundary } from './error-boundary'\nimport DefaultGlobalError from './builtin/global-error'\nimport { isBot } from '../../shared/lib/router/utils/is-bot'\nimport { addBasePath } from '../add-base-path'\nimport { AppRouterAnnouncer } from './app-router-announcer'\nimport { RedirectBoundary } from './redirect-boundary'\nimport { findHeadInCache } from './router-reducer/reducers/find-head-in-cache'\nimport { unresolvedThenable } from './unresolved-thenable'\nimport { removeBasePath } from '../remove-base-path'\nimport { hasBasePath } from '../has-base-path'\nimport { getSelectedParams } from './router-reducer/compute-changed-path'\nimport type { FlightRouterState } from '../../server/app-render/types'\nimport { useNavFailureHandler } from './nav-failure-handler'\nimport {\n  dispatchTraverseAction,\n  publicAppRouterInstance,\n  type AppRouterActionQueue,\n  type GlobalErrorState,\n} from './app-router-instance'\nimport { getRedirectTypeFromError, getURLFromRedirectError } from './redirect'\nimport { isRedirectError, RedirectType } from './redirect-error'\nimport { pingVisibleLinks } from './links'\nimport GracefulDegradeBoundary from './errors/graceful-degrade-boundary'\n\nconst globalMutable: {\n  pendingMpaPath?: string\n} = {}\n\nexport function isExternalURL(url: URL) {\n  return url.origin !== window.location.origin\n}\n\n/**\n * Given a link href, constructs the URL that should be prefetched. Returns null\n * in cases where prefetching should be disabled, like external URLs, or\n * during development.\n * @param href The href passed to <Link>, router.prefetch(), or similar\n * @returns A URL object to prefetch, or null if prefetching should be disabled\n */\nexport function createPrefetchURL(href: string): URL | null {\n  // Don't prefetch for bots as they don't navigate.\n  if (isBot(window.navigator.userAgent)) {\n    return null\n  }\n\n  let url: URL\n  try {\n    url = new URL(addBasePath(href), window.location.href)\n  } catch (_) {\n    // TODO: Does this need to throw or can we just console.error instead? Does\n    // anyone rely on this throwing? (Seems unlikely.)\n    throw new Error(\n      `Cannot prefetch '${href}' because it cannot be converted to a URL.`\n    )\n  }\n\n  // Don't prefetch during development (improves compilation performance)\n  if (process.env.NODE_ENV === 'development') {\n    return null\n  }\n\n  // External urls can't be prefetched in the same way.\n  if (isExternalURL(url)) {\n    return null\n  }\n\n  return url\n}\n\nfunction HistoryUpdater({\n  appRouterState,\n}: {\n  appRouterState: AppRouterState\n}) {\n  useInsertionEffect(() => {\n    if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n      // clear pending URL as navigation is no longer\n      // in flight\n      window.next.__pendingUrl = undefined\n    }\n\n    const { tree, pushRef, canonicalUrl } = appRouterState\n    const historyState = {\n      ...(pushRef.preserveCustomHistoryState ? window.history.state : {}),\n      // Identifier is shortened intentionally.\n      // __NA is used to identify if the history entry can be handled by the app-router.\n      // __N is used to identify if the history entry can be handled by the old router.\n      __NA: true,\n      __PRIVATE_NEXTJS_INTERNALS_TREE: tree,\n    }\n    if (\n      pushRef.pendingPush &&\n      // Skip pushing an additional history entry if the canonicalUrl is the same as the current url.\n      // This mirrors the browser behavior for normal navigation.\n      createHrefFromUrl(new URL(window.location.href)) !== canonicalUrl\n    ) {\n      // This intentionally mutates React state, pushRef is overwritten to ensure additional push/replace calls do not trigger an additional history entry.\n      pushRef.pendingPush = false\n      window.history.pushState(historyState, '', canonicalUrl)\n    } else {\n      window.history.replaceState(historyState, '', canonicalUrl)\n    }\n  }, [appRouterState])\n\n  useEffect(() => {\n    // The Next-Url and the base tree may affect the result of a prefetch\n    // task. Re-prefetch all visible links with the updated values. In most\n    // cases, this will not result in any new network requests, only if\n    // the prefetch result actually varies on one of these inputs.\n    if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n      pingVisibleLinks(appRouterState.nextUrl, appRouterState.tree)\n    }\n  }, [appRouterState.nextUrl, appRouterState.tree])\n\n  return null\n}\n\nexport function createEmptyCacheNode(): CacheNode {\n  return {\n    lazyData: null,\n    rsc: null,\n    prefetchRsc: null,\n    head: null,\n    prefetchHead: null,\n    parallelRoutes: new Map(),\n    loading: null,\n    navigatedAt: -1,\n  }\n}\n\nfunction copyNextJsInternalHistoryState(data: any) {\n  if (data == null) data = {}\n  const currentState = window.history.state\n  const __NA = currentState?.__NA\n  if (__NA) {\n    data.__NA = __NA\n  }\n  const __PRIVATE_NEXTJS_INTERNALS_TREE =\n    currentState?.__PRIVATE_NEXTJS_INTERNALS_TREE\n  if (__PRIVATE_NEXTJS_INTERNALS_TREE) {\n    data.__PRIVATE_NEXTJS_INTERNALS_TREE = __PRIVATE_NEXTJS_INTERNALS_TREE\n  }\n\n  return data\n}\n\nfunction Head({\n  headCacheNode,\n}: {\n  headCacheNode: CacheNode | null\n}): React.ReactNode {\n  // If this segment has a `prefetchHead`, it's the statically prefetched data.\n  // We should use that on initial render instead of `head`. Then we'll switch\n  // to `head` when the dynamic response streams in.\n  const head = headCacheNode !== null ? headCacheNode.head : null\n  const prefetchHead =\n    headCacheNode !== null ? headCacheNode.prefetchHead : null\n\n  // If no prefetch data is available, then we go straight to rendering `head`.\n  const resolvedPrefetchRsc = prefetchHead !== null ? prefetchHead : head\n\n  // We use `useDeferredValue` to handle switching between the prefetched and\n  // final values. The second argument is returned on initial render, then it\n  // re-renders with the first argument.\n  return useDeferredValue(head, resolvedPrefetchRsc)\n}\n\n/**\n * The global router that wraps the application components.\n */\nfunction Router({\n  actionQueue,\n  assetPrefix,\n  globalError,\n  gracefullyDegrade,\n}: {\n  actionQueue: AppRouterActionQueue\n  assetPrefix: string\n  globalError: GlobalErrorState\n  gracefullyDegrade: boolean\n}) {\n  const state = useActionQueue(actionQueue)\n  const { canonicalUrl } = state\n  // Add memoized pathname/query for useSearchParams and usePathname.\n  const { searchParams, pathname } = useMemo(() => {\n    const url = new URL(\n      canonicalUrl,\n      typeof window === 'undefined' ? 'http://n' : window.location.href\n    )\n\n    return {\n      // This is turned into a readonly class in `useSearchParams`\n      searchParams: url.searchParams,\n      pathname: hasBasePath(url.pathname)\n        ? removeBasePath(url.pathname)\n        : url.pathname,\n    }\n  }, [canonicalUrl])\n\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const { cache, prefetchCache, tree } = state\n\n    // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      // Add `window.nd` for debugging purposes.\n      // This is not meant for use in applications as concurrent rendering will affect the cache/tree/router.\n      // @ts-ignore this is for debugging\n      window.nd = {\n        router: publicAppRouterInstance,\n        cache,\n        prefetchCache,\n        tree,\n      }\n    }, [cache, prefetchCache, tree])\n  }\n\n  useEffect(() => {\n    // If the app is restored from bfcache, it's possible that\n    // pushRef.mpaNavigation is true, which would mean that any re-render of this component\n    // would trigger the mpa navigation logic again from the lines below.\n    // This will restore the router to the initial state in the event that the app is restored from bfcache.\n    function handlePageShow(event: PageTransitionEvent) {\n      if (\n        !event.persisted ||\n        !window.history.state?.__PRIVATE_NEXTJS_INTERNALS_TREE\n      ) {\n        return\n      }\n\n      // Clear the pendingMpaPath value so that a subsequent MPA navigation to the same URL can be triggered.\n      // This is necessary because if the browser restored from bfcache, the pendingMpaPath would still be set to the value\n      // of the last MPA navigation.\n      globalMutable.pendingMpaPath = undefined\n\n      dispatchAppRouterAction({\n        type: ACTION_RESTORE,\n        url: new URL(window.location.href),\n        tree: window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE,\n      })\n    }\n\n    window.addEventListener('pageshow', handlePageShow)\n\n    return () => {\n      window.removeEventListener('pageshow', handlePageShow)\n    }\n  }, [])\n\n  useEffect(() => {\n    // Ensure that any redirect errors that bubble up outside of the RedirectBoundary\n    // are caught and handled by the router.\n    function handleUnhandledRedirect(\n      event: ErrorEvent | PromiseRejectionEvent\n    ) {\n      const error = 'reason' in event ? event.reason : event.error\n      if (isRedirectError(error)) {\n        event.preventDefault()\n        const url = getURLFromRedirectError(error)\n        const redirectType = getRedirectTypeFromError(error)\n        // TODO: This should access the router methods directly, rather than\n        // go through the public interface.\n        if (redirectType === RedirectType.push) {\n          publicAppRouterInstance.push(url, {})\n        } else {\n          publicAppRouterInstance.replace(url, {})\n        }\n      }\n    }\n    window.addEventListener('error', handleUnhandledRedirect)\n    window.addEventListener('unhandledrejection', handleUnhandledRedirect)\n\n    return () => {\n      window.removeEventListener('error', handleUnhandledRedirect)\n      window.removeEventListener('unhandledrejection', handleUnhandledRedirect)\n    }\n  }, [])\n\n  // When mpaNavigation flag is set do a hard navigation to the new url.\n  // Infinitely suspend because we don't actually want to rerender any child\n  // components with the new URL and any entangled state updates shouldn't\n  // commit either (eg: useTransition isPending should stay true until the page\n  // unloads).\n  //\n  // This is a side effect in render. Don't try this at home, kids. It's\n  // probably safe because we know this is a singleton component and it's never\n  // in <Offscreen>. At least I hope so. (It will run twice in dev strict mode,\n  // but that's... fine?)\n  const { pushRef } = state\n  if (pushRef.mpaNavigation) {\n    // if there's a re-render, we don't want to trigger another redirect if one is already in flight to the same URL\n    if (globalMutable.pendingMpaPath !== canonicalUrl) {\n      const location = window.location\n      if (pushRef.pendingPush) {\n        location.assign(canonicalUrl)\n      } else {\n        location.replace(canonicalUrl)\n      }\n\n      globalMutable.pendingMpaPath = canonicalUrl\n    }\n    // TODO-APP: Should we listen to navigateerror here to catch failed\n    // navigations somehow? And should we call window.stop() if a SPA navigation\n    // should interrupt an MPA one?\n    // NOTE: This is intentionally using `throw` instead of `use` because we're\n    // inside an externally mutable condition (pushRef.mpaNavigation), which\n    // violates the rules of hooks.\n    throw unresolvedThenable\n  }\n\n  useEffect(() => {\n    const originalPushState = window.history.pushState.bind(window.history)\n    const originalReplaceState = window.history.replaceState.bind(\n      window.history\n    )\n\n    // Ensure the canonical URL in the Next.js Router is updated when the URL is changed so that `usePathname` and `useSearchParams` hold the pushed values.\n    const applyUrlFromHistoryPushReplace = (\n      url: string | URL | null | undefined\n    ) => {\n      const href = window.location.href\n      const tree: FlightRouterState | undefined =\n        window.history.state?.__PRIVATE_NEXTJS_INTERNALS_TREE\n\n      startTransition(() => {\n        dispatchAppRouterAction({\n          type: ACTION_RESTORE,\n          url: new URL(url ?? href, href),\n          tree,\n        })\n      })\n    }\n\n    /**\n     * Patch pushState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */\n    window.history.pushState = function pushState(\n      data: any,\n      _unused: string,\n      url?: string | URL | null\n    ): void {\n      // Avoid a loop when Next.js internals trigger pushState/replaceState\n      if (data?.__NA || data?._N) {\n        return originalPushState(data, _unused, url)\n      }\n\n      data = copyNextJsInternalHistoryState(data)\n\n      if (url) {\n        applyUrlFromHistoryPushReplace(url)\n      }\n\n      return originalPushState(data, _unused, url)\n    }\n\n    /**\n     * Patch replaceState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */\n    window.history.replaceState = function replaceState(\n      data: any,\n      _unused: string,\n      url?: string | URL | null\n    ): void {\n      // Avoid a loop when Next.js internals trigger pushState/replaceState\n      if (data?.__NA || data?._N) {\n        return originalReplaceState(data, _unused, url)\n      }\n      data = copyNextJsInternalHistoryState(data)\n\n      if (url) {\n        applyUrlFromHistoryPushReplace(url)\n      }\n      return originalReplaceState(data, _unused, url)\n    }\n\n    /**\n     * Handle popstate event, this is used to handle back/forward in the browser.\n     * By default dispatches ACTION_RESTORE, however if the history entry was not pushed/replaced by app-router it will reload the page.\n     * That case can happen when the old router injected the history entry.\n     */\n    const onPopState = (event: PopStateEvent) => {\n      if (!event.state) {\n        // TODO-APP: this case only happens when pushState/replaceState was called outside of Next.js. It should probably reload the page in this case.\n        return\n      }\n\n      // This case happens when the history entry was pushed by the `pages` router.\n      if (!event.state.__NA) {\n        window.location.reload()\n        return\n      }\n\n      // TODO-APP: Ideally the back button should not use startTransition as it should apply the updates synchronously\n      // Without startTransition works if the cache is there for this path\n      startTransition(() => {\n        dispatchTraverseAction(\n          window.location.href,\n          event.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n        )\n      })\n    }\n\n    // Register popstate event to call onPopstate.\n    window.addEventListener('popstate', onPopState)\n    return () => {\n      window.history.pushState = originalPushState\n      window.history.replaceState = originalReplaceState\n      window.removeEventListener('popstate', onPopState)\n    }\n  }, [])\n\n  const { cache, tree, nextUrl, focusAndScrollRef } = state\n\n  const matchingHead = useMemo(() => {\n    return findHeadInCache(cache, tree[1])\n  }, [cache, tree])\n\n  // Add memoized pathParams for useParams.\n  const pathParams = useMemo(() => {\n    return getSelectedParams(tree)\n  }, [tree])\n\n  const layoutRouterContext = useMemo(() => {\n    return {\n      parentTree: tree,\n      parentCacheNode: cache,\n      parentSegmentPath: null,\n      // Root node always has `url`\n      // Provided in AppTreeContext to ensure it can be overwritten in layout-router\n      url: canonicalUrl,\n    }\n  }, [tree, cache, canonicalUrl])\n\n  const globalLayoutRouterContext = useMemo(() => {\n    return {\n      tree,\n      focusAndScrollRef,\n      nextUrl,\n    }\n  }, [tree, focusAndScrollRef, nextUrl])\n\n  let head\n  if (matchingHead !== null) {\n    // The head is wrapped in an extra component so we can use\n    // `useDeferredValue` to swap between the prefetched and final versions of\n    // the head. (This is what LayoutRouter does for segment data, too.)\n    //\n    // The `key` is used to remount the component whenever the head moves to\n    // a different segment.\n    const [headCacheNode, headKey] = matchingHead\n    head = <Head key={headKey} headCacheNode={headCacheNode} />\n  } else {\n    head = null\n  }\n\n  let content = (\n    <RedirectBoundary>\n      {head}\n      {cache.rsc}\n      <AppRouterAnnouncer tree={tree} />\n    </RedirectBoundary>\n  )\n\n  if (process.env.NODE_ENV !== 'production') {\n    // In development, we apply few error boundaries and hot-reloader:\n    // - DevRootHTTPAccessFallbackBoundary: avoid using navigation API like notFound() in root layout\n    // - HotReloader:\n    //  - hot-reload the app when the code changes\n    //  - render dev overlay\n    //  - catch runtime errors and display global-error when necessary\n    if (typeof window !== 'undefined') {\n      const { DevRootHTTPAccessFallbackBoundary } =\n        require('./dev-root-http-access-fallback-boundary') as typeof import('./dev-root-http-access-fallback-boundary')\n      content = (\n        <DevRootHTTPAccessFallbackBoundary>\n          {content}\n        </DevRootHTTPAccessFallbackBoundary>\n      )\n    }\n    const HotReloader: typeof import('../dev/hot-reloader/app/hot-reloader-app').default =\n      (\n        require('../dev/hot-reloader/app/hot-reloader-app') as typeof import('../dev/hot-reloader/app/hot-reloader-app')\n      ).default\n\n    content = (\n      <HotReloader assetPrefix={assetPrefix} globalError={globalError}>\n        {content}\n      </HotReloader>\n    )\n  } else {\n    // If gracefully degrading is applied in production,\n    // leave the app as it is rather than caught by GlobalError boundary.\n    if (gracefullyDegrade) {\n      content = <GracefulDegradeBoundary>{content}</GracefulDegradeBoundary>\n    } else {\n      content = (\n        <ErrorBoundary\n          errorComponent={globalError[0]}\n          errorStyles={globalError[1]}\n        >\n          {content}\n        </ErrorBoundary>\n      )\n    }\n  }\n\n  return (\n    <>\n      <HistoryUpdater appRouterState={state} />\n      <RuntimeStyles />\n      <PathParamsContext.Provider value={pathParams}>\n        <PathnameContext.Provider value={pathname}>\n          <SearchParamsContext.Provider value={searchParams}>\n            <GlobalLayoutRouterContext.Provider\n              value={globalLayoutRouterContext}\n            >\n              {/* TODO: We should be able to remove this context. useRouter\n                  should import from app-router-instance instead. It's only\n                  necessary because useRouter is shared between Pages and\n                  App Router. We should fork that module, then remove this\n                  context provider. */}\n              <AppRouterContext.Provider value={publicAppRouterInstance}>\n                <LayoutRouterContext.Provider value={layoutRouterContext}>\n                  {content}\n                </LayoutRouterContext.Provider>\n              </AppRouterContext.Provider>\n            </GlobalLayoutRouterContext.Provider>\n          </SearchParamsContext.Provider>\n        </PathnameContext.Provider>\n      </PathParamsContext.Provider>\n    </>\n  )\n}\n\nexport default function AppRouter({\n  actionQueue,\n  globalErrorState,\n  assetPrefix,\n  gracefullyDegrade,\n}: {\n  actionQueue: AppRouterActionQueue\n  globalErrorState: GlobalErrorState\n  assetPrefix: string\n  gracefullyDegrade: boolean\n}) {\n  useNavFailureHandler()\n\n  const router = (\n    <Router\n      actionQueue={actionQueue}\n      assetPrefix={assetPrefix}\n      globalError={globalErrorState}\n      gracefullyDegrade={gracefullyDegrade}\n    />\n  )\n\n  if (gracefullyDegrade) {\n    return router\n  } else {\n    return (\n      <ErrorBoundary\n        // At the very top level, use the default GlobalError component as the final fallback.\n        // When the app router itself fails, which means the framework itself fails, we show the default error.\n        errorComponent={DefaultGlobalError}\n      >\n        {router}\n      </ErrorBoundary>\n    )\n  }\n}\n\nconst runtimeStyles = new Set<string>()\nlet runtimeStyleChanged = new Set<() => void>()\n\nglobalThis._N_E_STYLE_LOAD = function (href: string) {\n  let len = runtimeStyles.size\n  runtimeStyles.add(href)\n  if (runtimeStyles.size !== len) {\n    runtimeStyleChanged.forEach((cb) => cb())\n  }\n  // TODO figure out how to get a promise here\n  // But maybe it's not necessary as react would block rendering until it's loaded\n  return Promise.resolve()\n}\n\nfunction RuntimeStyles() {\n  const [, forceUpdate] = React.useState(0)\n  const renderedStylesSize = runtimeStyles.size\n  useEffect(() => {\n    const changed = () => forceUpdate((c) => c + 1)\n    runtimeStyleChanged.add(changed)\n    if (renderedStylesSize !== runtimeStyles.size) {\n      changed()\n    }\n    return () => {\n      runtimeStyleChanged.delete(changed)\n    }\n  }, [renderedStylesSize, forceUpdate])\n\n  const dplId = process.env.NEXT_DEPLOYMENT_ID\n    ? `?dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n    : ''\n  return [...runtimeStyles].map((href, i) => (\n    <link\n      key={i}\n      rel=\"stylesheet\"\n      href={`${href}${dplId}`}\n      // @ts-ignore\n      precedence=\"next\"\n      // TODO figure out crossOrigin and nonce\n      // crossOrigin={TODO}\n      // nonce={TODO}\n    />\n  ))\n}\n"], "names": ["React", "useEffect", "useMemo", "startTransition", "useInsertionEffect", "useDeferredValue", "AppRouterContext", "LayoutRouterContext", "GlobalLayoutRouterContext", "ACTION_RESTORE", "createHrefFromUrl", "SearchParamsContext", "PathnameContext", "PathParamsContext", "dispatchAppRouterAction", "useActionQueue", "Error<PERSON>ou<PERSON><PERSON>", "DefaultGlobalError", "isBot", "addBasePath", "AppRouterAnnouncer", "RedirectBoundary", "findHeadInCache", "unresolvedThenable", "removeBasePath", "has<PERSON>ase<PERSON><PERSON>", "getSelectedParams", "useNavFailureHandler", "dispatchTraverseAction", "publicAppRouterInstance", "getRedirectTypeFromError", "getURLFromRedirectError", "isRedirectError", "RedirectType", "pingVisibleLinks", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "globalMutable", "isExternalURL", "url", "origin", "window", "location", "createPrefetchURL", "href", "navigator", "userAgent", "URL", "_", "Error", "process", "env", "NODE_ENV", "HistoryUpdater", "appRouterState", "__NEXT_APP_NAV_FAIL_HANDLING", "next", "__pendingUrl", "undefined", "tree", "pushRef", "canonicalUrl", "historyState", "preserveCustomHistoryState", "history", "state", "__NA", "__PRIVATE_NEXTJS_INTERNALS_TREE", "pendingPush", "pushState", "replaceState", "__NEXT_CLIENT_SEGMENT_CACHE", "nextUrl", "createEmptyCacheNode", "lazyData", "rsc", "prefetchRsc", "head", "prefetchHead", "parallelRoutes", "Map", "loading", "navigatedAt", "copyNextJsInternalHistoryState", "data", "currentState", "Head", "headCacheNode", "resolvedPrefetchRsc", "Router", "actionQueue", "assetPrefix", "globalError", "gracefully<PERSON><PERSON><PERSON>", "searchParams", "pathname", "cache", "prefetchCache", "nd", "router", "handlePageShow", "event", "persisted", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "addEventListener", "removeEventListener", "handleUnhandledRedirect", "error", "reason", "preventDefault", "redirectType", "push", "replace", "mpaNavigation", "assign", "originalPushState", "bind", "originalReplaceState", "applyUrlFromHistoryPushReplace", "_unused", "_N", "onPopState", "reload", "focusAndScrollRef", "matchingHead", "pathParams", "layoutRouterContext", "parentTree", "parentCacheNode", "parentSegmentPath", "globalLayoutRouterContext", "head<PERSON><PERSON>", "content", "DevRootHTTPAccessFallbackBoundary", "require", "HotReloader", "default", "errorComponent", "errorStyles", "RuntimeStyles", "Provider", "value", "AppRouter", "globalErrorState", "runtimeStyles", "Set", "runtimeStyleChanged", "globalThis", "_N_E_STYLE_LOAD", "len", "size", "add", "for<PERSON>ach", "cb", "Promise", "resolve", "forceUpdate", "useState", "renderedStylesSize", "changed", "c", "delete", "dplId", "NEXT_DEPLOYMENT_ID", "map", "i", "link", "rel", "precedence"], "mappings": "AAAA;;AAEA,OAAOA,SACLC,SAAS,EACTC,OAAO,EACPC,eAAe,EACfC,kBAAkB,EAClBC,gBAAgB,QACX,QAAO;AACd,SACEC,gBAAgB,EAChBC,mBAAmB,EACnBC,yBAAyB,QACpB,qDAAoD;AAE3D,SAASC,cAAc,QAAQ,wCAAuC;AAEtE,SAASC,iBAAiB,QAAQ,wCAAuC;AACzE,SACEC,mBAAmB,EACnBC,eAAe,EACfC,iBAAiB,QACZ,uDAAsD;AAC7D,SAASC,uBAAuB,EAAEC,cAAc,QAAQ,qBAAoB;AAC5E,SAASC,aAAa,QAAQ,mBAAkB;AAChD,OAAOC,wBAAwB,yBAAwB;AACvD,SAASC,KAAK,QAAQ,uCAAsC;AAC5D,SAASC,WAAW,QAAQ,mBAAkB;AAC9C,SAASC,kBAAkB,QAAQ,yBAAwB;AAC3D,SAASC,gBAAgB,QAAQ,sBAAqB;AACtD,SAASC,eAAe,QAAQ,+CAA8C;AAC9E,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SAASC,cAAc,QAAQ,sBAAqB;AACpD,SAASC,WAAW,QAAQ,mBAAkB;AAC9C,SAASC,iBAAiB,QAAQ,wCAAuC;AAEzE,SAASC,oBAAoB,QAAQ,wBAAuB;AAC5D,SACEC,sBAAsB,EACtBC,uBAAuB,QAGlB,wBAAuB;AAC9B,SAASC,wBAAwB,EAAEC,uBAAuB,QAAQ,aAAY;AAC9E,SAASC,eAAe,EAAEC,YAAY,QAAQ,mBAAkB;AAChE,SAASC,gBAAgB,QAAQ,UAAS;AAC1C,OAAOC,6BAA6B,qCAAoC;AAExE,MAAMC,gBAEF,CAAC;AAEL,OAAO,SAASC,cAAcC,GAAQ;IACpC,OAAOA,IAAIC,MAAM,KAAKC,OAAOC,QAAQ,CAACF,MAAM;AAC9C;AAEA;;;;;;CAMC,GACD,OAAO,SAASG,kBAAkBC,IAAY;IAC5C,kDAAkD;IAClD,IAAIzB,MAAMsB,OAAOI,SAAS,CAACC,SAAS,GAAG;QACrC,OAAO;IACT;IAEA,IAAIP;IACJ,IAAI;QACFA,MAAM,IAAIQ,IAAI3B,YAAYwB,OAAOH,OAAOC,QAAQ,CAACE,IAAI;IACvD,EAAE,OAAOI,GAAG;QACV,2EAA2E;QAC3E,kDAAkD;QAClD,MAAM,qBAEL,CAFK,IAAIC,MACR,AAAC,sBAAmBL,OAAK,+CADrB,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,uEAAuE;IACvE,IAAIM,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,OAAO;IACT;IAEA,qDAAqD;IACrD,IAAId,cAAcC,MAAM;QACtB,OAAO;IACT;IAEA,OAAOA;AACT;AAEA,SAASc,eAAe,KAIvB;IAJuB,IAAA,EACtBC,cAAc,EAGf,GAJuB;IAKtBjD,mBAAmB;QACjB,IAAI6C,QAAQC,GAAG,CAACI,4BAA4B,EAAE;YAC5C,+CAA+C;YAC/C,YAAY;YACZd,OAAOe,IAAI,CAACC,YAAY,GAAGC;QAC7B;QAEA,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAEC,YAAY,EAAE,GAAGP;QACxC,MAAMQ,eAAe;YACnB,GAAIF,QAAQG,0BAA0B,GAAGtB,OAAOuB,OAAO,CAACC,KAAK,GAAG,CAAC,CAAC;YAClE,yCAAyC;YACzC,kFAAkF;YAClF,iFAAiF;YACjFC,MAAM;YACNC,iCAAiCR;QACnC;QACA,IACEC,QAAQQ,WAAW,IACnB,+FAA+F;QAC/F,2DAA2D;QAC3DzD,kBAAkB,IAAIoC,IAAIN,OAAOC,QAAQ,CAACE,IAAI,OAAOiB,cACrD;YACA,qJAAqJ;YACrJD,QAAQQ,WAAW,GAAG;YACtB3B,OAAOuB,OAAO,CAACK,SAAS,CAACP,cAAc,IAAID;QAC7C,OAAO;YACLpB,OAAOuB,OAAO,CAACM,YAAY,CAACR,cAAc,IAAID;QAChD;IACF,GAAG;QAACP;KAAe;IAEnBpD,UAAU;QACR,qEAAqE;QACrE,uEAAuE;QACvE,mEAAmE;QACnE,8DAA8D;QAC9D,IAAIgD,QAAQC,GAAG,CAACoB,2BAA2B,EAAE;YAC3CpC,iBAAiBmB,eAAekB,OAAO,EAAElB,eAAeK,IAAI;QAC9D;IACF,GAAG;QAACL,eAAekB,OAAO;QAAElB,eAAeK,IAAI;KAAC;IAEhD,OAAO;AACT;AAEA,OAAO,SAASc;IACd,OAAO;QACLC,UAAU;QACVC,KAAK;QACLC,aAAa;QACbC,MAAM;QACNC,cAAc;QACdC,gBAAgB,IAAIC;QACpBC,SAAS;QACTC,aAAa,CAAC;IAChB;AACF;AAEA,SAASC,+BAA+BC,IAAS;IAC/C,IAAIA,QAAQ,MAAMA,OAAO,CAAC;IAC1B,MAAMC,eAAe5C,OAAOuB,OAAO,CAACC,KAAK;IACzC,MAAMC,OAAOmB,gCAAAA,aAAcnB,IAAI;IAC/B,IAAIA,MAAM;QACRkB,KAAKlB,IAAI,GAAGA;IACd;IACA,MAAMC,kCACJkB,gCAAAA,aAAclB,+BAA+B;IAC/C,IAAIA,iCAAiC;QACnCiB,KAAKjB,+BAA+B,GAAGA;IACzC;IAEA,OAAOiB;AACT;AAEA,SAASE,KAAK,KAIb;IAJa,IAAA,EACZC,aAAa,EAGd,GAJa;IAKZ,6EAA6E;IAC7E,4EAA4E;IAC5E,kDAAkD;IAClD,MAAMV,OAAOU,kBAAkB,OAAOA,cAAcV,IAAI,GAAG;IAC3D,MAAMC,eACJS,kBAAkB,OAAOA,cAAcT,YAAY,GAAG;IAExD,6EAA6E;IAC7E,MAAMU,sBAAsBV,iBAAiB,OAAOA,eAAeD;IAEnE,2EAA2E;IAC3E,2EAA2E;IAC3E,sCAAsC;IACtC,OAAOvE,iBAAiBuE,MAAMW;AAChC;AAEA;;CAEC,GACD,SAASC,OAAO,KAUf;IAVe,IAAA,EACdC,WAAW,EACXC,WAAW,EACXC,WAAW,EACXC,iBAAiB,EAMlB,GAVe;IAWd,MAAM5B,QAAQjD,eAAe0E;IAC7B,MAAM,EAAE7B,YAAY,EAAE,GAAGI;IACzB,mEAAmE;IACnE,MAAM,EAAE6B,YAAY,EAAEC,QAAQ,EAAE,GAAG5F,QAAQ;QACzC,MAAMoC,MAAM,IAAIQ,IACdc,cACA,OAAOpB,WAAW,cAAc,aAAaA,OAAOC,QAAQ,CAACE,IAAI;QAGnE,OAAO;YACL,4DAA4D;YAC5DkD,cAAcvD,IAAIuD,YAAY;YAC9BC,UAAUrE,YAAYa,IAAIwD,QAAQ,IAC9BtE,eAAec,IAAIwD,QAAQ,IAC3BxD,IAAIwD,QAAQ;QAClB;IACF,GAAG;QAAClC;KAAa;IAEjB,IAAIX,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,sDAAsD;QACtD,MAAM,EAAE4C,KAAK,EAAEC,aAAa,EAAEtC,IAAI,EAAE,GAAGM;QAEvC,4FAA4F;QAC5F,sDAAsD;QACtD/D,UAAU;YACR,0CAA0C;YAC1C,uGAAuG;YACvG,mCAAmC;YACnCuC,OAAOyD,EAAE,GAAG;gBACVC,QAAQrE;gBACRkE;gBACAC;gBACAtC;YACF;QACF,GAAG;YAACqC;YAAOC;YAAetC;SAAK;IACjC;IAEAzD,UAAU;QACR,0DAA0D;QAC1D,uFAAuF;QACvF,qEAAqE;QACrE,wGAAwG;QACxG,SAASkG,eAAeC,KAA0B;gBAG7C5D;YAFH,IACE,CAAC4D,MAAMC,SAAS,IAChB,GAAC7D,wBAAAA,OAAOuB,OAAO,CAACC,KAAK,qBAApBxB,sBAAsB0B,+BAA+B,GACtD;gBACA;YACF;YAEA,uGAAuG;YACvG,qHAAqH;YACrH,8BAA8B;YAC9B9B,cAAckE,cAAc,GAAG7C;YAE/B3C,wBAAwB;gBACtByF,MAAM9F;gBACN6B,KAAK,IAAIQ,IAAIN,OAAOC,QAAQ,CAACE,IAAI;gBACjCe,MAAMlB,OAAOuB,OAAO,CAACC,KAAK,CAACE,+BAA+B;YAC5D;QACF;QAEA1B,OAAOgE,gBAAgB,CAAC,YAAYL;QAEpC,OAAO;YACL3D,OAAOiE,mBAAmB,CAAC,YAAYN;QACzC;IACF,GAAG,EAAE;IAELlG,UAAU;QACR,iFAAiF;QACjF,wCAAwC;QACxC,SAASyG,wBACPN,KAAyC;YAEzC,MAAMO,QAAQ,YAAYP,QAAQA,MAAMQ,MAAM,GAAGR,MAAMO,KAAK;YAC5D,IAAI3E,gBAAgB2E,QAAQ;gBAC1BP,MAAMS,cAAc;gBACpB,MAAMvE,MAAMP,wBAAwB4E;gBACpC,MAAMG,eAAehF,yBAAyB6E;gBAC9C,oEAAoE;gBACpE,mCAAmC;gBACnC,IAAIG,iBAAiB7E,aAAa8E,IAAI,EAAE;oBACtClF,wBAAwBkF,IAAI,CAACzE,KAAK,CAAC;gBACrC,OAAO;oBACLT,wBAAwBmF,OAAO,CAAC1E,KAAK,CAAC;gBACxC;YACF;QACF;QACAE,OAAOgE,gBAAgB,CAAC,SAASE;QACjClE,OAAOgE,gBAAgB,CAAC,sBAAsBE;QAE9C,OAAO;YACLlE,OAAOiE,mBAAmB,CAAC,SAASC;YACpClE,OAAOiE,mBAAmB,CAAC,sBAAsBC;QACnD;IACF,GAAG,EAAE;IAEL,sEAAsE;IACtE,0EAA0E;IAC1E,wEAAwE;IACxE,6EAA6E;IAC7E,YAAY;IACZ,EAAE;IACF,sEAAsE;IACtE,6EAA6E;IAC7E,6EAA6E;IAC7E,uBAAuB;IACvB,MAAM,EAAE/C,OAAO,EAAE,GAAGK;IACpB,IAAIL,QAAQsD,aAAa,EAAE;QACzB,gHAAgH;QAChH,IAAI7E,cAAckE,cAAc,KAAK1C,cAAc;YACjD,MAAMnB,WAAWD,OAAOC,QAAQ;YAChC,IAAIkB,QAAQQ,WAAW,EAAE;gBACvB1B,SAASyE,MAAM,CAACtD;YAClB,OAAO;gBACLnB,SAASuE,OAAO,CAACpD;YACnB;YAEAxB,cAAckE,cAAc,GAAG1C;QACjC;QACA,mEAAmE;QACnE,4EAA4E;QAC5E,+BAA+B;QAC/B,2EAA2E;QAC3E,wEAAwE;QACxE,+BAA+B;QAC/B,MAAMrC;IACR;IAEAtB,UAAU;QACR,MAAMkH,oBAAoB3E,OAAOuB,OAAO,CAACK,SAAS,CAACgD,IAAI,CAAC5E,OAAOuB,OAAO;QACtE,MAAMsD,uBAAuB7E,OAAOuB,OAAO,CAACM,YAAY,CAAC+C,IAAI,CAC3D5E,OAAOuB,OAAO;QAGhB,wJAAwJ;QACxJ,MAAMuD,iCAAiC,CACrChF;gBAIEE;YAFF,MAAMG,OAAOH,OAAOC,QAAQ,CAACE,IAAI;YACjC,MAAMe,QACJlB,wBAAAA,OAAOuB,OAAO,CAACC,KAAK,qBAApBxB,sBAAsB0B,+BAA+B;YAEvD/D,gBAAgB;gBACdW,wBAAwB;oBACtByF,MAAM9F;oBACN6B,KAAK,IAAIQ,IAAIR,cAAAA,MAAOK,MAAMA;oBAC1Be;gBACF;YACF;QACF;QAEA;;;;KAIC,GACDlB,OAAOuB,OAAO,CAACK,SAAS,GAAG,SAASA,UAClCe,IAAS,EACToC,OAAe,EACfjF,GAAyB;YAEzB,qEAAqE;YACrE,IAAI6C,CAAAA,wBAAAA,KAAMlB,IAAI,MAAIkB,wBAAAA,KAAMqC,EAAE,GAAE;gBAC1B,OAAOL,kBAAkBhC,MAAMoC,SAASjF;YAC1C;YAEA6C,OAAOD,+BAA+BC;YAEtC,IAAI7C,KAAK;gBACPgF,+BAA+BhF;YACjC;YAEA,OAAO6E,kBAAkBhC,MAAMoC,SAASjF;QAC1C;QAEA;;;;KAIC,GACDE,OAAOuB,OAAO,CAACM,YAAY,GAAG,SAASA,aACrCc,IAAS,EACToC,OAAe,EACfjF,GAAyB;YAEzB,qEAAqE;YACrE,IAAI6C,CAAAA,wBAAAA,KAAMlB,IAAI,MAAIkB,wBAAAA,KAAMqC,EAAE,GAAE;gBAC1B,OAAOH,qBAAqBlC,MAAMoC,SAASjF;YAC7C;YACA6C,OAAOD,+BAA+BC;YAEtC,IAAI7C,KAAK;gBACPgF,+BAA+BhF;YACjC;YACA,OAAO+E,qBAAqBlC,MAAMoC,SAASjF;QAC7C;QAEA;;;;KAIC,GACD,MAAMmF,aAAa,CAACrB;YAClB,IAAI,CAACA,MAAMpC,KAAK,EAAE;gBAChB,+IAA+I;gBAC/I;YACF;YAEA,6EAA6E;YAC7E,IAAI,CAACoC,MAAMpC,KAAK,CAACC,IAAI,EAAE;gBACrBzB,OAAOC,QAAQ,CAACiF,MAAM;gBACtB;YACF;YAEA,gHAAgH;YAChH,oEAAoE;YACpEvH,gBAAgB;gBACdyB,uBACEY,OAAOC,QAAQ,CAACE,IAAI,EACpByD,MAAMpC,KAAK,CAACE,+BAA+B;YAE/C;QACF;QAEA,8CAA8C;QAC9C1B,OAAOgE,gBAAgB,CAAC,YAAYiB;QACpC,OAAO;YACLjF,OAAOuB,OAAO,CAACK,SAAS,GAAG+C;YAC3B3E,OAAOuB,OAAO,CAACM,YAAY,GAAGgD;YAC9B7E,OAAOiE,mBAAmB,CAAC,YAAYgB;QACzC;IACF,GAAG,EAAE;IAEL,MAAM,EAAE1B,KAAK,EAAErC,IAAI,EAAEa,OAAO,EAAEoD,iBAAiB,EAAE,GAAG3D;IAEpD,MAAM4D,eAAe1H,QAAQ;QAC3B,OAAOoB,gBAAgByE,OAAOrC,IAAI,CAAC,EAAE;IACvC,GAAG;QAACqC;QAAOrC;KAAK;IAEhB,yCAAyC;IACzC,MAAMmE,aAAa3H,QAAQ;QACzB,OAAOwB,kBAAkBgC;IAC3B,GAAG;QAACA;KAAK;IAET,MAAMoE,sBAAsB5H,QAAQ;QAClC,OAAO;YACL6H,YAAYrE;YACZsE,iBAAiBjC;YACjBkC,mBAAmB;YACnB,6BAA6B;YAC7B,8EAA8E;YAC9E3F,KAAKsB;QACP;IACF,GAAG;QAACF;QAAMqC;QAAOnC;KAAa;IAE9B,MAAMsE,4BAA4BhI,QAAQ;QACxC,OAAO;YACLwD;YACAiE;YACApD;QACF;IACF,GAAG;QAACb;QAAMiE;QAAmBpD;KAAQ;IAErC,IAAIK;IACJ,IAAIgD,iBAAiB,MAAM;QACzB,0DAA0D;QAC1D,0EAA0E;QAC1E,oEAAoE;QACpE,EAAE;QACF,wEAAwE;QACxE,uBAAuB;QACvB,MAAM,CAACtC,eAAe6C,QAAQ,GAAGP;QACjChD,qBAAO,KAACS;YAAmBC,eAAeA;WAAxB6C;IACpB,OAAO;QACLvD,OAAO;IACT;IAEA,IAAIwD,wBACF,MAAC/G;;YACEuD;YACAmB,MAAMrB,GAAG;0BACV,KAACtD;gBAAmBsC,MAAMA;;;;IAI9B,IAAIT,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,kEAAkE;QAClE,iGAAiG;QACjG,iBAAiB;QACjB,8CAA8C;QAC9C,wBAAwB;QACxB,kEAAkE;QAClE,IAAI,OAAOX,WAAW,aAAa;YACjC,MAAM,EAAE6F,iCAAiC,EAAE,GACzCC,QAAQ;YACVF,wBACE,KAACC;0BACED;;QAGP;QACA,MAAMG,cACJ,AACED,QAAQ,4CACRE,OAAO;QAEXJ,wBACE,KAACG;YAAY7C,aAAaA;YAAaC,aAAaA;sBACjDyC;;IAGP,OAAO;QACL,oDAAoD;QACpD,qEAAqE;QACrE,IAAIxC,mBAAmB;YACrBwC,wBAAU,KAACjG;0BAAyBiG;;QACtC,OAAO;YACLA,wBACE,KAACpH;gBACCyH,gBAAgB9C,WAAW,CAAC,EAAE;gBAC9B+C,aAAa/C,WAAW,CAAC,EAAE;0BAE1ByC;;QAGP;IACF;IAEA,qBACE;;0BACE,KAAChF;gBAAeC,gBAAgBW;;0BAChC,KAAC2E;0BACD,KAAC9H,kBAAkB+H,QAAQ;gBAACC,OAAOhB;0BACjC,cAAA,KAACjH,gBAAgBgI,QAAQ;oBAACC,OAAO/C;8BAC/B,cAAA,KAACnF,oBAAoBiI,QAAQ;wBAACC,OAAOhD;kCACnC,cAAA,KAACrF,0BAA0BoI,QAAQ;4BACjCC,OAAOX;sCAOP,cAAA,KAAC5H,iBAAiBsI,QAAQ;gCAACC,OAAOhH;0CAChC,cAAA,KAACtB,oBAAoBqI,QAAQ;oCAACC,OAAOf;8CAClCM;;;;;;;;;AASnB;AAEA,eAAe,SAASU,UAAU,KAUjC;IAViC,IAAA,EAChCrD,WAAW,EACXsD,gBAAgB,EAChBrD,WAAW,EACXE,iBAAiB,EAMlB,GAViC;IAWhCjE;IAEA,MAAMuE,uBACJ,KAACV;QACCC,aAAaA;QACbC,aAAaA;QACbC,aAAaoD;QACbnD,mBAAmBA;;IAIvB,IAAIA,mBAAmB;QACrB,OAAOM;IACT,OAAO;QACL,qBACE,KAAClF;YACC,sFAAsF;YACtF,uGAAuG;YACvGyH,gBAAgBxH;sBAEfiF;;IAGP;AACF;AAEA,MAAM8C,gBAAgB,IAAIC;AAC1B,IAAIC,sBAAsB,IAAID;AAE9BE,WAAWC,eAAe,GAAG,SAAUzG,IAAY;IACjD,IAAI0G,MAAML,cAAcM,IAAI;IAC5BN,cAAcO,GAAG,CAAC5G;IAClB,IAAIqG,cAAcM,IAAI,KAAKD,KAAK;QAC9BH,oBAAoBM,OAAO,CAAC,CAACC,KAAOA;IACtC;IACA,4CAA4C;IAC5C,gFAAgF;IAChF,OAAOC,QAAQC,OAAO;AACxB;AAEA,SAAShB;IACP,MAAM,GAAGiB,YAAY,GAAG5J,MAAM6J,QAAQ,CAAC;IACvC,MAAMC,qBAAqBd,cAAcM,IAAI;IAC7CrJ,UAAU;QACR,MAAM8J,UAAU,IAAMH,YAAY,CAACI,IAAMA,IAAI;QAC7Cd,oBAAoBK,GAAG,CAACQ;QACxB,IAAID,uBAAuBd,cAAcM,IAAI,EAAE;YAC7CS;QACF;QACA,OAAO;YACLb,oBAAoBe,MAAM,CAACF;QAC7B;IACF,GAAG;QAACD;QAAoBF;KAAY;IAEpC,MAAMM,QAAQjH,QAAQC,GAAG,CAACiH,kBAAkB,GACxC,AAAC,UAAOlH,QAAQC,GAAG,CAACiH,kBAAkB,GACtC;IACJ,OAAO;WAAInB;KAAc,CAACoB,GAAG,CAAC,CAACzH,MAAM0H,kBACnC,KAACC;YAECC,KAAI;YACJ5H,MAAM,AAAC,KAAEA,OAAOuH;YAChB,aAAa;YACbM,YAAW;WAJNH;AAUX", "ignoreList": [0]}