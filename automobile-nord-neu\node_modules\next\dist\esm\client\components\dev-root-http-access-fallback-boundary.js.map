{"version": 3, "sources": ["../../../src/client/components/dev-root-http-access-fallback-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { HTTPAccessFallbackBoundary } from './http-access-fallback/error-boundary'\n\n// TODO: error on using forbidden and unauthorized in root layout\nexport function bailOnRootNotFound() {\n  throw new Error('notFound() is not allowed to use in root layout')\n}\n\nfunction NotAllowedRootHTTPFallbackError() {\n  bailOnRootNotFound()\n  return null\n}\n\nexport function DevRootHTTPAccessFallbackBoundary({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return (\n    <HTTPAccessFallbackBoundary notFound={<NotAllowedRootHTTPFallbackError />}>\n      {children}\n    </HTTPAccessFallbackBoundary>\n  )\n}\n"], "names": ["React", "HTTPAccessFallbackBoundary", "bailOnRootNotFound", "Error", "NotAllowedRootHTTPFallbackError", "DevRootHTTPAccessFallbackBoundary", "children", "notFound"], "mappings": "AAAA;;AAEA,OAAOA,WAAW,QAAO;AACzB,SAASC,0BAA0B,QAAQ,wCAAuC;AAElF,iEAAiE;AACjE,OAAO,SAASC;IACd,MAAM,qBAA4D,CAA5D,IAAIC,MAAM,oDAAV,qBAAA;eAAA;oBAAA;sBAAA;IAA2D;AACnE;AAEA,SAASC;IACPF;IACA,OAAO;AACT;AAEA,OAAO,SAASG,kCAAkC,KAIjD;IAJiD,IAAA,EAChDC,QAAQ,EAGT,GAJiD;IAKhD,qBACE,KAACL;QAA2BM,wBAAU,KAACH;kBACpCE;;AAGP", "ignoreList": [0]}