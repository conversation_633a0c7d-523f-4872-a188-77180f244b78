{"version": 3, "sources": ["../../src/lib/format-dynamic-import-path.ts"], "sourcesContent": ["import path from 'path'\nimport { pathToFileURL } from 'url'\n\n/**\n * The path for a dynamic route must be URLs with a valid scheme.\n *\n * When an absolute Windows path is passed to it, it interprets the beginning of the path as a protocol (`C:`).\n * Therefore, it is important to always construct a complete path.\n * @param dir File directory\n * @param filePath Absolute or relative path\n */\nexport const formatDynamicImportPath = (dir: string, filePath: string) => {\n  const absoluteFilePath = path.isAbsolute(filePath)\n    ? filePath\n    : path.join(dir, filePath)\n  const formattedFilePath = pathToFileURL(absoluteFilePath).toString()\n\n  return formattedFilePath\n}\n"], "names": ["path", "pathToFileURL", "formatDynamicImportPath", "dir", "filePath", "absoluteFilePath", "isAbsolute", "join", "formattedFilePath", "toString"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,SAASC,aAAa,QAAQ,MAAK;AAEnC;;;;;;;CAOC,GACD,OAAO,MAAMC,0BAA0B,CAACC,KAAaC;IACnD,MAAMC,mBAAmBL,KAAKM,UAAU,CAACF,YACrCA,WACAJ,KAAKO,IAAI,CAACJ,KAAKC;IACnB,MAAMI,oBAAoBP,cAAcI,kBAAkBI,QAAQ;IAElE,OAAOD;AACT,EAAC", "ignoreList": [0]}