{"version": 3, "sources": ["../../../../src/next-devtools/userspace/app/app-dev-overlay-error-boundary.tsx"], "sourcesContent": ["import { PureComponent } from 'react'\nimport { dispatcher } from 'next/dist/compiled/next-devtools'\nimport { RuntimeErrorHandler } from '../../../client/dev/runtime-error-handler'\nimport { ErrorBoundary } from '../../../client/components/error-boundary'\nimport DefaultGlobalError from '../../../client/components/builtin/global-error'\nimport type { GlobalErrorState } from '../../../client/components/app-router-instance'\nimport { SEGMENT_EXPLORER_SIMULATED_ERROR_MESSAGE } from './segment-explorer-node'\n\ntype AppDevOverlayErrorBoundaryProps = {\n  children: React.ReactNode\n  globalError: GlobalErrorState\n}\n\ntype AppDevOverlayErrorBoundaryState = {\n  reactError: unknown\n}\n\nfunction ErroredHtml({\n  globalError: [GlobalError, globalErrorStyles],\n  error,\n}: {\n  globalError: GlobalErrorState\n  error: unknown\n}) {\n  if (!error) {\n    return (\n      <html>\n        <head />\n        <body />\n      </html>\n    )\n  }\n  return (\n    <ErrorBoundary errorComponent={DefaultGlobalError}>\n      {globalErrorStyles}\n      <GlobalError error={error} />\n    </ErrorBoundary>\n  )\n}\n\nexport class AppDevOverlayErrorBoundary extends PureComponent<\n  AppDevOverlayErrorBoundaryProps,\n  AppDevOverlayErrorBoundaryState\n> {\n  state = { reactError: null }\n\n  static getDerivedStateFromError(error: Error) {\n    RuntimeErrorHandler.hadRuntimeError = true\n\n    return {\n      reactError: error,\n    }\n  }\n\n  componentDidCatch(err: Error) {\n    if (\n      process.env.NODE_ENV === 'development' &&\n      err.message === SEGMENT_EXPLORER_SIMULATED_ERROR_MESSAGE\n    ) {\n      return\n    }\n    dispatcher.openErrorOverlay()\n  }\n\n  render() {\n    const { children, globalError } = this.props\n    const { reactError } = this.state\n\n    const fallback = (\n      <ErroredHtml globalError={globalError} error={reactError} />\n    )\n\n    return reactError !== null ? fallback : children\n  }\n}\n"], "names": ["PureComponent", "dispatcher", "RuntimeError<PERSON>andler", "Error<PERSON>ou<PERSON><PERSON>", "DefaultGlobalError", "SEGMENT_EXPLORER_SIMULATED_ERROR_MESSAGE", "ErroredHtml", "globalError", "GlobalError", "globalErrorStyles", "error", "html", "head", "body", "errorComponent", "AppDevOverlayErrorBoundary", "getDerivedStateFromError", "hadRuntimeError", "reactError", "componentDidCatch", "err", "process", "env", "NODE_ENV", "message", "openErrorOverlay", "render", "children", "props", "state", "fallback"], "mappings": ";AAAA,SAASA,aAAa,QAAQ,QAAO;AACrC,SAASC,UAAU,QAAQ,mCAAkC;AAC7D,SAASC,mBAAmB,QAAQ,4CAA2C;AAC/E,SAASC,aAAa,QAAQ,4CAA2C;AACzE,OAAOC,wBAAwB,kDAAiD;AAEhF,SAASC,wCAAwC,QAAQ,0BAAyB;AAWlF,SAASC,YAAY,KAMpB;IANoB,IAAA,EACnBC,aAAa,CAACC,aAAaC,kBAAkB,EAC7CC,KAAK,EAIN,GANoB;IAOnB,IAAI,CAACA,OAAO;QACV,qBACE,MAACC;;8BACC,KAACC;8BACD,KAACC;;;IAGP;IACA,qBACE,MAACV;QAAcW,gBAAgBV;;YAC5BK;0BACD,KAACD;gBAAYE,OAAOA;;;;AAG1B;AAEA,OAAO,MAAMK,mCAAmCf;IAM9C,OAAOgB,yBAAyBN,KAAY,EAAE;QAC5CR,oBAAoBe,eAAe,GAAG;QAEtC,OAAO;YACLC,YAAYR;QACd;IACF;IAEAS,kBAAkBC,GAAU,EAAE;QAC5B,IACEC,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBH,IAAII,OAAO,KAAKnB,0CAChB;YACA;QACF;QACAJ,WAAWwB,gBAAgB;IAC7B;IAEAC,SAAS;QACP,MAAM,EAAEC,QAAQ,EAAEpB,WAAW,EAAE,GAAG,IAAI,CAACqB,KAAK;QAC5C,MAAM,EAAEV,UAAU,EAAE,GAAG,IAAI,CAACW,KAAK;QAEjC,MAAMC,yBACJ,KAACxB;YAAYC,aAAaA;YAAaG,OAAOQ;;QAGhD,OAAOA,eAAe,OAAOY,WAAWH;IAC1C;;QAjCK,qBAILE,QAAQ;YAAEX,YAAY;QAAK;;AA8B7B", "ignoreList": [0]}