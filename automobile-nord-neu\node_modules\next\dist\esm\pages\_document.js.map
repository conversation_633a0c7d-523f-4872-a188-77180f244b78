{"version": 3, "sources": ["../../src/pages/_document.tsx"], "sourcesContent": ["/// <reference types=\"webpack/module.d.ts\" />\n\nimport React, { type JSX } from 'react'\nimport { NEXT_BUILTIN_DOCUMENT } from '../shared/lib/constants'\nimport type {\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps,\n  DocumentType,\n  NEXT_DATA,\n} from '../shared/lib/utils'\nimport type { ScriptProps } from '../client/script'\nimport type { NextFontManifest } from '../build/webpack/plugins/next-font-manifest-plugin'\n\nimport { getPageFiles } from '../server/get-page-files'\nimport type { BuildManifest } from '../server/get-page-files'\nimport { htmlEscapeJsonString } from '../server/htmlescape'\nimport isError from '../lib/is-error'\n\nimport {\n  HtmlContext,\n  useHtmlContext,\n} from '../shared/lib/html-context.shared-runtime'\nimport type { HtmlProps } from '../shared/lib/html-context.shared-runtime'\nimport { encodeURIPath } from '../shared/lib/encode-uri-path'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport { getTracer } from '../server/lib/trace/tracer'\nimport { getTracedMetadata } from '../server/lib/trace/utils'\n\nexport type { DocumentContext, DocumentInitialProps, DocumentProps }\n\nexport type OriginProps = {\n  nonce?: string\n  crossOrigin?: 'anonymous' | 'use-credentials' | '' | undefined\n  children?: React.ReactNode\n}\n\ntype DocumentFiles = {\n  sharedFiles: readonly string[]\n  pageFiles: readonly string[]\n  allFiles: readonly string[]\n}\n\ntype HeadHTMLProps = React.DetailedHTMLProps<\n  React.HTMLAttributes<HTMLHeadElement>,\n  HTMLHeadElement\n>\n\ntype HeadProps = OriginProps & HeadHTMLProps\n\n/** Set of pages that have triggered a large data warning on production mode. */\nconst largePageDataWarnings = new Set<string>()\n\nfunction getDocumentFiles(\n  buildManifest: BuildManifest,\n  pathname: string,\n  inAmpMode: boolean\n): DocumentFiles {\n  const sharedFiles: readonly string[] = getPageFiles(buildManifest, '/_app')\n  const pageFiles: readonly string[] =\n    process.env.NEXT_RUNTIME !== 'edge' && inAmpMode\n      ? []\n      : getPageFiles(buildManifest, pathname)\n\n  return {\n    sharedFiles,\n    pageFiles,\n    allFiles: [...new Set([...sharedFiles, ...pageFiles])],\n  }\n}\n\nfunction getPolyfillScripts(context: HtmlProps, props: OriginProps) {\n  // polyfills.js has to be rendered as nomodule without async\n  // It also has to be the first script to load\n  const {\n    assetPrefix,\n    buildManifest,\n    assetQueryString,\n    disableOptimizedLoading,\n    crossOrigin,\n  } = context\n\n  return buildManifest.polyfillFiles\n    .filter(\n      (polyfill) => polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n    )\n    .map((polyfill) => (\n      <script\n        key={polyfill}\n        defer={!disableOptimizedLoading}\n        nonce={props.nonce}\n        crossOrigin={props.crossOrigin || crossOrigin}\n        noModule={true}\n        src={`${assetPrefix}/_next/${encodeURIPath(\n          polyfill\n        )}${assetQueryString}`}\n      />\n    ))\n}\n\nfunction hasComponentProps(child: any): child is React.ReactElement<any> {\n  return !!child && !!child.props\n}\n\nfunction AmpStyles({\n  styles,\n}: {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode>\n}) {\n  if (!styles) return null\n\n  // try to parse styles from fragment for backwards compat\n  const curStyles: React.ReactElement<any>[] = Array.isArray(styles)\n    ? (styles as React.ReactElement[])\n    : []\n  if (\n    // @ts-ignore Property 'props' does not exist on type ReactElement\n    styles.props &&\n    // @ts-ignore Property 'props' does not exist on type ReactElement\n    Array.isArray(styles.props.children)\n  ) {\n    const hasStyles = (el: React.ReactElement<any>) =>\n      el?.props?.dangerouslySetInnerHTML?.__html\n    // @ts-ignore Property 'props' does not exist on type ReactElement\n    styles.props.children.forEach((child: React.ReactElement) => {\n      if (Array.isArray(child)) {\n        child.forEach((el) => hasStyles(el) && curStyles.push(el))\n      } else if (hasStyles(child)) {\n        curStyles.push(child)\n      }\n    })\n  }\n\n  /* Add custom styles before AMP styles to prevent accidental overrides */\n  return (\n    <style\n      amp-custom=\"\"\n      dangerouslySetInnerHTML={{\n        __html: curStyles\n          .map((style) => style.props.dangerouslySetInnerHTML.__html)\n          .join('')\n          .replace(/\\/\\*# sourceMappingURL=.*\\*\\//g, '')\n          .replace(/\\/\\*@ sourceURL=.*?\\*\\//g, ''),\n      }}\n    />\n  )\n}\n\nfunction getDynamicChunks(\n  context: HtmlProps,\n  props: OriginProps,\n  files: DocumentFiles\n) {\n  const {\n    dynamicImports,\n    assetPrefix,\n    isDevelopment,\n    assetQueryString,\n    disableOptimizedLoading,\n    crossOrigin,\n  } = context\n\n  return dynamicImports.map((file) => {\n    if (!file.endsWith('.js') || files.allFiles.includes(file)) return null\n\n    return (\n      <script\n        async={!isDevelopment && disableOptimizedLoading}\n        defer={!disableOptimizedLoading}\n        key={file}\n        src={`${assetPrefix}/_next/${encodeURIPath(file)}${assetQueryString}`}\n        nonce={props.nonce}\n        crossOrigin={props.crossOrigin || crossOrigin}\n      />\n    )\n  })\n}\n\nfunction getScripts(\n  context: HtmlProps,\n  props: OriginProps,\n  files: DocumentFiles\n) {\n  const {\n    assetPrefix,\n    buildManifest,\n    isDevelopment,\n    assetQueryString,\n    disableOptimizedLoading,\n    crossOrigin,\n  } = context\n\n  const normalScripts = files.allFiles.filter((file) => file.endsWith('.js'))\n  const lowPriorityScripts = buildManifest.lowPriorityFiles?.filter((file) =>\n    file.endsWith('.js')\n  )\n\n  return [...normalScripts, ...lowPriorityScripts].map((file) => {\n    return (\n      <script\n        key={file}\n        src={`${assetPrefix}/_next/${encodeURIPath(file)}${assetQueryString}`}\n        nonce={props.nonce}\n        async={!isDevelopment && disableOptimizedLoading}\n        defer={!disableOptimizedLoading}\n        crossOrigin={props.crossOrigin || crossOrigin}\n      />\n    )\n  })\n}\n\nfunction getPreNextWorkerScripts(context: HtmlProps, props: OriginProps) {\n  const { assetPrefix, scriptLoader, crossOrigin, nextScriptWorkers } = context\n\n  // disable `nextScriptWorkers` in edge runtime\n  if (!nextScriptWorkers || process.env.NEXT_RUNTIME === 'edge') return null\n\n  try {\n    // @ts-expect-error: Prevent webpack from processing this require\n    let { partytownSnippet } = __non_webpack_require__(\n      '@builder.io/partytown/integration'!\n    )\n\n    const children = Array.isArray(props.children)\n      ? props.children\n      : [props.children]\n\n    // Check to see if the user has defined their own Partytown configuration\n    const userDefinedConfig = children.find(\n      (child) =>\n        hasComponentProps(child) &&\n        child?.props?.dangerouslySetInnerHTML?.__html.length &&\n        'data-partytown-config' in child.props\n    )\n\n    return (\n      <>\n        {!userDefinedConfig && (\n          <script\n            data-partytown-config=\"\"\n            dangerouslySetInnerHTML={{\n              __html: `\n            partytown = {\n              lib: \"${assetPrefix}/_next/static/~partytown/\"\n            };\n          `,\n            }}\n          />\n        )}\n        <script\n          data-partytown=\"\"\n          dangerouslySetInnerHTML={{\n            __html: partytownSnippet(),\n          }}\n        />\n        {(scriptLoader.worker || []).map((file: ScriptProps, index: number) => {\n          const {\n            strategy,\n            src,\n            children: scriptChildren,\n            dangerouslySetInnerHTML,\n            ...scriptProps\n          } = file\n\n          let srcProps: {\n            src?: string\n            dangerouslySetInnerHTML?: ScriptProps['dangerouslySetInnerHTML']\n          } = {}\n\n          if (src) {\n            // Use external src if provided\n            srcProps.src = src\n          } else if (\n            dangerouslySetInnerHTML &&\n            dangerouslySetInnerHTML.__html\n          ) {\n            // Embed inline script if provided with dangerouslySetInnerHTML\n            srcProps.dangerouslySetInnerHTML = {\n              __html: dangerouslySetInnerHTML.__html,\n            }\n          } else if (scriptChildren) {\n            // Embed inline script if provided with children\n            srcProps.dangerouslySetInnerHTML = {\n              __html:\n                typeof scriptChildren === 'string'\n                  ? scriptChildren\n                  : Array.isArray(scriptChildren)\n                    ? scriptChildren.join('')\n                    : '',\n            }\n          } else {\n            throw new Error(\n              'Invalid usage of next/script. Did you forget to include a src attribute or an inline script? https://nextjs.org/docs/messages/invalid-script'\n            )\n          }\n\n          return (\n            <script\n              {...srcProps}\n              {...scriptProps}\n              type=\"text/partytown\"\n              key={src || index}\n              nonce={props.nonce}\n              data-nscript=\"worker\"\n              crossOrigin={props.crossOrigin || crossOrigin}\n            />\n          )\n        })}\n      </>\n    )\n  } catch (err) {\n    if (isError(err) && err.code !== 'MODULE_NOT_FOUND') {\n      console.warn(`Warning: ${err.message}`)\n    }\n    return null\n  }\n}\n\nfunction getPreNextScripts(context: HtmlProps, props: OriginProps) {\n  const { scriptLoader, disableOptimizedLoading, crossOrigin } = context\n\n  const webWorkerScripts = getPreNextWorkerScripts(context, props)\n\n  const beforeInteractiveScripts = (scriptLoader.beforeInteractive || [])\n    .filter((script) => script.src)\n    .map((file: ScriptProps, index: number) => {\n      const { strategy, ...scriptProps } = file\n      return (\n        <script\n          {...scriptProps}\n          key={scriptProps.src || index}\n          defer={scriptProps.defer ?? !disableOptimizedLoading}\n          nonce={scriptProps.nonce || props.nonce}\n          data-nscript=\"beforeInteractive\"\n          crossOrigin={props.crossOrigin || crossOrigin}\n        />\n      )\n    })\n\n  return (\n    <>\n      {webWorkerScripts}\n      {beforeInteractiveScripts}\n    </>\n  )\n}\n\nfunction getHeadHTMLProps(props: HeadProps) {\n  const { crossOrigin, nonce, ...restProps } = props\n\n  // This assignment is necessary for additional type checking to avoid unsupported attributes in <head>\n  const headProps: HeadHTMLProps & {\n    [P in Exclude<keyof HeadProps, keyof HeadHTMLProps>]?: never\n  } = restProps\n\n  return headProps\n}\n\nfunction getAmpPath(ampPath: string, asPath: string): string {\n  return ampPath || `${asPath}${asPath.includes('?') ? '&' : '?'}amp=1`\n}\n\nfunction getNextFontLinkTags(\n  nextFontManifest: DeepReadonly<NextFontManifest> | undefined,\n  dangerousAsPath: string,\n  assetPrefix: string = ''\n) {\n  if (!nextFontManifest) {\n    return {\n      preconnect: null,\n      preload: null,\n    }\n  }\n\n  const appFontsEntry = nextFontManifest.pages['/_app']\n  const pageFontsEntry = nextFontManifest.pages[dangerousAsPath]\n\n  const preloadedFontFiles = Array.from(\n    new Set([...(appFontsEntry ?? []), ...(pageFontsEntry ?? [])])\n  )\n\n  // If no font files should preload but there's an entry for the path, add a preconnect tag.\n  const preconnectToSelf = !!(\n    preloadedFontFiles.length === 0 &&\n    (appFontsEntry || pageFontsEntry)\n  )\n\n  return {\n    preconnect: preconnectToSelf ? (\n      <link\n        data-next-font={\n          nextFontManifest.pagesUsingSizeAdjust ? 'size-adjust' : ''\n        }\n        rel=\"preconnect\"\n        href=\"/\"\n        crossOrigin=\"anonymous\"\n      />\n    ) : null,\n    preload: preloadedFontFiles\n      ? preloadedFontFiles.map((fontFile) => {\n          const ext = /\\.(woff|woff2|eot|ttf|otf)$/.exec(fontFile)![1]\n          return (\n            <link\n              key={fontFile}\n              rel=\"preload\"\n              href={`${assetPrefix}/_next/${encodeURIPath(fontFile)}`}\n              as=\"font\"\n              type={`font/${ext}`}\n              crossOrigin=\"anonymous\"\n              data-next-font={fontFile.includes('-s') ? 'size-adjust' : ''}\n            />\n          )\n        })\n      : null,\n  }\n}\n\n// Use `React.Component` to avoid errors from the RSC checks because\n// it can't be imported directly in Server Components:\n//\n//   import { Component } from 'react'\n//\n// More info: https://github.com/vercel/next.js/pull/40686\nexport class Head extends React.Component<HeadProps> {\n  static contextType = HtmlContext\n\n  context!: HtmlProps\n\n  getCssLinks(files: DocumentFiles): JSX.Element[] | null {\n    const {\n      assetPrefix,\n      assetQueryString,\n      dynamicImports,\n      dynamicCssManifest,\n      crossOrigin,\n      optimizeCss,\n    } = this.context\n    const cssFiles = files.allFiles.filter((f) => f.endsWith('.css'))\n    const sharedFiles: Set<string> = new Set(files.sharedFiles)\n\n    // Unmanaged files are CSS files that will be handled directly by the\n    // webpack runtime (`mini-css-extract-plugin`).\n    let unmanagedFiles: Set<string> = new Set([])\n    let localDynamicCssFiles = Array.from(\n      new Set(dynamicImports.filter((file) => file.endsWith('.css')))\n    )\n    if (localDynamicCssFiles.length) {\n      const existing = new Set(cssFiles)\n      localDynamicCssFiles = localDynamicCssFiles.filter(\n        (f) => !(existing.has(f) || sharedFiles.has(f))\n      )\n      unmanagedFiles = new Set(localDynamicCssFiles)\n      cssFiles.push(...localDynamicCssFiles)\n    }\n\n    let cssLinkElements: JSX.Element[] = []\n    cssFiles.forEach((file) => {\n      const isSharedFile = sharedFiles.has(file)\n      const isUnmanagedFile = unmanagedFiles.has(file)\n      const isFileInDynamicCssManifest = dynamicCssManifest.has(file)\n\n      if (!optimizeCss) {\n        cssLinkElements.push(\n          <link\n            key={`${file}-preload`}\n            nonce={this.props.nonce}\n            rel=\"preload\"\n            href={`${assetPrefix}/_next/${encodeURIPath(\n              file\n            )}${assetQueryString}`}\n            as=\"style\"\n            crossOrigin={this.props.crossOrigin || crossOrigin}\n          />\n        )\n      }\n\n      cssLinkElements.push(\n        <link\n          key={file}\n          nonce={this.props.nonce}\n          rel=\"stylesheet\"\n          href={`${assetPrefix}/_next/${encodeURIPath(\n            file\n          )}${assetQueryString}`}\n          crossOrigin={this.props.crossOrigin || crossOrigin}\n          data-n-g={isUnmanagedFile ? undefined : isSharedFile ? '' : undefined}\n          data-n-p={\n            isSharedFile || isUnmanagedFile || isFileInDynamicCssManifest\n              ? undefined\n              : ''\n          }\n        />\n      )\n    })\n\n    return cssLinkElements.length === 0 ? null : cssLinkElements\n  }\n\n  getPreloadDynamicChunks() {\n    const { dynamicImports, assetPrefix, assetQueryString, crossOrigin } =\n      this.context\n\n    return (\n      dynamicImports\n        .map((file) => {\n          if (!file.endsWith('.js')) {\n            return null\n          }\n\n          return (\n            <link\n              rel=\"preload\"\n              key={file}\n              href={`${assetPrefix}/_next/${encodeURIPath(\n                file\n              )}${assetQueryString}`}\n              as=\"script\"\n              nonce={this.props.nonce}\n              crossOrigin={this.props.crossOrigin || crossOrigin}\n            />\n          )\n        })\n        // Filter out nulled scripts\n        .filter(Boolean)\n    )\n  }\n\n  getPreloadMainLinks(files: DocumentFiles): JSX.Element[] | null {\n    const { assetPrefix, assetQueryString, scriptLoader, crossOrigin } =\n      this.context\n    const preloadFiles = files.allFiles.filter((file: string) => {\n      return file.endsWith('.js')\n    })\n\n    return [\n      ...(scriptLoader.beforeInteractive || []).map((file) => (\n        <link\n          key={file.src}\n          nonce={this.props.nonce}\n          rel=\"preload\"\n          href={file.src}\n          as=\"script\"\n          crossOrigin={this.props.crossOrigin || crossOrigin}\n        />\n      )),\n      ...preloadFiles.map((file: string) => (\n        <link\n          key={file}\n          nonce={this.props.nonce}\n          rel=\"preload\"\n          href={`${assetPrefix}/_next/${encodeURIPath(\n            file\n          )}${assetQueryString}`}\n          as=\"script\"\n          crossOrigin={this.props.crossOrigin || crossOrigin}\n        />\n      )),\n    ]\n  }\n\n  getBeforeInteractiveInlineScripts() {\n    const { scriptLoader } = this.context\n    const { nonce, crossOrigin } = this.props\n\n    return (scriptLoader.beforeInteractive || [])\n      .filter(\n        (script) =>\n          !script.src && (script.dangerouslySetInnerHTML || script.children)\n      )\n      .map((file: ScriptProps, index: number) => {\n        const {\n          strategy,\n          children,\n          dangerouslySetInnerHTML,\n          src,\n          ...scriptProps\n        } = file\n        let html: NonNullable<\n          ScriptProps['dangerouslySetInnerHTML']\n        >['__html'] = ''\n\n        if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n          html = dangerouslySetInnerHTML.__html\n        } else if (children) {\n          html =\n            typeof children === 'string'\n              ? children\n              : Array.isArray(children)\n                ? children.join('')\n                : ''\n        }\n\n        return (\n          <script\n            {...scriptProps}\n            dangerouslySetInnerHTML={{ __html: html }}\n            key={scriptProps.id || index}\n            nonce={nonce}\n            data-nscript=\"beforeInteractive\"\n            crossOrigin={\n              crossOrigin ||\n              (process.env.__NEXT_CROSS_ORIGIN as typeof crossOrigin)\n            }\n          />\n        )\n      })\n  }\n\n  getDynamicChunks(files: DocumentFiles) {\n    return getDynamicChunks(this.context, this.props, files)\n  }\n\n  getPreNextScripts() {\n    return getPreNextScripts(this.context, this.props)\n  }\n\n  getScripts(files: DocumentFiles) {\n    return getScripts(this.context, this.props, files)\n  }\n\n  getPolyfillScripts() {\n    return getPolyfillScripts(this.context, this.props)\n  }\n\n  render() {\n    const {\n      styles,\n      ampPath,\n      inAmpMode,\n      hybridAmp,\n      canonicalBase,\n      __NEXT_DATA__,\n      dangerousAsPath,\n      headTags,\n      unstable_runtimeJS,\n      unstable_JsPreload,\n      disableOptimizedLoading,\n      optimizeCss,\n      assetPrefix,\n      nextFontManifest,\n    } = this.context\n\n    const disableRuntimeJS = unstable_runtimeJS === false\n    const disableJsPreload =\n      unstable_JsPreload === false || !disableOptimizedLoading\n\n    this.context.docComponentsRendered.Head = true\n\n    let { head } = this.context\n    let cssPreloads: Array<JSX.Element> = []\n    let otherHeadElements: Array<JSX.Element> = []\n    if (head) {\n      head.forEach((child) => {\n        if (\n          child &&\n          child.type === 'link' &&\n          child.props['rel'] === 'preload' &&\n          child.props['as'] === 'style'\n        ) {\n          if (this.context.strictNextHead) {\n            cssPreloads.push(\n              React.cloneElement(child, { 'data-next-head': '' })\n            )\n          } else {\n            cssPreloads.push(child)\n          }\n        } else {\n          if (child) {\n            if (this.context.strictNextHead) {\n              otherHeadElements.push(\n                React.cloneElement(child, { 'data-next-head': '' })\n              )\n            } else {\n              otherHeadElements.push(child)\n            }\n          }\n        }\n      })\n      head = cssPreloads.concat(otherHeadElements)\n    }\n    let children: React.ReactNode[] = React.Children.toArray(\n      this.props.children\n    ).filter(Boolean)\n    // show a warning if Head contains <title> (only in development)\n    if (process.env.NODE_ENV !== 'production') {\n      children = React.Children.map(children, (child: any) => {\n        const isReactHelmet = child?.props?.['data-react-helmet']\n        if (!isReactHelmet) {\n          if (child?.type === 'title') {\n            console.warn(\n              \"Warning: <title> should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-title\"\n            )\n          } else if (\n            child?.type === 'meta' &&\n            child?.props?.name === 'viewport'\n          ) {\n            console.warn(\n              \"Warning: viewport meta tags should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-viewport-meta\"\n            )\n          }\n        }\n        return child\n        // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n      })!\n      if (this.props.crossOrigin)\n        console.warn(\n          'Warning: `Head` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated'\n        )\n    }\n\n    let hasAmphtmlRel = false\n    let hasCanonicalRel = false\n\n    // show warning and remove conflicting amp head tags\n    head = React.Children.map(head || [], (child) => {\n      if (!child) return child\n      const { type, props } = child\n      if (process.env.NEXT_RUNTIME !== 'edge' && inAmpMode) {\n        let badProp: string = ''\n\n        if (type === 'meta' && props.name === 'viewport') {\n          badProp = 'name=\"viewport\"'\n        } else if (type === 'link' && props.rel === 'canonical') {\n          hasCanonicalRel = true\n        } else if (type === 'script') {\n          // only block if\n          // 1. it has a src and isn't pointing to ampproject's CDN\n          // 2. it is using dangerouslySetInnerHTML without a type or\n          // a type of text/javascript\n          if (\n            (props.src && props.src.indexOf('ampproject') < -1) ||\n            (props.dangerouslySetInnerHTML &&\n              (!props.type || props.type === 'text/javascript'))\n          ) {\n            badProp = '<script'\n            Object.keys(props).forEach((prop) => {\n              badProp += ` ${prop}=\"${props[prop]}\"`\n            })\n            badProp += '/>'\n          }\n        }\n\n        if (badProp) {\n          console.warn(\n            `Found conflicting amp tag \"${child.type}\" with conflicting prop ${badProp} in ${__NEXT_DATA__.page}. https://nextjs.org/docs/messages/conflicting-amp-tag`\n          )\n          return null\n        }\n      } else {\n        // non-amp mode\n        if (type === 'link' && props.rel === 'amphtml') {\n          hasAmphtmlRel = true\n        }\n      }\n      return child\n      // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n    })!\n\n    const files: DocumentFiles = getDocumentFiles(\n      this.context.buildManifest,\n      this.context.__NEXT_DATA__.page,\n      process.env.NEXT_RUNTIME !== 'edge' && inAmpMode\n    )\n\n    const nextFontLinkTags = getNextFontLinkTags(\n      nextFontManifest,\n      dangerousAsPath,\n      assetPrefix\n    )\n\n    const tracingMetadata = getTracedMetadata(\n      getTracer().getTracePropagationData(),\n      this.context.experimentalClientTraceMetadata\n    )\n\n    const traceMetaTags = (tracingMetadata || []).map(\n      ({ key, value }, index) => (\n        <meta key={`next-trace-data-${index}`} name={key} content={value} />\n      )\n    )\n\n    return (\n      <head {...getHeadHTMLProps(this.props)}>\n        {this.context.isDevelopment && (\n          <>\n            <style\n              data-next-hide-fouc\n              data-ampdevmode={\n                process.env.NEXT_RUNTIME !== 'edge' && inAmpMode\n                  ? 'true'\n                  : undefined\n              }\n              dangerouslySetInnerHTML={{\n                __html: `body{display:none}`,\n              }}\n            />\n            <noscript\n              data-next-hide-fouc\n              data-ampdevmode={\n                process.env.NEXT_RUNTIME !== 'edge' && inAmpMode\n                  ? 'true'\n                  : undefined\n              }\n            >\n              <style\n                dangerouslySetInnerHTML={{\n                  __html: `body{display:block}`,\n                }}\n              />\n            </noscript>\n          </>\n        )}\n        {head}\n        {this.context.strictNextHead ? null : (\n          <meta\n            name=\"next-head-count\"\n            content={React.Children.count(head || []).toString()}\n          />\n        )}\n\n        {children}\n\n        {nextFontLinkTags.preconnect}\n        {nextFontLinkTags.preload}\n\n        {process.env.NEXT_RUNTIME !== 'edge' && inAmpMode && (\n          <>\n            <meta\n              name=\"viewport\"\n              content=\"width=device-width,minimum-scale=1,initial-scale=1\"\n            />\n            {!hasCanonicalRel && (\n              <link\n                rel=\"canonical\"\n                href={\n                  canonicalBase +\n                  (\n                    require('../server/utils') as typeof import('../server/utils')\n                  ).cleanAmpPath(dangerousAsPath)\n                }\n              />\n            )}\n            {/* https://www.ampproject.org/docs/fundamentals/optimize_amp#optimize-the-amp-runtime-loading */}\n            <link\n              rel=\"preload\"\n              as=\"script\"\n              href=\"https://cdn.ampproject.org/v0.js\"\n            />\n            <AmpStyles styles={styles} />\n            <style\n              amp-boilerplate=\"\"\n              dangerouslySetInnerHTML={{\n                __html: `body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`,\n              }}\n            />\n            <noscript>\n              <style\n                amp-boilerplate=\"\"\n                dangerouslySetInnerHTML={{\n                  __html: `body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`,\n                }}\n              />\n            </noscript>\n            <script async src=\"https://cdn.ampproject.org/v0.js\" />\n          </>\n        )}\n        {!(process.env.NEXT_RUNTIME !== 'edge' && inAmpMode) && (\n          <>\n            {!hasAmphtmlRel && hybridAmp && (\n              <link\n                rel=\"amphtml\"\n                href={canonicalBase + getAmpPath(ampPath, dangerousAsPath)}\n              />\n            )}\n            {this.getBeforeInteractiveInlineScripts()}\n            {!optimizeCss && this.getCssLinks(files)}\n            {!optimizeCss && <noscript data-n-css={this.props.nonce ?? ''} />}\n\n            {!disableRuntimeJS &&\n              !disableJsPreload &&\n              this.getPreloadDynamicChunks()}\n            {!disableRuntimeJS &&\n              !disableJsPreload &&\n              this.getPreloadMainLinks(files)}\n\n            {!disableOptimizedLoading &&\n              !disableRuntimeJS &&\n              this.getPolyfillScripts()}\n\n            {!disableOptimizedLoading &&\n              !disableRuntimeJS &&\n              this.getPreNextScripts()}\n            {!disableOptimizedLoading &&\n              !disableRuntimeJS &&\n              this.getDynamicChunks(files)}\n            {!disableOptimizedLoading &&\n              !disableRuntimeJS &&\n              this.getScripts(files)}\n\n            {optimizeCss && this.getCssLinks(files)}\n            {optimizeCss && <noscript data-n-css={this.props.nonce ?? ''} />}\n            {this.context.isDevelopment && (\n              // this element is used to mount development styles so the\n              // ordering matches production\n              // (by default, style-loader injects at the bottom of <head />)\n              <noscript id=\"__next_css__DO_NOT_USE__\" />\n            )}\n            {traceMetaTags}\n            {styles || null}\n          </>\n        )}\n        {React.createElement(React.Fragment, {}, ...(headTags || []))}\n      </head>\n    )\n  }\n}\n\nfunction handleDocumentScriptLoaderItems(\n  scriptLoader: { beforeInteractive?: any[] },\n  __NEXT_DATA__: NEXT_DATA,\n  props: any\n): void {\n  if (!props.children) return\n\n  const scriptLoaderItems: ScriptProps[] = []\n\n  const children = Array.isArray(props.children)\n    ? props.children\n    : [props.children]\n\n  const headChildren = children.find(\n    (child: React.ReactElement) => child.type === Head\n  )?.props?.children\n  const bodyChildren = children.find(\n    (child: React.ReactElement) => child.type === 'body'\n  )?.props?.children\n\n  // Scripts with beforeInteractive can be placed inside Head or <body> so children of both needs to be traversed\n  const combinedChildren = [\n    ...(Array.isArray(headChildren) ? headChildren : [headChildren]),\n    ...(Array.isArray(bodyChildren) ? bodyChildren : [bodyChildren]),\n  ]\n\n  React.Children.forEach(combinedChildren, (child: any) => {\n    if (!child) return\n\n    // When using the `next/script` component, register it in script loader.\n    if (child.type?.__nextScript) {\n      if (child.props.strategy === 'beforeInteractive') {\n        scriptLoader.beforeInteractive = (\n          scriptLoader.beforeInteractive || []\n        ).concat([\n          {\n            ...child.props,\n          },\n        ])\n        return\n      } else if (\n        ['lazyOnload', 'afterInteractive', 'worker'].includes(\n          child.props.strategy\n        )\n      ) {\n        scriptLoaderItems.push(child.props)\n        return\n      } else if (typeof child.props.strategy === 'undefined') {\n        scriptLoaderItems.push({ ...child.props, strategy: 'afterInteractive' })\n        return\n      }\n    }\n  })\n\n  __NEXT_DATA__.scriptLoader = scriptLoaderItems\n}\n\nexport class NextScript extends React.Component<OriginProps> {\n  static contextType = HtmlContext\n\n  context!: HtmlProps\n\n  getDynamicChunks(files: DocumentFiles) {\n    return getDynamicChunks(this.context, this.props, files)\n  }\n\n  getPreNextScripts() {\n    return getPreNextScripts(this.context, this.props)\n  }\n\n  getScripts(files: DocumentFiles) {\n    return getScripts(this.context, this.props, files)\n  }\n\n  getPolyfillScripts() {\n    return getPolyfillScripts(this.context, this.props)\n  }\n\n  static getInlineScriptSource(context: Readonly<HtmlProps>): string {\n    const { __NEXT_DATA__, largePageDataBytes } = context\n    try {\n      const data = JSON.stringify(__NEXT_DATA__)\n\n      if (largePageDataWarnings.has(__NEXT_DATA__.page)) {\n        return htmlEscapeJsonString(data)\n      }\n\n      const bytes =\n        process.env.NEXT_RUNTIME === 'edge'\n          ? new TextEncoder().encode(data).buffer.byteLength\n          : Buffer.from(data).byteLength\n      const prettyBytes = (\n        require('../lib/pretty-bytes') as typeof import('../lib/pretty-bytes')\n      ).default\n\n      if (largePageDataBytes && bytes > largePageDataBytes) {\n        if (process.env.NODE_ENV === 'production') {\n          largePageDataWarnings.add(__NEXT_DATA__.page)\n        }\n\n        console.warn(\n          `Warning: data for page \"${__NEXT_DATA__.page}\"${\n            __NEXT_DATA__.page === context.dangerousAsPath\n              ? ''\n              : ` (path \"${context.dangerousAsPath}\")`\n          } is ${prettyBytes(\n            bytes\n          )} which exceeds the threshold of ${prettyBytes(\n            largePageDataBytes\n          )}, this amount of data can reduce performance.\\nSee more info here: https://nextjs.org/docs/messages/large-page-data`\n        )\n      }\n\n      return htmlEscapeJsonString(data)\n    } catch (err) {\n      if (isError(err) && err.message.indexOf('circular structure') !== -1) {\n        throw new Error(\n          `Circular structure in \"getInitialProps\" result of page \"${__NEXT_DATA__.page}\". https://nextjs.org/docs/messages/circular-structure`\n        )\n      }\n      throw err\n    }\n  }\n\n  render() {\n    const {\n      assetPrefix,\n      inAmpMode,\n      buildManifest,\n      unstable_runtimeJS,\n      docComponentsRendered,\n      assetQueryString,\n      disableOptimizedLoading,\n      crossOrigin,\n    } = this.context\n    const disableRuntimeJS = unstable_runtimeJS === false\n\n    docComponentsRendered.NextScript = true\n\n    if (process.env.NEXT_RUNTIME !== 'edge' && inAmpMode) {\n      if (process.env.NODE_ENV === 'production') {\n        return null\n      }\n      const ampDevFiles = [\n        ...buildManifest.devFiles,\n        ...buildManifest.polyfillFiles,\n        ...buildManifest.ampDevFiles,\n      ]\n\n      return (\n        <>\n          {disableRuntimeJS ? null : (\n            <script\n              id=\"__NEXT_DATA__\"\n              type=\"application/json\"\n              nonce={this.props.nonce}\n              crossOrigin={this.props.crossOrigin || crossOrigin}\n              dangerouslySetInnerHTML={{\n                __html: NextScript.getInlineScriptSource(this.context),\n              }}\n              data-ampdevmode\n            />\n          )}\n          {ampDevFiles.map((file) => (\n            <script\n              key={file}\n              src={`${assetPrefix}/_next/${encodeURIPath(\n                file\n              )}${assetQueryString}`}\n              nonce={this.props.nonce}\n              crossOrigin={this.props.crossOrigin || crossOrigin}\n              data-ampdevmode\n            />\n          ))}\n        </>\n      )\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (this.props.crossOrigin)\n        console.warn(\n          'Warning: `NextScript` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated'\n        )\n    }\n\n    const files: DocumentFiles = getDocumentFiles(\n      this.context.buildManifest,\n      this.context.__NEXT_DATA__.page,\n      process.env.NEXT_RUNTIME !== 'edge' && inAmpMode\n    )\n\n    return (\n      <>\n        {!disableRuntimeJS && buildManifest.devFiles\n          ? buildManifest.devFiles.map((file: string) => (\n              <script\n                key={file}\n                src={`${assetPrefix}/_next/${encodeURIPath(\n                  file\n                )}${assetQueryString}`}\n                nonce={this.props.nonce}\n                crossOrigin={this.props.crossOrigin || crossOrigin}\n              />\n            ))\n          : null}\n        {disableRuntimeJS ? null : (\n          <script\n            id=\"__NEXT_DATA__\"\n            type=\"application/json\"\n            nonce={this.props.nonce}\n            crossOrigin={this.props.crossOrigin || crossOrigin}\n            dangerouslySetInnerHTML={{\n              __html: NextScript.getInlineScriptSource(this.context),\n            }}\n          />\n        )}\n        {disableOptimizedLoading &&\n          !disableRuntimeJS &&\n          this.getPolyfillScripts()}\n        {disableOptimizedLoading &&\n          !disableRuntimeJS &&\n          this.getPreNextScripts()}\n        {disableOptimizedLoading &&\n          !disableRuntimeJS &&\n          this.getDynamicChunks(files)}\n        {disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files)}\n      </>\n    )\n  }\n}\n\nexport function Html(\n  props: React.DetailedHTMLProps<\n    React.HtmlHTMLAttributes<HTMLHtmlElement>,\n    HTMLHtmlElement\n  >\n) {\n  const {\n    inAmpMode,\n    docComponentsRendered,\n    locale,\n    scriptLoader,\n    __NEXT_DATA__,\n  } = useHtmlContext()\n\n  docComponentsRendered.Html = true\n  handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props)\n\n  return (\n    <html\n      {...props}\n      lang={props.lang || locale || undefined}\n      amp={process.env.NEXT_RUNTIME !== 'edge' && inAmpMode ? '' : undefined}\n      data-ampdevmode={\n        process.env.NEXT_RUNTIME !== 'edge' &&\n        inAmpMode &&\n        process.env.NODE_ENV !== 'production'\n          ? ''\n          : undefined\n      }\n    />\n  )\n}\n\nexport function Main() {\n  const { docComponentsRendered } = useHtmlContext()\n  docComponentsRendered.Main = true\n  // @ts-ignore\n  return <next-js-internal-body-render-target />\n}\n\n/**\n * `Document` component handles the initial `document` markup and renders only on the server side.\n * Commonly used for implementing server side rendering for `css-in-js` libraries.\n */\nexport default class Document<P = {}> extends React.Component<\n  DocumentProps & P\n> {\n  /**\n   * `getInitialProps` hook returns the context object with the addition of `renderPage`.\n   * `renderPage` callback executes `React` rendering logic synchronously to support server-rendering wrappers\n   */\n  static getInitialProps(ctx: DocumentContext): Promise<DocumentInitialProps> {\n    return ctx.defaultGetInitialProps(ctx)\n  }\n\n  render() {\n    return (\n      <Html>\n        <Head nonce={this.props.nonce} />\n        <body>\n          <Main />\n          <NextScript nonce={this.props.nonce} />\n        </body>\n      </Html>\n    )\n  }\n}\n\n// Add a special property to the built-in `Document` component so later we can\n// identify if a user customized `Document` is used or not.\nconst InternalFunctionDocument: DocumentType =\n  function InternalFunctionDocument() {\n    return (\n      <Html>\n        <Head />\n        <body>\n          <Main />\n          <NextScript />\n        </body>\n      </Html>\n    )\n  }\n;(Document as any)[NEXT_BUILTIN_DOCUMENT] = InternalFunctionDocument\n"], "names": ["React", "NEXT_BUILTIN_DOCUMENT", "getPageFiles", "htmlEscapeJsonString", "isError", "HtmlContext", "useHtmlContext", "encodeURIPath", "getTracer", "getTracedMetadata", "largePageDataWarnings", "Set", "getDocumentFiles", "buildManifest", "pathname", "inAmpMode", "sharedFiles", "pageFiles", "process", "env", "NEXT_RUNTIME", "allFiles", "getPolyfillScripts", "context", "props", "assetPrefix", "assetQueryString", "disableOptimizedLoading", "crossOrigin", "polyfillFiles", "filter", "polyfill", "endsWith", "map", "script", "defer", "nonce", "noModule", "src", "hasComponentProps", "child", "AmpStyles", "styles", "curStyles", "Array", "isArray", "children", "hasStyles", "el", "dangerouslySetInnerHTML", "__html", "for<PERSON>ach", "push", "style", "amp-custom", "join", "replace", "getDynamicChunks", "files", "dynamicImports", "isDevelopment", "file", "includes", "async", "getScripts", "normalScripts", "lowPriorityScripts", "lowPriorityFiles", "getPreNextWorkerScripts", "<PERSON><PERSON><PERSON><PERSON>", "nextScriptWorkers", "partytownSnippet", "__non_webpack_require__", "userDefinedConfig", "find", "length", "data-partytown-config", "data-partytown", "worker", "index", "strategy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scriptProps", "srcProps", "Error", "type", "key", "data-nscript", "err", "code", "console", "warn", "message", "getPreNextScripts", "webWorkerScripts", "beforeInteractiveScripts", "beforeInteractive", "getHeadHTMLProps", "restProps", "headProps", "getAmp<PERSON><PERSON>", "ampPath", "<PERSON><PERSON><PERSON>", "getNextFontLinkTags", "nextFontManifest", "dangerousAsPath", "preconnect", "preload", "appFontsEntry", "pages", "pageFontsEntry", "preloadedFontFiles", "from", "preconnectToSelf", "link", "data-next-font", "pagesUsingSizeAdjust", "rel", "href", "fontFile", "ext", "exec", "as", "Head", "Component", "contextType", "getCssLinks", "dynamicCssManifest", "optimizeCss", "cssFiles", "f", "unmanagedFiles", "localDynamicCssFiles", "existing", "has", "cssLinkElements", "isSharedFile", "isUnmanagedFile", "isFileInDynamicCssManifest", "data-n-g", "undefined", "data-n-p", "getPreloadDynamicChunks", "Boolean", "getPreloadMainLinks", "preloadFiles", "getBeforeInteractiveInlineScripts", "html", "id", "__NEXT_CROSS_ORIGIN", "render", "hybridAmp", "canonicalBase", "__NEXT_DATA__", "headTags", "unstable_runtimeJS", "unstable_JsPreload", "disableRuntimeJS", "disableJsPreload", "doc<PERSON><PERSON><PERSON><PERSON><PERSON>ed", "head", "cssPreloads", "otherHeadElements", "strictNextHead", "cloneElement", "concat", "Children", "toArray", "NODE_ENV", "isReactHelmet", "name", "hasAmphtmlRel", "hasCanonicalRel", "badProp", "indexOf", "Object", "keys", "prop", "page", "nextFontLinkTags", "tracingMetadata", "getTracePropagationData", "experimentalClientTraceMetadata", "traceMetaTags", "value", "meta", "content", "data-next-hide-fouc", "data-ampdevmode", "noscript", "count", "toString", "require", "cleanAmpPath", "amp-boilerplate", "data-n-css", "createElement", "Fragment", "handleDocumentScriptLoaderItems", "scriptLoaderItems", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "combinedChildren", "__nextScript", "NextScript", "getInlineScriptSource", "largePageDataBytes", "data", "JSON", "stringify", "bytes", "TextEncoder", "encode", "buffer", "byteLength", "<PERSON><PERSON><PERSON>", "prettyBytes", "default", "add", "ampDevFiles", "devFiles", "Html", "locale", "lang", "amp", "Main", "next-js-internal-body-render-target", "Document", "getInitialProps", "ctx", "defaultGetInitialProps", "body", "InternalFunctionDocument"], "mappings": "AAAA,6CAA6C;;;AAE7C,OAAOA,WAAyB,QAAO;AACvC,SAASC,qBAAqB,QAAQ,0BAAyB;AAW/D,SAASC,YAAY,QAAQ,2BAA0B;AAEvD,SAASC,oBAAoB,QAAQ,uBAAsB;AAC3D,OAAOC,aAAa,kBAAiB;AAErC,SACEC,WAAW,EACXC,cAAc,QACT,4CAA2C;AAElD,SAASC,aAAa,QAAQ,gCAA+B;AAE7D,SAASC,SAAS,QAAQ,6BAA4B;AACtD,SAASC,iBAAiB,QAAQ,4BAA2B;AAuB7D,8EAA8E,GAC9E,MAAMC,wBAAwB,IAAIC;AAElC,SAASC,iBACPC,aAA4B,EAC5BC,QAAgB,EAChBC,SAAkB;IAElB,MAAMC,cAAiCd,aAAaW,eAAe;IACnE,MAAMI,YACJC,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAUL,YACnC,EAAE,GACFb,aAAaW,eAAeC;IAElC,OAAO;QACLE;QACAC;QACAI,UAAU;eAAI,IAAIV,IAAI;mBAAIK;mBAAgBC;aAAU;SAAE;IACxD;AACF;AAEA,SAASK,mBAAmBC,OAAkB,EAAEC,KAAkB;IAChE,4DAA4D;IAC5D,6CAA6C;IAC7C,MAAM,EACJC,WAAW,EACXZ,aAAa,EACba,gBAAgB,EAChBC,uBAAuB,EACvBC,WAAW,EACZ,GAAGL;IAEJ,OAAOV,cAAcgB,aAAa,CAC/BC,MAAM,CACL,CAACC,WAAaA,SAASC,QAAQ,CAAC,UAAU,CAACD,SAASC,QAAQ,CAAC,eAE9DC,GAAG,CAAC,CAACF,yBACJ,KAACG;YAECC,OAAO,CAACR;YACRS,OAAOZ,MAAMY,KAAK;YAClBR,aAAaJ,MAAMI,WAAW,IAAIA;YAClCS,UAAU;YACVC,KAAK,GAAGb,YAAY,OAAO,EAAElB,cAC3BwB,YACEL,kBAAkB;WAPjBK;AAUb;AAEA,SAASQ,kBAAkBC,KAAU;IACnC,OAAO,CAAC,CAACA,SAAS,CAAC,CAACA,MAAMhB,KAAK;AACjC;AAEA,SAASiB,UAAU,EACjBC,MAAM,EAGP;IACC,IAAI,CAACA,QAAQ,OAAO;IAEpB,yDAAyD;IACzD,MAAMC,YAAuCC,MAAMC,OAAO,CAACH,UACtDA,SACD,EAAE;IACN,IACE,kEAAkE;IAClEA,OAAOlB,KAAK,IACZ,kEAAkE;IAClEoB,MAAMC,OAAO,CAACH,OAAOlB,KAAK,CAACsB,QAAQ,GACnC;QACA,MAAMC,YAAY,CAACC;gBACjBA,mCAAAA;mBAAAA,uBAAAA,YAAAA,GAAIxB,KAAK,sBAATwB,oCAAAA,UAAWC,uBAAuB,qBAAlCD,kCAAoCE,MAAM;;QAC5C,kEAAkE;QAClER,OAAOlB,KAAK,CAACsB,QAAQ,CAACK,OAAO,CAAC,CAACX;YAC7B,IAAII,MAAMC,OAAO,CAACL,QAAQ;gBACxBA,MAAMW,OAAO,CAAC,CAACH,KAAOD,UAAUC,OAAOL,UAAUS,IAAI,CAACJ;YACxD,OAAO,IAAID,UAAUP,QAAQ;gBAC3BG,UAAUS,IAAI,CAACZ;YACjB;QACF;IACF;IAEA,uEAAuE,GACvE,qBACE,KAACa;QACCC,cAAW;QACXL,yBAAyB;YACvBC,QAAQP,UACLV,GAAG,CAAC,CAACoB,QAAUA,MAAM7B,KAAK,CAACyB,uBAAuB,CAACC,MAAM,EACzDK,IAAI,CAAC,IACLC,OAAO,CAAC,kCAAkC,IAC1CA,OAAO,CAAC,4BAA4B;QACzC;;AAGN;AAEA,SAASC,iBACPlC,OAAkB,EAClBC,KAAkB,EAClBkC,KAAoB;IAEpB,MAAM,EACJC,cAAc,EACdlC,WAAW,EACXmC,aAAa,EACblC,gBAAgB,EAChBC,uBAAuB,EACvBC,WAAW,EACZ,GAAGL;IAEJ,OAAOoC,eAAe1B,GAAG,CAAC,CAAC4B;QACzB,IAAI,CAACA,KAAK7B,QAAQ,CAAC,UAAU0B,MAAMrC,QAAQ,CAACyC,QAAQ,CAACD,OAAO,OAAO;QAEnE,qBACE,KAAC3B;YACC6B,OAAO,CAACH,iBAAiBjC;YACzBQ,OAAO,CAACR;YAERW,KAAK,GAAGb,YAAY,OAAO,EAAElB,cAAcsD,QAAQnC,kBAAkB;YACrEU,OAAOZ,MAAMY,KAAK;YAClBR,aAAaJ,MAAMI,WAAW,IAAIA;WAH7BiC;IAMX;AACF;AAEA,SAASG,WACPzC,OAAkB,EAClBC,KAAkB,EAClBkC,KAAoB;QAYO7C;IAV3B,MAAM,EACJY,WAAW,EACXZ,aAAa,EACb+C,aAAa,EACblC,gBAAgB,EAChBC,uBAAuB,EACvBC,WAAW,EACZ,GAAGL;IAEJ,MAAM0C,gBAAgBP,MAAMrC,QAAQ,CAACS,MAAM,CAAC,CAAC+B,OAASA,KAAK7B,QAAQ,CAAC;IACpE,MAAMkC,sBAAqBrD,kCAAAA,cAAcsD,gBAAgB,qBAA9BtD,gCAAgCiB,MAAM,CAAC,CAAC+B,OACjEA,KAAK7B,QAAQ,CAAC;IAGhB,OAAO;WAAIiC;WAAkBC;KAAmB,CAACjC,GAAG,CAAC,CAAC4B;QACpD,qBACE,KAAC3B;YAECI,KAAK,GAAGb,YAAY,OAAO,EAAElB,cAAcsD,QAAQnC,kBAAkB;YACrEU,OAAOZ,MAAMY,KAAK;YAClB2B,OAAO,CAACH,iBAAiBjC;YACzBQ,OAAO,CAACR;YACRC,aAAaJ,MAAMI,WAAW,IAAIA;WAL7BiC;IAQX;AACF;AAEA,SAASO,wBAAwB7C,OAAkB,EAAEC,KAAkB;IACrE,MAAM,EAAEC,WAAW,EAAE4C,YAAY,EAAEzC,WAAW,EAAE0C,iBAAiB,EAAE,GAAG/C;IAEtE,8CAA8C;IAC9C,IAAI,CAAC+C,qBAAqBpD,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ,OAAO;IAEtE,IAAI;QACF,iEAAiE;QACjE,IAAI,EAAEmD,gBAAgB,EAAE,GAAGC,wBACzB;QAGF,MAAM1B,WAAWF,MAAMC,OAAO,CAACrB,MAAMsB,QAAQ,IACzCtB,MAAMsB,QAAQ,GACd;YAACtB,MAAMsB,QAAQ;SAAC;QAEpB,yEAAyE;QACzE,MAAM2B,oBAAoB3B,SAAS4B,IAAI,CACrC,CAAClC;gBAECA,sCAAAA;mBADAD,kBAAkBC,WAClBA,0BAAAA,eAAAA,MAAOhB,KAAK,sBAAZgB,uCAAAA,aAAcS,uBAAuB,qBAArCT,qCAAuCU,MAAM,CAACyB,MAAM,KACpD,2BAA2BnC,MAAMhB,KAAK;;QAG1C,qBACE;;gBACG,CAACiD,mCACA,KAACvC;oBACC0C,yBAAsB;oBACtB3B,yBAAyB;wBACvBC,QAAQ,CAAC;;oBAEH,EAAEzB,YAAY;;UAExB,CAAC;oBACC;;8BAGJ,KAACS;oBACC2C,kBAAe;oBACf5B,yBAAyB;wBACvBC,QAAQqB;oBACV;;gBAEAF,CAAAA,aAAaS,MAAM,IAAI,EAAE,AAAD,EAAG7C,GAAG,CAAC,CAAC4B,MAAmBkB;oBACnD,MAAM,EACJC,QAAQ,EACR1C,GAAG,EACHQ,UAAUmC,cAAc,EACxBhC,uBAAuB,EACvB,GAAGiC,aACJ,GAAGrB;oBAEJ,IAAIsB,WAGA,CAAC;oBAEL,IAAI7C,KAAK;wBACP,+BAA+B;wBAC/B6C,SAAS7C,GAAG,GAAGA;oBACjB,OAAO,IACLW,2BACAA,wBAAwBC,MAAM,EAC9B;wBACA,+DAA+D;wBAC/DiC,SAASlC,uBAAuB,GAAG;4BACjCC,QAAQD,wBAAwBC,MAAM;wBACxC;oBACF,OAAO,IAAI+B,gBAAgB;wBACzB,gDAAgD;wBAChDE,SAASlC,uBAAuB,GAAG;4BACjCC,QACE,OAAO+B,mBAAmB,WACtBA,iBACArC,MAAMC,OAAO,CAACoC,kBACZA,eAAe1B,IAAI,CAAC,MACpB;wBACV;oBACF,OAAO;wBACL,MAAM,qBAEL,CAFK,IAAI6B,MACR,iJADI,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEA,qBACE,eAAClD;wBACE,GAAGiD,QAAQ;wBACX,GAAGD,WAAW;wBACfG,MAAK;wBACLC,KAAKhD,OAAOyC;wBACZ3C,OAAOZ,MAAMY,KAAK;wBAClBmD,gBAAa;wBACb3D,aAAaJ,MAAMI,WAAW,IAAIA;;gBAGxC;;;IAGN,EAAE,OAAO4D,KAAK;QACZ,IAAIpF,QAAQoF,QAAQA,IAAIC,IAAI,KAAK,oBAAoB;YACnDC,QAAQC,IAAI,CAAC,CAAC,SAAS,EAAEH,IAAII,OAAO,EAAE;QACxC;QACA,OAAO;IACT;AACF;AAEA,SAASC,kBAAkBtE,OAAkB,EAAEC,KAAkB;IAC/D,MAAM,EAAE6C,YAAY,EAAE1C,uBAAuB,EAAEC,WAAW,EAAE,GAAGL;IAE/D,MAAMuE,mBAAmB1B,wBAAwB7C,SAASC;IAE1D,MAAMuE,2BAA2B,AAAC1B,CAAAA,aAAa2B,iBAAiB,IAAI,EAAE,AAAD,EAClElE,MAAM,CAAC,CAACI,SAAWA,OAAOI,GAAG,EAC7BL,GAAG,CAAC,CAAC4B,MAAmBkB;QACvB,MAAM,EAAEC,QAAQ,EAAE,GAAGE,aAAa,GAAGrB;QACrC,qBACE,eAAC3B;YACE,GAAGgD,WAAW;YACfI,KAAKJ,YAAY5C,GAAG,IAAIyC;YACxB5C,OAAO+C,YAAY/C,KAAK,IAAI,CAACR;YAC7BS,OAAO8C,YAAY9C,KAAK,IAAIZ,MAAMY,KAAK;YACvCmD,gBAAa;YACb3D,aAAaJ,MAAMI,WAAW,IAAIA;;IAGxC;IAEF,qBACE;;YACGkE;YACAC;;;AAGP;AAEA,SAASE,iBAAiBzE,KAAgB;IACxC,MAAM,EAAEI,WAAW,EAAEQ,KAAK,EAAE,GAAG8D,WAAW,GAAG1E;IAE7C,sGAAsG;IACtG,MAAM2E,YAEFD;IAEJ,OAAOC;AACT;AAEA,SAASC,WAAWC,OAAe,EAAEC,MAAc;IACjD,OAAOD,WAAW,GAAGC,SAASA,OAAOxC,QAAQ,CAAC,OAAO,MAAM,IAAI,KAAK,CAAC;AACvE;AAEA,SAASyC,oBACPC,gBAA4D,EAC5DC,eAAuB,EACvBhF,cAAsB,EAAE;IAExB,IAAI,CAAC+E,kBAAkB;QACrB,OAAO;YACLE,YAAY;YACZC,SAAS;QACX;IACF;IAEA,MAAMC,gBAAgBJ,iBAAiBK,KAAK,CAAC,QAAQ;IACrD,MAAMC,iBAAiBN,iBAAiBK,KAAK,CAACJ,gBAAgB;IAE9D,MAAMM,qBAAqBnE,MAAMoE,IAAI,CACnC,IAAIrG,IAAI;WAAKiG,iBAAiB,EAAE;WAAOE,kBAAkB,EAAE;KAAE;IAG/D,2FAA2F;IAC3F,MAAMG,mBAAmB,CAAC,CACxBF,CAAAA,mBAAmBpC,MAAM,KAAK,KAC7BiC,CAAAA,iBAAiBE,cAAa,CAAC;IAGlC,OAAO;QACLJ,YAAYO,iCACV,KAACC;YACCC,kBACEX,iBAAiBY,oBAAoB,GAAG,gBAAgB;YAE1DC,KAAI;YACJC,MAAK;YACL1F,aAAY;aAEZ;QACJ+E,SAASI,qBACLA,mBAAmB9E,GAAG,CAAC,CAACsF;YACtB,MAAMC,MAAM,8BAA8BC,IAAI,CAACF,SAAU,CAAC,EAAE;YAC5D,qBACE,KAACL;gBAECG,KAAI;gBACJC,MAAM,GAAG7F,YAAY,OAAO,EAAElB,cAAcgH,WAAW;gBACvDG,IAAG;gBACHrC,MAAM,CAAC,KAAK,EAAEmC,KAAK;gBACnB5F,aAAY;gBACZuF,kBAAgBI,SAASzD,QAAQ,CAAC,QAAQ,gBAAgB;eANrDyD;QASX,KACA;IACN;AACF;AAEA,oEAAoE;AACpE,sDAAsD;AACtD,EAAE;AACF,sCAAsC;AACtC,EAAE;AACF,0DAA0D;AAC1D,OAAO,MAAMI,aAAa3H,MAAM4H,SAAS;qBAChCC,cAAcxH;IAIrByH,YAAYpE,KAAoB,EAAwB;QACtD,MAAM,EACJjC,WAAW,EACXC,gBAAgB,EAChBiC,cAAc,EACdoE,kBAAkB,EAClBnG,WAAW,EACXoG,WAAW,EACZ,GAAG,IAAI,CAACzG,OAAO;QAChB,MAAM0G,WAAWvE,MAAMrC,QAAQ,CAACS,MAAM,CAAC,CAACoG,IAAMA,EAAElG,QAAQ,CAAC;QACzD,MAAMhB,cAA2B,IAAIL,IAAI+C,MAAM1C,WAAW;QAE1D,qEAAqE;QACrE,+CAA+C;QAC/C,IAAImH,iBAA8B,IAAIxH,IAAI,EAAE;QAC5C,IAAIyH,uBAAuBxF,MAAMoE,IAAI,CACnC,IAAIrG,IAAIgD,eAAe7B,MAAM,CAAC,CAAC+B,OAASA,KAAK7B,QAAQ,CAAC;QAExD,IAAIoG,qBAAqBzD,MAAM,EAAE;YAC/B,MAAM0D,WAAW,IAAI1H,IAAIsH;YACzBG,uBAAuBA,qBAAqBtG,MAAM,CAChD,CAACoG,IAAM,CAAEG,CAAAA,SAASC,GAAG,CAACJ,MAAMlH,YAAYsH,GAAG,CAACJ,EAAC;YAE/CC,iBAAiB,IAAIxH,IAAIyH;YACzBH,SAAS7E,IAAI,IAAIgF;QACnB;QAEA,IAAIG,kBAAiC,EAAE;QACvCN,SAAS9E,OAAO,CAAC,CAACU;YAChB,MAAM2E,eAAexH,YAAYsH,GAAG,CAACzE;YACrC,MAAM4E,kBAAkBN,eAAeG,GAAG,CAACzE;YAC3C,MAAM6E,6BAA6BX,mBAAmBO,GAAG,CAACzE;YAE1D,IAAI,CAACmE,aAAa;gBAChBO,gBAAgBnF,IAAI,eAClB,KAAC8D;oBAEC9E,OAAO,IAAI,CAACZ,KAAK,CAACY,KAAK;oBACvBiF,KAAI;oBACJC,MAAM,GAAG7F,YAAY,OAAO,EAAElB,cAC5BsD,QACEnC,kBAAkB;oBACtBgG,IAAG;oBACH9F,aAAa,IAAI,CAACJ,KAAK,CAACI,WAAW,IAAIA;mBAPlC,GAAGiC,KAAK,QAAQ,CAAC;YAU5B;YAEA0E,gBAAgBnF,IAAI,eAClB,KAAC8D;gBAEC9E,OAAO,IAAI,CAACZ,KAAK,CAACY,KAAK;gBACvBiF,KAAI;gBACJC,MAAM,GAAG7F,YAAY,OAAO,EAAElB,cAC5BsD,QACEnC,kBAAkB;gBACtBE,aAAa,IAAI,CAACJ,KAAK,CAACI,WAAW,IAAIA;gBACvC+G,YAAUF,kBAAkBG,YAAYJ,eAAe,KAAKI;gBAC5DC,YACEL,gBAAgBC,mBAAmBC,6BAC/BE,YACA;eAXD/E;QAeX;QAEA,OAAO0E,gBAAgB5D,MAAM,KAAK,IAAI,OAAO4D;IAC/C;IAEAO,0BAA0B;QACxB,MAAM,EAAEnF,cAAc,EAAElC,WAAW,EAAEC,gBAAgB,EAAEE,WAAW,EAAE,GAClE,IAAI,CAACL,OAAO;QAEd,OACEoC,eACG1B,GAAG,CAAC,CAAC4B;YACJ,IAAI,CAACA,KAAK7B,QAAQ,CAAC,QAAQ;gBACzB,OAAO;YACT;YAEA,qBACE,KAACkF;gBACCG,KAAI;gBAEJC,MAAM,GAAG7F,YAAY,OAAO,EAAElB,cAC5BsD,QACEnC,kBAAkB;gBACtBgG,IAAG;gBACHtF,OAAO,IAAI,CAACZ,KAAK,CAACY,KAAK;gBACvBR,aAAa,IAAI,CAACJ,KAAK,CAACI,WAAW,IAAIA;eANlCiC;QASX,EACA,4BAA4B;SAC3B/B,MAAM,CAACiH;IAEd;IAEAC,oBAAoBtF,KAAoB,EAAwB;QAC9D,MAAM,EAAEjC,WAAW,EAAEC,gBAAgB,EAAE2C,YAAY,EAAEzC,WAAW,EAAE,GAChE,IAAI,CAACL,OAAO;QACd,MAAM0H,eAAevF,MAAMrC,QAAQ,CAACS,MAAM,CAAC,CAAC+B;YAC1C,OAAOA,KAAK7B,QAAQ,CAAC;QACvB;QAEA,OAAO;eACF,AAACqC,CAAAA,aAAa2B,iBAAiB,IAAI,EAAE,AAAD,EAAG/D,GAAG,CAAC,CAAC4B,qBAC7C,KAACqD;oBAEC9E,OAAO,IAAI,CAACZ,KAAK,CAACY,KAAK;oBACvBiF,KAAI;oBACJC,MAAMzD,KAAKvB,GAAG;oBACdoF,IAAG;oBACH9F,aAAa,IAAI,CAACJ,KAAK,CAACI,WAAW,IAAIA;mBALlCiC,KAAKvB,GAAG;eAQd2G,aAAahH,GAAG,CAAC,CAAC4B,qBACnB,KAACqD;oBAEC9E,OAAO,IAAI,CAACZ,KAAK,CAACY,KAAK;oBACvBiF,KAAI;oBACJC,MAAM,GAAG7F,YAAY,OAAO,EAAElB,cAC5BsD,QACEnC,kBAAkB;oBACtBgG,IAAG;oBACH9F,aAAa,IAAI,CAACJ,KAAK,CAACI,WAAW,IAAIA;mBAPlCiC;SAUV;IACH;IAEAqF,oCAAoC;QAClC,MAAM,EAAE7E,YAAY,EAAE,GAAG,IAAI,CAAC9C,OAAO;QACrC,MAAM,EAAEa,KAAK,EAAER,WAAW,EAAE,GAAG,IAAI,CAACJ,KAAK;QAEzC,OAAO,AAAC6C,CAAAA,aAAa2B,iBAAiB,IAAI,EAAE,AAAD,EACxClE,MAAM,CACL,CAACI,SACC,CAACA,OAAOI,GAAG,IAAKJ,CAAAA,OAAOe,uBAAuB,IAAIf,OAAOY,QAAQ,AAAD,GAEnEb,GAAG,CAAC,CAAC4B,MAAmBkB;YACvB,MAAM,EACJC,QAAQ,EACRlC,QAAQ,EACRG,uBAAuB,EACvBX,GAAG,EACH,GAAG4C,aACJ,GAAGrB;YACJ,IAAIsF,OAEU;YAEd,IAAIlG,2BAA2BA,wBAAwBC,MAAM,EAAE;gBAC7DiG,OAAOlG,wBAAwBC,MAAM;YACvC,OAAO,IAAIJ,UAAU;gBACnBqG,OACE,OAAOrG,aAAa,WAChBA,WACAF,MAAMC,OAAO,CAACC,YACZA,SAASS,IAAI,CAAC,MACd;YACV;YAEA,qBACE,eAACrB;gBACE,GAAGgD,WAAW;gBACfjC,yBAAyB;oBAAEC,QAAQiG;gBAAK;gBACxC7D,KAAKJ,YAAYkE,EAAE,IAAIrE;gBACvB3C,OAAOA;gBACPmD,gBAAa;gBACb3D,aACEA,eACCV,QAAQC,GAAG,CAACkI,mBAAmB;;QAIxC;IACJ;IAEA5F,iBAAiBC,KAAoB,EAAE;QACrC,OAAOD,iBAAiB,IAAI,CAAClC,OAAO,EAAE,IAAI,CAACC,KAAK,EAAEkC;IACpD;IAEAmC,oBAAoB;QAClB,OAAOA,kBAAkB,IAAI,CAACtE,OAAO,EAAE,IAAI,CAACC,KAAK;IACnD;IAEAwC,WAAWN,KAAoB,EAAE;QAC/B,OAAOM,WAAW,IAAI,CAACzC,OAAO,EAAE,IAAI,CAACC,KAAK,EAAEkC;IAC9C;IAEApC,qBAAqB;QACnB,OAAOA,mBAAmB,IAAI,CAACC,OAAO,EAAE,IAAI,CAACC,KAAK;IACpD;IAEA8H,SAAS;QACP,MAAM,EACJ5G,MAAM,EACN2D,OAAO,EACPtF,SAAS,EACTwI,SAAS,EACTC,aAAa,EACbC,aAAa,EACbhD,eAAe,EACfiD,QAAQ,EACRC,kBAAkB,EAClBC,kBAAkB,EAClBjI,uBAAuB,EACvBqG,WAAW,EACXvG,WAAW,EACX+E,gBAAgB,EACjB,GAAG,IAAI,CAACjF,OAAO;QAEhB,MAAMsI,mBAAmBF,uBAAuB;QAChD,MAAMG,mBACJF,uBAAuB,SAAS,CAACjI;QAEnC,IAAI,CAACJ,OAAO,CAACwI,qBAAqB,CAACpC,IAAI,GAAG;QAE1C,IAAI,EAAEqC,IAAI,EAAE,GAAG,IAAI,CAACzI,OAAO;QAC3B,IAAI0I,cAAkC,EAAE;QACxC,IAAIC,oBAAwC,EAAE;QAC9C,IAAIF,MAAM;YACRA,KAAK7G,OAAO,CAAC,CAACX;gBACZ,IACEA,SACAA,MAAM6C,IAAI,KAAK,UACf7C,MAAMhB,KAAK,CAAC,MAAM,KAAK,aACvBgB,MAAMhB,KAAK,CAAC,KAAK,KAAK,SACtB;oBACA,IAAI,IAAI,CAACD,OAAO,CAAC4I,cAAc,EAAE;wBAC/BF,YAAY7G,IAAI,eACdpD,MAAMoK,YAAY,CAAC5H,OAAO;4BAAE,kBAAkB;wBAAG;oBAErD,OAAO;wBACLyH,YAAY7G,IAAI,CAACZ;oBACnB;gBACF,OAAO;oBACL,IAAIA,OAAO;wBACT,IAAI,IAAI,CAACjB,OAAO,CAAC4I,cAAc,EAAE;4BAC/BD,kBAAkB9G,IAAI,eACpBpD,MAAMoK,YAAY,CAAC5H,OAAO;gCAAE,kBAAkB;4BAAG;wBAErD,OAAO;4BACL0H,kBAAkB9G,IAAI,CAACZ;wBACzB;oBACF;gBACF;YACF;YACAwH,OAAOC,YAAYI,MAAM,CAACH;QAC5B;QACA,IAAIpH,WAA8B9C,MAAMsK,QAAQ,CAACC,OAAO,CACtD,IAAI,CAAC/I,KAAK,CAACsB,QAAQ,EACnBhB,MAAM,CAACiH;QACT,gEAAgE;QAChE,IAAI7H,QAAQC,GAAG,CAACqJ,QAAQ,KAAK,cAAc;YACzC1H,WAAW9C,MAAMsK,QAAQ,CAACrI,GAAG,CAACa,UAAU,CAACN;oBACjBA;gBAAtB,MAAMiI,gBAAgBjI,0BAAAA,eAAAA,MAAOhB,KAAK,qBAAZgB,YAAc,CAAC,oBAAoB;gBACzD,IAAI,CAACiI,eAAe;wBAOhBjI;oBANF,IAAIA,CAAAA,yBAAAA,MAAO6C,IAAI,MAAK,SAAS;wBAC3BK,QAAQC,IAAI,CACV;oBAEJ,OAAO,IACLnD,CAAAA,yBAAAA,MAAO6C,IAAI,MAAK,UAChB7C,CAAAA,0BAAAA,gBAAAA,MAAOhB,KAAK,qBAAZgB,cAAckI,IAAI,MAAK,YACvB;wBACAhF,QAAQC,IAAI,CACV;oBAEJ;gBACF;gBACA,OAAOnD;YACP,wFAAwF;YAC1F;YACA,IAAI,IAAI,CAAChB,KAAK,CAACI,WAAW,EACxB8D,QAAQC,IAAI,CACV;QAEN;QAEA,IAAIgF,gBAAgB;QACpB,IAAIC,kBAAkB;QAEtB,oDAAoD;QACpDZ,OAAOhK,MAAMsK,QAAQ,CAACrI,GAAG,CAAC+H,QAAQ,EAAE,EAAE,CAACxH;YACrC,IAAI,CAACA,OAAO,OAAOA;YACnB,MAAM,EAAE6C,IAAI,EAAE7D,KAAK,EAAE,GAAGgB;YACxB,IAAItB,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAUL,WAAW;gBACpD,IAAI8J,UAAkB;gBAEtB,IAAIxF,SAAS,UAAU7D,MAAMkJ,IAAI,KAAK,YAAY;oBAChDG,UAAU;gBACZ,OAAO,IAAIxF,SAAS,UAAU7D,MAAM6F,GAAG,KAAK,aAAa;oBACvDuD,kBAAkB;gBACpB,OAAO,IAAIvF,SAAS,UAAU;oBAC5B,gBAAgB;oBAChB,yDAAyD;oBACzD,2DAA2D;oBAC3D,4BAA4B;oBAC5B,IACE,AAAC7D,MAAMc,GAAG,IAAId,MAAMc,GAAG,CAACwI,OAAO,CAAC,gBAAgB,CAAC,KAChDtJ,MAAMyB,uBAAuB,IAC3B,CAAA,CAACzB,MAAM6D,IAAI,IAAI7D,MAAM6D,IAAI,KAAK,iBAAgB,GACjD;wBACAwF,UAAU;wBACVE,OAAOC,IAAI,CAACxJ,OAAO2B,OAAO,CAAC,CAAC8H;4BAC1BJ,WAAW,CAAC,CAAC,EAAEI,KAAK,EAAE,EAAEzJ,KAAK,CAACyJ,KAAK,CAAC,CAAC,CAAC;wBACxC;wBACAJ,WAAW;oBACb;gBACF;gBAEA,IAAIA,SAAS;oBACXnF,QAAQC,IAAI,CACV,CAAC,2BAA2B,EAAEnD,MAAM6C,IAAI,CAAC,wBAAwB,EAAEwF,QAAQ,IAAI,EAAEpB,cAAcyB,IAAI,CAAC,sDAAsD,CAAC;oBAE7J,OAAO;gBACT;YACF,OAAO;gBACL,eAAe;gBACf,IAAI7F,SAAS,UAAU7D,MAAM6F,GAAG,KAAK,WAAW;oBAC9CsD,gBAAgB;gBAClB;YACF;YACA,OAAOnI;QACP,wFAAwF;QAC1F;QAEA,MAAMkB,QAAuB9C,iBAC3B,IAAI,CAACW,OAAO,CAACV,aAAa,EAC1B,IAAI,CAACU,OAAO,CAACkI,aAAa,CAACyB,IAAI,EAC/BhK,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAUL;QAGzC,MAAMoK,mBAAmB5E,oBACvBC,kBACAC,iBACAhF;QAGF,MAAM2J,kBAAkB3K,kBACtBD,YAAY6K,uBAAuB,IACnC,IAAI,CAAC9J,OAAO,CAAC+J,+BAA+B;QAG9C,MAAMC,gBAAgB,AAACH,CAAAA,mBAAmB,EAAE,AAAD,EAAGnJ,GAAG,CAC/C,CAAC,EAAEqD,GAAG,EAAEkG,KAAK,EAAE,EAAEzG,sBACf,KAAC0G;gBAAsCf,MAAMpF;gBAAKoG,SAASF;eAAhD,CAAC,gBAAgB,EAAEzG,OAAO;QAIzC,qBACE,MAACiF;YAAM,GAAG/D,iBAAiB,IAAI,CAACzE,KAAK,CAAC;;gBACnC,IAAI,CAACD,OAAO,CAACqC,aAAa,kBACzB;;sCACE,KAACP;4BACCsI,qBAAmB;4BACnBC,mBACE1K,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAUL,YACnC,SACA6H;4BAEN3F,yBAAyB;gCACvBC,QAAQ,CAAC,kBAAkB,CAAC;4BAC9B;;sCAEF,KAAC2I;4BACCF,qBAAmB;4BACnBC,mBACE1K,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAUL,YACnC,SACA6H;sCAGN,cAAA,KAACvF;gCACCJ,yBAAyB;oCACvBC,QAAQ,CAAC,mBAAmB,CAAC;gCAC/B;;;;;gBAKP8G;gBACA,IAAI,CAACzI,OAAO,CAAC4I,cAAc,GAAG,qBAC7B,KAACsB;oBACCf,MAAK;oBACLgB,SAAS1L,MAAMsK,QAAQ,CAACwB,KAAK,CAAC9B,QAAQ,EAAE,EAAE+B,QAAQ;;gBAIrDjJ;gBAEAqI,iBAAiBzE,UAAU;gBAC3ByE,iBAAiBxE,OAAO;gBAExBzF,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAUL,2BACtC;;sCACE,KAAC0K;4BACCf,MAAK;4BACLgB,SAAQ;;wBAET,CAACd,iCACA,KAAC1D;4BACCG,KAAI;4BACJC,MACEkC,gBACA,AACEwC,QAAQ,mBACRC,YAAY,CAACxF;;sCAKrB,KAACS;4BACCG,KAAI;4BACJK,IAAG;4BACHJ,MAAK;;sCAEP,KAAC7E;4BAAUC,QAAQA;;sCACnB,KAACW;4BACC6I,mBAAgB;4BAChBjJ,yBAAyB;gCACvBC,QAAQ,CAAC,slBAAslB,CAAC;4BAClmB;;sCAEF,KAAC2I;sCACC,cAAA,KAACxI;gCACC6I,mBAAgB;gCAChBjJ,yBAAyB;oCACvBC,QAAQ,CAAC,kFAAkF,CAAC;gCAC9F;;;sCAGJ,KAAChB;4BAAO6B,KAAK;4BAACzB,KAAI;;;;gBAGrB,CAAEpB,CAAAA,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAUL,SAAQ,mBAChD;;wBACG,CAAC4J,iBAAiBpB,2BACjB,KAACrC;4BACCG,KAAI;4BACJC,MAAMkC,gBAAgBpD,WAAWC,SAASI;;wBAG7C,IAAI,CAACyC,iCAAiC;wBACtC,CAAClB,eAAe,IAAI,CAACF,WAAW,CAACpE;wBACjC,CAACsE,6BAAe,KAAC6D;4BAASM,cAAY,IAAI,CAAC3K,KAAK,CAACY,KAAK,IAAI;;wBAE1D,CAACyH,oBACA,CAACC,oBACD,IAAI,CAAChB,uBAAuB;wBAC7B,CAACe,oBACA,CAACC,oBACD,IAAI,CAACd,mBAAmB,CAACtF;wBAE1B,CAAC/B,2BACA,CAACkI,oBACD,IAAI,CAACvI,kBAAkB;wBAExB,CAACK,2BACA,CAACkI,oBACD,IAAI,CAAChE,iBAAiB;wBACvB,CAAClE,2BACA,CAACkI,oBACD,IAAI,CAACpG,gBAAgB,CAACC;wBACvB,CAAC/B,2BACA,CAACkI,oBACD,IAAI,CAAC7F,UAAU,CAACN;wBAEjBsE,eAAe,IAAI,CAACF,WAAW,CAACpE;wBAChCsE,6BAAe,KAAC6D;4BAASM,cAAY,IAAI,CAAC3K,KAAK,CAACY,KAAK,IAAI;;wBACzD,IAAI,CAACb,OAAO,CAACqC,aAAa,IACzB,0DAA0D;wBAC1D,8BAA8B;wBAC9B,+DAA+D;sCAC/D,KAACiI;4BAASzC,IAAG;;wBAEdmC;wBACA7I,UAAU;;;8BAGd1C,MAAMoM,aAAa,CAACpM,MAAMqM,QAAQ,EAAE,CAAC,MAAO3C,YAAY,EAAE;;;IAGjE;AACF;AAEA,SAAS4C,gCACPjI,YAA2C,EAC3CoF,aAAwB,EACxBjI,KAAU;QAUWsB,sBAAAA,gBAGAA,uBAAAA;IAXrB,IAAI,CAACtB,MAAMsB,QAAQ,EAAE;IAErB,MAAMyJ,oBAAmC,EAAE;IAE3C,MAAMzJ,WAAWF,MAAMC,OAAO,CAACrB,MAAMsB,QAAQ,IACzCtB,MAAMsB,QAAQ,GACd;QAACtB,MAAMsB,QAAQ;KAAC;IAEpB,MAAM0J,gBAAe1J,iBAAAA,SAAS4B,IAAI,CAChC,CAAClC,QAA8BA,MAAM6C,IAAI,KAAKsC,2BAD3B7E,uBAAAA,eAElBtB,KAAK,qBAFasB,qBAEXA,QAAQ;IAClB,MAAM2J,gBAAe3J,kBAAAA,SAAS4B,IAAI,CAChC,CAAClC,QAA8BA,MAAM6C,IAAI,KAAK,6BAD3BvC,wBAAAA,gBAElBtB,KAAK,qBAFasB,sBAEXA,QAAQ;IAElB,+GAA+G;IAC/G,MAAM4J,mBAAmB;WACnB9J,MAAMC,OAAO,CAAC2J,gBAAgBA,eAAe;YAACA;SAAa;WAC3D5J,MAAMC,OAAO,CAAC4J,gBAAgBA,eAAe;YAACA;SAAa;KAChE;IAEDzM,MAAMsK,QAAQ,CAACnH,OAAO,CAACuJ,kBAAkB,CAAClK;YAIpCA;QAHJ,IAAI,CAACA,OAAO;QAEZ,wEAAwE;QACxE,KAAIA,cAAAA,MAAM6C,IAAI,qBAAV7C,YAAYmK,YAAY,EAAE;YAC5B,IAAInK,MAAMhB,KAAK,CAACwD,QAAQ,KAAK,qBAAqB;gBAChDX,aAAa2B,iBAAiB,GAAG,AAC/B3B,CAAAA,aAAa2B,iBAAiB,IAAI,EAAE,AAAD,EACnCqE,MAAM,CAAC;oBACP;wBACE,GAAG7H,MAAMhB,KAAK;oBAChB;iBACD;gBACD;YACF,OAAO,IACL;gBAAC;gBAAc;gBAAoB;aAAS,CAACsC,QAAQ,CACnDtB,MAAMhB,KAAK,CAACwD,QAAQ,GAEtB;gBACAuH,kBAAkBnJ,IAAI,CAACZ,MAAMhB,KAAK;gBAClC;YACF,OAAO,IAAI,OAAOgB,MAAMhB,KAAK,CAACwD,QAAQ,KAAK,aAAa;gBACtDuH,kBAAkBnJ,IAAI,CAAC;oBAAE,GAAGZ,MAAMhB,KAAK;oBAAEwD,UAAU;gBAAmB;gBACtE;YACF;QACF;IACF;IAEAyE,cAAcpF,YAAY,GAAGkI;AAC/B;AAEA,OAAO,MAAMK,mBAAmB5M,MAAM4H,SAAS;qBACtCC,cAAcxH;IAIrBoD,iBAAiBC,KAAoB,EAAE;QACrC,OAAOD,iBAAiB,IAAI,CAAClC,OAAO,EAAE,IAAI,CAACC,KAAK,EAAEkC;IACpD;IAEAmC,oBAAoB;QAClB,OAAOA,kBAAkB,IAAI,CAACtE,OAAO,EAAE,IAAI,CAACC,KAAK;IACnD;IAEAwC,WAAWN,KAAoB,EAAE;QAC/B,OAAOM,WAAW,IAAI,CAACzC,OAAO,EAAE,IAAI,CAACC,KAAK,EAAEkC;IAC9C;IAEApC,qBAAqB;QACnB,OAAOA,mBAAmB,IAAI,CAACC,OAAO,EAAE,IAAI,CAACC,KAAK;IACpD;IAEA,OAAOqL,sBAAsBtL,OAA4B,EAAU;QACjE,MAAM,EAAEkI,aAAa,EAAEqD,kBAAkB,EAAE,GAAGvL;QAC9C,IAAI;YACF,MAAMwL,OAAOC,KAAKC,SAAS,CAACxD;YAE5B,IAAI/I,sBAAsB4H,GAAG,CAACmB,cAAcyB,IAAI,GAAG;gBACjD,OAAO/K,qBAAqB4M;YAC9B;YAEA,MAAMG,QACJhM,QAAQC,GAAG,CAACC,YAAY,KAAK,SACzB,IAAI+L,cAAcC,MAAM,CAACL,MAAMM,MAAM,CAACC,UAAU,GAChDC,OAAOvG,IAAI,CAAC+F,MAAMO,UAAU;YAClC,MAAME,cAAc,AAClBxB,QAAQ,uBACRyB,OAAO;YAET,IAAIX,sBAAsBI,QAAQJ,oBAAoB;gBACpD,IAAI5L,QAAQC,GAAG,CAACqJ,QAAQ,KAAK,cAAc;oBACzC9J,sBAAsBgN,GAAG,CAACjE,cAAcyB,IAAI;gBAC9C;gBAEAxF,QAAQC,IAAI,CACV,CAAC,wBAAwB,EAAE8D,cAAcyB,IAAI,CAAC,CAAC,EAC7CzB,cAAcyB,IAAI,KAAK3J,QAAQkF,eAAe,GAC1C,KACA,CAAC,QAAQ,EAAElF,QAAQkF,eAAe,CAAC,EAAE,CAAC,CAC3C,IAAI,EAAE+G,YACLN,OACA,gCAAgC,EAAEM,YAClCV,oBACA,mHAAmH,CAAC;YAE1H;YAEA,OAAO3M,qBAAqB4M;QAC9B,EAAE,OAAOvH,KAAK;YACZ,IAAIpF,QAAQoF,QAAQA,IAAII,OAAO,CAACkF,OAAO,CAAC,0BAA0B,CAAC,GAAG;gBACpE,MAAM,qBAEL,CAFK,IAAI1F,MACR,CAAC,wDAAwD,EAAEqE,cAAcyB,IAAI,CAAC,sDAAsD,CAAC,GADjI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAM1F;QACR;IACF;IAEA8D,SAAS;QACP,MAAM,EACJ7H,WAAW,EACXV,SAAS,EACTF,aAAa,EACb8I,kBAAkB,EAClBI,qBAAqB,EACrBrI,gBAAgB,EAChBC,uBAAuB,EACvBC,WAAW,EACZ,GAAG,IAAI,CAACL,OAAO;QAChB,MAAMsI,mBAAmBF,uBAAuB;QAEhDI,sBAAsB6C,UAAU,GAAG;QAEnC,IAAI1L,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAUL,WAAW;YACpD,IAAIG,QAAQC,GAAG,CAACqJ,QAAQ,KAAK,cAAc;gBACzC,OAAO;YACT;YACA,MAAMmD,cAAc;mBACf9M,cAAc+M,QAAQ;mBACtB/M,cAAcgB,aAAa;mBAC3BhB,cAAc8M,WAAW;aAC7B;YAED,qBACE;;oBACG9D,mBAAmB,qBAClB,KAAC3H;wBACCkH,IAAG;wBACH/D,MAAK;wBACLjD,OAAO,IAAI,CAACZ,KAAK,CAACY,KAAK;wBACvBR,aAAa,IAAI,CAACJ,KAAK,CAACI,WAAW,IAAIA;wBACvCqB,yBAAyB;4BACvBC,QAAQ0J,WAAWC,qBAAqB,CAAC,IAAI,CAACtL,OAAO;wBACvD;wBACAqK,iBAAe;;oBAGlB+B,YAAY1L,GAAG,CAAC,CAAC4B,qBAChB,KAAC3B;4BAECI,KAAK,GAAGb,YAAY,OAAO,EAAElB,cAC3BsD,QACEnC,kBAAkB;4BACtBU,OAAO,IAAI,CAACZ,KAAK,CAACY,KAAK;4BACvBR,aAAa,IAAI,CAACJ,KAAK,CAACI,WAAW,IAAIA;4BACvCgK,iBAAe;2BANV/H;;;QAWf;QAEA,IAAI3C,QAAQC,GAAG,CAACqJ,QAAQ,KAAK,cAAc;YACzC,IAAI,IAAI,CAAChJ,KAAK,CAACI,WAAW,EACxB8D,QAAQC,IAAI,CACV;QAEN;QAEA,MAAMjC,QAAuB9C,iBAC3B,IAAI,CAACW,OAAO,CAACV,aAAa,EAC1B,IAAI,CAACU,OAAO,CAACkI,aAAa,CAACyB,IAAI,EAC/BhK,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAUL;QAGzC,qBACE;;gBACG,CAAC8I,oBAAoBhJ,cAAc+M,QAAQ,GACxC/M,cAAc+M,QAAQ,CAAC3L,GAAG,CAAC,CAAC4B,qBAC1B,KAAC3B;wBAECI,KAAK,GAAGb,YAAY,OAAO,EAAElB,cAC3BsD,QACEnC,kBAAkB;wBACtBU,OAAO,IAAI,CAACZ,KAAK,CAACY,KAAK;wBACvBR,aAAa,IAAI,CAACJ,KAAK,CAACI,WAAW,IAAIA;uBALlCiC,SAQT;gBACHgG,mBAAmB,qBAClB,KAAC3H;oBACCkH,IAAG;oBACH/D,MAAK;oBACLjD,OAAO,IAAI,CAACZ,KAAK,CAACY,KAAK;oBACvBR,aAAa,IAAI,CAACJ,KAAK,CAACI,WAAW,IAAIA;oBACvCqB,yBAAyB;wBACvBC,QAAQ0J,WAAWC,qBAAqB,CAAC,IAAI,CAACtL,OAAO;oBACvD;;gBAGHI,2BACC,CAACkI,oBACD,IAAI,CAACvI,kBAAkB;gBACxBK,2BACC,CAACkI,oBACD,IAAI,CAAChE,iBAAiB;gBACvBlE,2BACC,CAACkI,oBACD,IAAI,CAACpG,gBAAgB,CAACC;gBACvB/B,2BAA2B,CAACkI,oBAAoB,IAAI,CAAC7F,UAAU,CAACN;;;IAGvE;AACF;AAEA,OAAO,SAASmK,KACdrM,KAGC;IAED,MAAM,EACJT,SAAS,EACTgJ,qBAAqB,EACrB+D,MAAM,EACNzJ,YAAY,EACZoF,aAAa,EACd,GAAGnJ;IAEJyJ,sBAAsB8D,IAAI,GAAG;IAC7BvB,gCAAgCjI,cAAcoF,eAAejI;IAE7D,qBACE,KAAC2H;QACE,GAAG3H,KAAK;QACTuM,MAAMvM,MAAMuM,IAAI,IAAID,UAAUlF;QAC9BoF,KAAK9M,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAUL,YAAY,KAAK6H;QAC7DgD,mBACE1K,QAAQC,GAAG,CAACC,YAAY,KAAK,UAC7BL,aACAG,QAAQC,GAAG,CAACqJ,QAAQ,KAAK,eACrB,KACA5B;;AAIZ;AAEA,OAAO,SAASqF;IACd,MAAM,EAAElE,qBAAqB,EAAE,GAAGzJ;IAClCyJ,sBAAsBkE,IAAI,GAAG;IAC7B,aAAa;IACb,qBAAO,KAACC;AACV;AAEA;;;CAGC,GACD,eAAe,MAAMC,iBAAyBnO,MAAM4H,SAAS;IAG3D;;;GAGC,GACD,OAAOwG,gBAAgBC,GAAoB,EAAiC;QAC1E,OAAOA,IAAIC,sBAAsB,CAACD;IACpC;IAEA/E,SAAS;QACP,qBACE,MAACuE;;8BACC,KAAClG;oBAAKvF,OAAO,IAAI,CAACZ,KAAK,CAACY,KAAK;;8BAC7B,MAACmM;;sCACC,KAACN;sCACD,KAACrB;4BAAWxK,OAAO,IAAI,CAACZ,KAAK,CAACY,KAAK;;;;;;IAI3C;AACF;AAEA,8EAA8E;AAC9E,2DAA2D;AAC3D,MAAMoM,2BACJ,SAASA;IACP,qBACE,MAACX;;0BACC,KAAClG;0BACD,MAAC4G;;kCACC,KAACN;kCACD,KAACrB;;;;;AAIT;AACAuB,QAAgB,CAAClO,sBAAsB,GAAGuO", "ignoreList": [0]}