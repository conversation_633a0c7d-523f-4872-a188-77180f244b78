{"version": 3, "sources": ["../../../src/server/app-render/app-render.tsx"], "sourcesContent": ["import type {\n  <PERSON>R<PERSON>ult,\n  DynamicParamTypesShort,\n  FlightRouterState,\n  RenderOpts,\n  Segment,\n  CacheNodeSeedData,\n  PreloadCallbacks,\n  RSCPayload,\n  FlightData,\n  InitialRSCPayload,\n  FlightDataPath,\n} from './types'\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport type { RequestStore } from '../app-render/work-unit-async-storage.external'\nimport type { NextParsedUrlQuery } from '../request-meta'\nimport type { LoaderTree } from '../lib/app-dir-module'\nimport type { AppPageModule } from '../route-modules/app-page/module'\nimport type {\n  ClientReferenceManifest,\n  ManifestNode,\n} from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport type { BaseNextRequest, BaseNextResponse } from '../base-http'\nimport type { IncomingHttpHeaders } from 'http'\n\nimport React, { type ErrorInfo, type JSX } from 'react'\n\nimport RenderResult, {\n  type AppPageRenderResultMetadata,\n  type RenderResultOptions,\n} from '../render-result'\nimport {\n  chainStreams,\n  renderToInitialFizzStream,\n  createDocumentClosingStream,\n  continueFizzStream,\n  continueDynamicPrerender,\n  continueStaticPrerender,\n  continueDynamicHTMLResume,\n  streamToBuffer,\n  streamToString,\n} from '../stream-utils/node-web-streams-helper'\nimport { stripInternalQueries } from '../internal-utils'\nimport {\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_STALE_TIME_HEADER,\n  NEXT_URL,\n  RSC_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_HMR_REFRESH_HASH_COOKIE,\n} from '../../client/components/app-router-headers'\nimport { createMetadataContext } from '../../lib/metadata/metadata-context'\nimport { createRequestStoreForRender } from '../async-storage/request-store'\nimport { createWorkStore } from '../async-storage/work-store'\nimport {\n  getAccessFallbackErrorTypeByStatus,\n  getAccessFallbackHTTPStatus,\n  isHTTPAccessFallbackError,\n} from '../../client/components/http-access-fallback/http-access-fallback'\nimport {\n  getURLFromRedirectError,\n  getRedirectStatusCodeFromError,\n} from '../../client/components/redirect'\nimport { isRedirectError } from '../../client/components/redirect-error'\nimport { getImplicitTags, type ImplicitTags } from '../lib/implicit-tags'\nimport { AppRenderSpan, NextNodeServerSpan } from '../lib/trace/constants'\nimport { getTracer } from '../lib/trace/tracer'\nimport { FlightRenderResult } from './flight-render-result'\nimport {\n  createFlightReactServerErrorHandler,\n  createHTMLReactServerErrorHandler,\n  createHTMLErrorHandler,\n  type DigestedError,\n  isUserLandError,\n  getDigestForWellKnownError,\n} from './create-error-handler'\nimport {\n  getShortDynamicParamType,\n  dynamicParamTypes,\n} from './get-short-dynamic-param-type'\nimport { getSegmentParam } from './get-segment-param'\nimport { getScriptNonceFromHeader } from './get-script-nonce-from-header'\nimport { parseAndValidateFlightRouterState } from './parse-and-validate-flight-router-state'\nimport { createFlightRouterStateFromLoaderTree } from './create-flight-router-state-from-loader-tree'\nimport { handleAction } from './action-handler'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { warn, error } from '../../build/output/log'\nimport { appendMutableCookies } from '../web/spec-extension/adapters/request-cookies'\nimport { createServerInsertedHTML } from './server-inserted-html'\nimport { getRequiredScripts } from './required-scripts'\nimport { addPathPrefix } from '../../shared/lib/router/utils/add-path-prefix'\nimport { makeGetServerInsertedHTML } from './make-get-server-inserted-html'\nimport { walkTreeWithFlightRouterState } from './walk-tree-with-flight-router-state'\nimport { createComponentTree, getRootParams } from './create-component-tree'\nimport { getAssetQueryString } from './get-asset-query-string'\nimport {\n  getServerModuleMap,\n  setReferenceManifestsSingleton,\n} from './encryption-utils'\nimport {\n  DynamicState,\n  type PostponedState,\n  parsePostponedState,\n} from './postponed-state'\nimport {\n  getDynamicDataPostponedState,\n  getDynamicHTMLPostponedState,\n  getPostponedFromState,\n} from './postponed-state'\nimport { isDynamicServerError } from '../../client/components/hooks-server-context'\nimport {\n  useFlightStream,\n  createInlinedDataReadableStream,\n} from './use-flight-response'\nimport {\n  StaticGenBailoutError,\n  isStaticGenBailoutError,\n} from '../../client/components/static-generation-bailout'\nimport { getStackWithoutErrorMessage } from '../../lib/format-server-error'\nimport {\n  accessedDynamicData,\n  createPostponedAbortSignal,\n  formatDynamicAPIAccesses,\n  isPrerenderInterruptedError,\n  createDynamicTrackingState,\n  createDynamicValidationState,\n  trackAllowedDynamicAccess,\n  throwIfDisallowedDynamic,\n  PreludeState,\n  consumeDynamicAccess,\n  type DynamicAccess,\n} from './dynamic-rendering'\nimport {\n  getClientComponentLoaderMetrics,\n  wrapClientComponentLoader,\n} from '../client-component-renderer-logger'\nimport { createServerModuleMap } from './action-utils'\nimport { isNodeNextRequest } from '../base-http/helpers'\nimport { parseParameter } from '../../shared/lib/router/utils/route-regex'\nimport { parseRelativeUrl } from '../../shared/lib/router/utils/parse-relative-url'\nimport AppRouter from '../../client/components/app-router'\nimport type { ServerComponentsHmrCache } from '../response-cache'\nimport type { RequestErrorContext } from '../instrumentation/types'\nimport { getIsPossibleServerAction } from '../lib/server-action-request-meta'\nimport { createInitialRouterState } from '../../client/components/router-reducer/create-initial-router-state'\nimport { createMutableActionQueue } from '../../client/components/app-router-instance'\nimport { getRevalidateReason } from '../instrumentation/utils'\nimport { PAGE_SEGMENT_KEY } from '../../shared/lib/segment'\nimport type { FallbackRouteParams } from '../request/fallback-params'\nimport {\n  ServerPrerenderStreamResult,\n  processPrelude,\n} from './app-render-prerender-utils'\nimport {\n  type ReactServerPrerenderResult,\n  ReactServerResult,\n  createReactServerPrerenderResult,\n  createReactServerPrerenderResultFromRender,\n  prerenderAndAbortInSequentialTasks,\n} from './app-render-prerender-utils'\nimport { printDebugThrownValueForProspectiveRender } from './prospective-render-utils'\nimport { scheduleInSequentialTasks } from './app-render-render-utils'\nimport { waitAtLeastOneReactRenderTask } from '../../lib/scheduler'\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n} from './work-unit-async-storage.external'\nimport { CacheSignal } from './cache-signal'\nimport { getTracedMetadata } from '../lib/trace/utils'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport { INFINITE_CACHE } from '../../lib/constants'\nimport { createComponentStylesAndScripts } from './create-component-styles-and-scripts'\nimport { parseLoaderTree } from './parse-loader-tree'\nimport {\n  createPrerenderResumeDataCache,\n  createRenderResumeDataCache,\n  type PrerenderResumeDataCache,\n  type RenderResumeDataCache,\n} from '../resume-data-cache/resume-data-cache'\nimport type { MetadataErrorType } from '../../lib/metadata/resolve-metadata'\nimport isError from '../../lib/is-error'\nimport { createServerInsertedMetadata } from './metadata-insertion/create-server-inserted-metadata'\nimport { getPreviouslyRevalidatedTags } from '../server-utils'\nimport { executeRevalidates } from '../revalidation-utils'\nimport {\n  trackPendingChunkLoad,\n  trackPendingImport,\n  trackPendingModules,\n} from './module-loading/track-module-loading.external'\nimport { isReactLargeShellError } from './react-large-shell-error'\nimport type { GlobalErrorComponent } from '../../client/components/builtin/global-error'\nimport { normalizeConventionFilePath } from './segment-explorer-path'\n\nexport type GetDynamicParamFromSegment = (\n  // [slug] / [[slug]] / [...slug]\n  segment: string\n) => {\n  param: string\n  value: string | string[] | null\n  treeSegment: Segment\n  type: DynamicParamTypesShort\n} | null\n\nexport type GenerateFlight = typeof generateDynamicFlightRenderResult\n\nexport type AppSharedContext = {\n  buildId: string\n}\n\nexport type AppRenderContext = {\n  sharedContext: AppSharedContext\n  workStore: WorkStore\n  url: ReturnType<typeof parseRelativeUrl>\n  componentMod: AppPageModule\n  renderOpts: RenderOpts\n  parsedRequestHeaders: ParsedRequestHeaders\n  getDynamicParamFromSegment: GetDynamicParamFromSegment\n  query: NextParsedUrlQuery\n  isPrefetch: boolean\n  isPossibleServerAction: boolean\n  requestTimestamp: number\n  appUsingSizeAdjustment: boolean\n  flightRouterState?: FlightRouterState\n  requestId: string\n  pagePath: string\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>\n  assetPrefix: string\n  isNotFoundPath: boolean\n  nonce: string | undefined\n  res: BaseNextResponse\n  /**\n   * For now, the implicit tags are common for the whole route. If we ever start\n   * rendering/revalidating segments independently, they need to move to the\n   * work unit store.\n   */\n  implicitTags: ImplicitTags\n}\n\ninterface ParseRequestHeadersOptions {\n  readonly isDevWarmup: undefined | boolean\n  readonly isRoutePPREnabled: boolean\n  readonly previewModeId: string | undefined\n}\n\nconst flightDataPathHeadKey = 'h'\nconst getFlightViewportKey = (requestId: string) => requestId + 'v'\nconst getFlightMetadataKey = (requestId: string) => requestId + 'm'\n\nconst filterStackFrame =\n  process.env.NODE_ENV !== 'production'\n    ? (require('../lib/source-maps') as typeof import('../lib/source-maps'))\n        .filterStackFrameDEV\n    : undefined\n\ninterface ParsedRequestHeaders {\n  /**\n   * Router state provided from the client-side router. Used to handle rendering\n   * from the common layout down. This value will be undefined if the request is\n   * not a client-side navigation request, or if the request is a prefetch\n   * request.\n   */\n  readonly flightRouterState: FlightRouterState | undefined\n  readonly isPrefetchRequest: boolean\n  readonly isRouteTreePrefetchRequest: boolean\n  readonly isDevWarmupRequest: boolean\n  readonly isHmrRefresh: boolean\n  readonly isRSCRequest: boolean\n  readonly nonce: string | undefined\n  readonly previouslyRevalidatedTags: string[]\n}\n\nfunction parseRequestHeaders(\n  headers: IncomingHttpHeaders,\n  options: ParseRequestHeadersOptions\n): ParsedRequestHeaders {\n  const isDevWarmupRequest = options.isDevWarmup === true\n\n  // dev warmup requests are treated as prefetch RSC requests\n  const isPrefetchRequest =\n    isDevWarmupRequest ||\n    headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] !== undefined\n\n  const isHmrRefresh =\n    headers[NEXT_HMR_REFRESH_HEADER.toLowerCase()] !== undefined\n\n  // dev warmup requests are treated as prefetch RSC requests\n  const isRSCRequest =\n    isDevWarmupRequest || headers[RSC_HEADER.toLowerCase()] !== undefined\n\n  const shouldProvideFlightRouterState =\n    isRSCRequest && (!isPrefetchRequest || !options.isRoutePPREnabled)\n\n  const flightRouterState = shouldProvideFlightRouterState\n    ? parseAndValidateFlightRouterState(\n        headers[NEXT_ROUTER_STATE_TREE_HEADER.toLowerCase()]\n      )\n    : undefined\n\n  // Checks if this is a prefetch of the Route Tree by the Segment Cache\n  const isRouteTreePrefetchRequest =\n    headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()] === '/_tree'\n\n  const csp =\n    headers['content-security-policy'] ||\n    headers['content-security-policy-report-only']\n\n  const nonce =\n    typeof csp === 'string' ? getScriptNonceFromHeader(csp) : undefined\n\n  const previouslyRevalidatedTags = getPreviouslyRevalidatedTags(\n    headers,\n    options.previewModeId\n  )\n\n  return {\n    flightRouterState,\n    isPrefetchRequest,\n    isRouteTreePrefetchRequest,\n    isHmrRefresh,\n    isRSCRequest,\n    isDevWarmupRequest,\n    nonce,\n    previouslyRevalidatedTags,\n  }\n}\n\nfunction createNotFoundLoaderTree(loaderTree: LoaderTree): LoaderTree {\n  const components = loaderTree[2]\n  const hasGlobalNotFound = !!components['global-not-found']\n  return [\n    '',\n    {\n      children: [\n        PAGE_SEGMENT_KEY,\n        {},\n        {\n          page: components['global-not-found'] ?? components['not-found'],\n        },\n      ],\n    },\n    // When global-not-found is present, skip layout from components\n    hasGlobalNotFound ? components : {},\n  ]\n}\n\n/**\n * Returns a function that parses the dynamic segment and return the associated value.\n */\nfunction makeGetDynamicParamFromSegment(\n  params: { [key: string]: any },\n  pagePath: string,\n  fallbackRouteParams: FallbackRouteParams | null\n): GetDynamicParamFromSegment {\n  return function getDynamicParamFromSegment(\n    // [slug] / [[slug]] / [...slug]\n    segment: string\n  ) {\n    const segmentParam = getSegmentParam(segment)\n    if (!segmentParam) {\n      return null\n    }\n\n    const key = segmentParam.param\n\n    let value = params[key]\n\n    if (fallbackRouteParams && fallbackRouteParams.has(segmentParam.param)) {\n      value = fallbackRouteParams.get(segmentParam.param)\n    } else if (Array.isArray(value)) {\n      value = value.map((i) => encodeURIComponent(i))\n    } else if (typeof value === 'string') {\n      value = encodeURIComponent(value)\n    }\n\n    if (!value) {\n      const isCatchall = segmentParam.type === 'catchall'\n      const isOptionalCatchall = segmentParam.type === 'optional-catchall'\n\n      if (isCatchall || isOptionalCatchall) {\n        const dynamicParamType = dynamicParamTypes[segmentParam.type]\n        // handle the case where an optional catchall does not have a value,\n        // e.g. `/dashboard/[[...slug]]` when requesting `/dashboard`\n        if (isOptionalCatchall) {\n          return {\n            param: key,\n            value: null,\n            type: dynamicParamType,\n            treeSegment: [key, '', dynamicParamType],\n          }\n        }\n\n        // handle the case where a catchall or optional catchall does not have a value,\n        // e.g. `/foo/bar/hello` and `@slot/[...catchall]` or `@slot/[[...catchall]]` is matched\n        value = pagePath\n          .split('/')\n          // remove the first empty string\n          .slice(1)\n          // replace any dynamic params with the actual values\n          .flatMap((pathSegment) => {\n            const param = parseParameter(pathSegment)\n            // if the segment matches a param, return the param value\n            // otherwise, it's a static segment, so just return that\n            return params[param.key] ?? param.key\n          })\n\n        return {\n          param: key,\n          value,\n          type: dynamicParamType,\n          // This value always has to be a string.\n          treeSegment: [key, value.join('/'), dynamicParamType],\n        }\n      }\n    }\n\n    const type = getShortDynamicParamType(segmentParam.type)\n\n    return {\n      param: key,\n      // The value that is passed to user code.\n      value: value,\n      // The value that is rendered in the router tree.\n      treeSegment: [key, Array.isArray(value) ? value.join('/') : value, type],\n      type: type,\n    }\n  }\n}\n\nfunction NonIndex({\n  pagePath,\n  statusCode,\n  isPossibleServerAction,\n}: {\n  pagePath: string\n  statusCode: number | undefined\n  isPossibleServerAction: boolean\n}) {\n  const is404Page = pagePath === '/404'\n  const isInvalidStatusCode = typeof statusCode === 'number' && statusCode > 400\n\n  // Only render noindex for page request, skip for server actions\n  // TODO: is this correct if `isPossibleServerAction` is a false positive?\n  if (!isPossibleServerAction && (is404Page || isInvalidStatusCode)) {\n    return <meta name=\"robots\" content=\"noindex\" />\n  }\n  return null\n}\n\n/**\n * This is used by server actions & client-side navigations to generate RSC data from a client-side request.\n * This function is only called on \"dynamic\" requests (ie, there wasn't already a static response).\n * It uses request headers (namely `Next-Router-State-Tree`) to determine where to start rendering.\n */\nasync function generateDynamicRSCPayload(\n  ctx: AppRenderContext,\n  options?: {\n    actionResult: ActionResult\n    skipFlight: boolean\n  }\n): Promise<RSCPayload> {\n  // Flight data that is going to be passed to the browser.\n  // Currently a single item array but in the future multiple patches might be combined in a single request.\n\n  // We initialize `flightData` to an empty string because the client router knows how to tolerate\n  // it (treating it as an MPA navigation). The only time this function wouldn't generate flight data\n  // is for server actions, if the server action handler instructs this function to skip it. When the server\n  // action reducer sees a falsy value, it'll simply resolve the action with no data.\n  let flightData: FlightData = ''\n\n  const {\n    componentMod: {\n      tree: loaderTree,\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    query,\n    requestId,\n    flightRouterState,\n    workStore,\n    url,\n  } = ctx\n\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n\n  if (!options?.skipFlight) {\n    const preloadCallbacks: PreloadCallbacks = []\n\n    const {\n      ViewportTree,\n      MetadataTree,\n      getViewportReady,\n      getMetadataReady,\n      StreamingMetadataOutlet,\n    } = createMetadataComponents({\n      tree: loaderTree,\n      parsedQuery: query,\n      pathname: url.pathname,\n      metadataContext: createMetadataContext(ctx.renderOpts),\n      getDynamicParamFromSegment,\n      appUsingSizeAdjustment,\n      workStore,\n      MetadataBoundary,\n      ViewportBoundary,\n      serveStreamingMetadata,\n    })\n\n    flightData = (\n      await walkTreeWithFlightRouterState({\n        ctx,\n        loaderTreeToFilter: loaderTree,\n        parentParams: {},\n        flightRouterState,\n        // For flight, render metadata inside leaf page\n        rscHead: (\n          <React.Fragment key={flightDataPathHeadKey}>\n            {/* noindex needs to be blocking */}\n            <NonIndex\n              pagePath={ctx.pagePath}\n              statusCode={ctx.res.statusCode}\n              isPossibleServerAction={ctx.isPossibleServerAction}\n            />\n            {/* Adding requestId as react key to make metadata remount for each render */}\n            <ViewportTree key={getFlightViewportKey(requestId)} />\n            <MetadataTree key={getFlightMetadataKey(requestId)} />\n          </React.Fragment>\n        ),\n        injectedCSS: new Set(),\n        injectedJS: new Set(),\n        injectedFontPreloadTags: new Set(),\n        rootLayoutIncluded: false,\n        getViewportReady,\n        getMetadataReady,\n        preloadCallbacks,\n        StreamingMetadataOutlet,\n      })\n    ).map((path) => path.slice(1)) // remove the '' (root) segment\n  }\n\n  // If we have an action result, then this is a server action response.\n  // We can rely on this because `ActionResult` will always be a promise, even if\n  // the result is falsey.\n  if (options?.actionResult) {\n    return {\n      a: options.actionResult,\n      f: flightData,\n      b: ctx.sharedContext.buildId,\n    }\n  }\n\n  // Otherwise, it's a regular RSC response.\n  return {\n    b: ctx.sharedContext.buildId,\n    f: flightData,\n    S: workStore.isStaticGeneration,\n  }\n}\n\nfunction createErrorContext(\n  ctx: AppRenderContext,\n  renderSource: RequestErrorContext['renderSource']\n): RequestErrorContext {\n  return {\n    routerKind: 'App Router',\n    routePath: ctx.pagePath,\n    // TODO: is this correct if `isPossibleServerAction` is a false positive?\n    routeType: ctx.isPossibleServerAction ? 'action' : 'render',\n    renderSource,\n    revalidateReason: getRevalidateReason(ctx.workStore),\n  }\n}\n/**\n * Produces a RenderResult containing the Flight data for the given request. See\n * `generateDynamicRSCPayload` for information on the contents of the render result.\n */\nasync function generateDynamicFlightRenderResult(\n  req: BaseNextRequest,\n  ctx: AppRenderContext,\n  requestStore: RequestStore,\n  options?: {\n    actionResult: ActionResult\n    skipFlight: boolean\n    componentTree?: CacheNodeSeedData\n    preloadCallbacks?: PreloadCallbacks\n    temporaryReferences?: WeakMap<any, string>\n  }\n): Promise<RenderResult> {\n  const renderOpts = ctx.renderOpts\n\n  function onFlightDataRenderError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components-payload')\n    )\n  }\n  const onError = createFlightReactServerErrorHandler(\n    !!renderOpts.dev,\n    onFlightDataRenderError\n  )\n\n  const RSCPayload: RSCPayload & {\n    /** Only available during dynamicIO development builds. Used for logging errors. */\n    _validation?: Promise<React.ReactNode>\n  } = await workUnitAsyncStorage.run(\n    requestStore,\n    generateDynamicRSCPayload,\n    ctx,\n    options\n  )\n\n  if (\n    // We only want this behavior when running `next dev`\n    renderOpts.dev &&\n    // We only want this behavior when we have React's dev builds available\n    process.env.NODE_ENV === 'development' &&\n    // We only have a Prerender environment for projects opted into dynamicIO\n    renderOpts.experimental.dynamicIO\n  ) {\n    const [resolveValidation, validationOutlet] = createValidationOutlet()\n    RSCPayload._validation = validationOutlet\n\n    spawnDynamicValidationInDev(\n      resolveValidation,\n      ctx.componentMod.tree,\n      ctx,\n      false,\n      ctx.clientReferenceManifest,\n      requestStore\n    )\n  }\n\n  // For app dir, use the bundled version of Flight server renderer (renderToReadableStream)\n  // which contains the subset React.\n  const flightReadableStream = workUnitAsyncStorage.run(\n    requestStore,\n    ctx.componentMod.renderToReadableStream,\n    RSCPayload,\n    ctx.clientReferenceManifest.clientModules,\n    {\n      onError,\n      temporaryReferences: options?.temporaryReferences,\n      filterStackFrame,\n    }\n  )\n\n  return new FlightRenderResult(flightReadableStream, {\n    fetchMetrics: ctx.workStore.fetchMetrics,\n  })\n}\n\n/**\n * Performs a \"warmup\" render of the RSC payload for a given route. This function is called by the server\n * prior to an actual render request in Dev mode only. It's purpose is to fill caches so the actual render\n * can accurately log activity in the right render context (Prerender vs Render).\n *\n * At the moment this implementation is mostly a fork of generateDynamicFlightRenderResult\n */\nasync function warmupDevRender(\n  req: BaseNextRequest,\n  ctx: AppRenderContext\n): Promise<RenderResult> {\n  const {\n    clientReferenceManifest,\n    componentMod: ComponentMod,\n    getDynamicParamFromSegment,\n    implicitTags,\n    renderOpts,\n    workStore,\n  } = ctx\n\n  const {\n    allowEmptyStaticShell = false,\n    dev,\n    onInstrumentationRequestError,\n  } = renderOpts\n\n  if (!dev) {\n    throw new InvariantError(\n      'generateDynamicFlightRenderResult should never be called in `next start` mode.'\n    )\n  }\n\n  const rootParams = getRootParams(\n    ComponentMod.tree,\n    getDynamicParamFromSegment\n  )\n\n  function onFlightDataRenderError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components-payload')\n    )\n  }\n  const onError = createFlightReactServerErrorHandler(\n    true,\n    onFlightDataRenderError\n  )\n\n  // We're doing a dev warmup, so we should create a new resume data cache so\n  // we can fill it.\n  const prerenderResumeDataCache = createPrerenderResumeDataCache()\n\n  const renderController = new AbortController()\n  const prerenderController = new AbortController()\n  const cacheSignal = new CacheSignal()\n\n  const prerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: renderController.signal,\n    controller: prerenderController,\n    cacheSignal,\n    dynamicTracking: null,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash: req.cookies[NEXT_HMR_REFRESH_HASH_COOKIE],\n    captureOwnerStack: ComponentMod.captureOwnerStack,\n  }\n\n  const rscPayload = await workUnitAsyncStorage.run(\n    prerenderStore,\n    generateDynamicRSCPayload,\n    ctx\n  )\n\n  // For app dir, use the bundled version of Flight server renderer (renderToReadableStream)\n  // which contains the subset React.\n  workUnitAsyncStorage.run(\n    prerenderStore,\n    ComponentMod.renderToReadableStream,\n    rscPayload,\n    clientReferenceManifest.clientModules,\n    {\n      filterStackFrame,\n      onError,\n      signal: renderController.signal,\n    }\n  )\n\n  // Wait for all caches to be finished filling and for async imports to resolve\n  trackPendingModules(cacheSignal)\n  await cacheSignal.cacheReady()\n\n  // We unset the cache so any late over-run renders aren't able to write into this cache\n  prerenderStore.prerenderResumeDataCache = null\n  // Abort the render\n  renderController.abort()\n\n  // We don't really want to return a result here but the stack of functions\n  // that calls into renderToHTML... expects a result. We should refactor this to\n  // lift the warmup pathway outside of renderToHTML... but for now this suffices\n  return new FlightRenderResult('', {\n    fetchMetrics: workStore.fetchMetrics,\n    renderResumeDataCache: createRenderResumeDataCache(\n      prerenderResumeDataCache\n    ),\n  })\n}\n\n/**\n * Crawlers will inadvertently think the canonicalUrl in the RSC payload should be crawled\n * when our intention is to just seed the router state with the current URL.\n * This function splits up the pathname so that we can later join it on\n * when we're ready to consume the path.\n */\nfunction prepareInitialCanonicalUrl(url: RequestStore['url']) {\n  return (url.pathname + url.search).split('/')\n}\n\n// This is the data necessary to render <AppRouter /> when no SSR errors are encountered\nasync function getRSCPayload(\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  is404: boolean\n): Promise<InitialRSCPayload & { P: React.ReactNode }> {\n  const injectedCSS = new Set<string>()\n  const injectedJS = new Set<string>()\n  const injectedFontPreloadTags = new Set<string>()\n  let missingSlots: Set<string> | undefined\n\n  // We only track missing parallel slots in development\n  if (process.env.NODE_ENV === 'development') {\n    missingSlots = new Set<string>()\n  }\n\n  const {\n    getDynamicParamFromSegment,\n    query,\n    appUsingSizeAdjustment,\n    componentMod: {\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    url,\n    workStore,\n  } = ctx\n\n  const initialTree = createFlightRouterStateFromLoaderTree(\n    tree,\n    getDynamicParamFromSegment,\n    query\n  )\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n  const hasGlobalNotFound = !!tree[2]['global-not-found']\n\n  const {\n    ViewportTree,\n    MetadataTree,\n    getViewportReady,\n    getMetadataReady,\n    StreamingMetadataOutlet,\n  } = createMetadataComponents({\n    tree,\n    // When it's using global-not-found, metadata errorType is undefined, which will retrieve the\n    // metadata from the page.\n    // When it's using not-found, metadata errorType is 'not-found', which will retrieve the\n    // metadata from the not-found.js boundary.\n    // TODO: remove this condition and keep it undefined when global-not-found is stabilized.\n    errorType: is404 && !hasGlobalNotFound ? 'not-found' : undefined,\n    parsedQuery: query,\n    pathname: url.pathname,\n    metadataContext: createMetadataContext(ctx.renderOpts),\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    workStore,\n    MetadataBoundary,\n    ViewportBoundary,\n    serveStreamingMetadata,\n  })\n\n  const preloadCallbacks: PreloadCallbacks = []\n\n  const seedData = await createComponentTree({\n    ctx,\n    loaderTree: tree,\n    parentParams: {},\n    injectedCSS,\n    injectedJS,\n    injectedFontPreloadTags,\n    rootLayoutIncluded: false,\n    getViewportReady,\n    getMetadataReady,\n    missingSlots,\n    preloadCallbacks,\n    authInterrupts: ctx.renderOpts.experimental.authInterrupts,\n    StreamingMetadataOutlet,\n  })\n\n  // When the `vary` response header is present with `Next-URL`, that means there's a chance\n  // it could respond differently if there's an interception route. We provide this information\n  // to `AppRouter` so that it can properly seed the prefetch cache with a prefix, if needed.\n  const varyHeader = ctx.res.getHeader('vary')\n  const couldBeIntercepted =\n    typeof varyHeader === 'string' && varyHeader.includes(NEXT_URL)\n\n  const initialHead = (\n    <React.Fragment key={flightDataPathHeadKey}>\n      <NonIndex\n        pagePath={ctx.pagePath}\n        statusCode={ctx.res.statusCode}\n        isPossibleServerAction={ctx.isPossibleServerAction}\n      />\n      <ViewportTree />\n      <MetadataTree />\n    </React.Fragment>\n  )\n\n  const { GlobalError, styles: globalErrorStyles } = await getGlobalErrorStyles(\n    tree,\n    ctx\n  )\n\n  // Assume the head we're rendering contains only partial data if PPR is\n  // enabled and this is a statically generated response. This is used by the\n  // client Segment Cache after a prefetch to determine if it can skip the\n  // second request to fill in the dynamic data.\n  //\n  // See similar comment in create-component-tree.tsx for more context.\n  const isPossiblyPartialHead =\n    workStore.isStaticGeneration &&\n    ctx.renderOpts.experimental.isRoutePPREnabled === true\n\n  return {\n    // See the comment above the `Preloads` component (below) for why this is part of the payload\n    P: <Preloads preloadCallbacks={preloadCallbacks} />,\n    b: ctx.sharedContext.buildId,\n    p: ctx.assetPrefix,\n    c: prepareInitialCanonicalUrl(url),\n    i: !!couldBeIntercepted,\n    f: [\n      [\n        initialTree,\n        seedData,\n        initialHead,\n        isPossiblyPartialHead,\n      ] as FlightDataPath,\n    ],\n    m: missingSlots,\n    G: [GlobalError, globalErrorStyles],\n    s: typeof ctx.renderOpts.postponed === 'string',\n    S: workStore.isStaticGeneration,\n  }\n}\n\n/**\n * Preload calls (such as `ReactDOM.preloadStyle` and `ReactDOM.preloadFont`) need to be called during rendering\n * in order to create the appropriate preload tags in the DOM, otherwise they're a no-op. Since we invoke\n * renderToReadableStream with a function that returns component props rather than a component itself, we use\n * this component to \"render  \" the preload calls.\n */\nfunction Preloads({ preloadCallbacks }: { preloadCallbacks: Function[] }) {\n  preloadCallbacks.forEach((preloadFn) => preloadFn())\n  return null\n}\n\n// This is the data necessary to render <AppRouter /> when an error state is triggered\nasync function getErrorRSCPayload(\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  ssrError: unknown,\n  errorType: MetadataErrorType | 'redirect' | undefined\n) {\n  const {\n    getDynamicParamFromSegment,\n    query,\n    appUsingSizeAdjustment,\n    componentMod: {\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    url,\n    workStore,\n  } = ctx\n\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n  const { MetadataTree, ViewportTree } = createMetadataComponents({\n    tree,\n    parsedQuery: query,\n    pathname: url.pathname,\n    metadataContext: createMetadataContext(ctx.renderOpts),\n    errorType,\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    workStore,\n    MetadataBoundary,\n    ViewportBoundary,\n    serveStreamingMetadata: serveStreamingMetadata,\n  })\n\n  const initialHead = (\n    <React.Fragment key={flightDataPathHeadKey}>\n      <NonIndex\n        pagePath={ctx.pagePath}\n        statusCode={ctx.res.statusCode}\n        isPossibleServerAction={ctx.isPossibleServerAction}\n      />\n      <ViewportTree />\n      {process.env.NODE_ENV === 'development' && (\n        <meta name=\"next-error\" content=\"not-found\" />\n      )}\n      <MetadataTree />\n    </React.Fragment>\n  )\n\n  const initialTree = createFlightRouterStateFromLoaderTree(\n    tree,\n    getDynamicParamFromSegment,\n    query\n  )\n\n  let err: Error | undefined = undefined\n  if (ssrError) {\n    err = isError(ssrError) ? ssrError : new Error(ssrError + '')\n  }\n\n  // For metadata notFound error there's no global not found boundary on top\n  // so we create a not found page with AppRouter\n  const seedData: CacheNodeSeedData = [\n    initialTree[0],\n    <html id=\"__next_error__\">\n      <head></head>\n      <body>\n        {process.env.NODE_ENV !== 'production' && err ? (\n          <template\n            data-next-error-message={err.message}\n            data-next-error-digest={'digest' in err ? err.digest : ''}\n            data-next-error-stack={err.stack}\n          />\n        ) : null}\n      </body>\n    </html>,\n    {},\n    null,\n    false,\n  ]\n\n  const { GlobalError, styles: globalErrorStyles } = await getGlobalErrorStyles(\n    tree,\n    ctx\n  )\n\n  const isPossiblyPartialHead =\n    workStore.isStaticGeneration &&\n    ctx.renderOpts.experimental.isRoutePPREnabled === true\n\n  return {\n    b: ctx.sharedContext.buildId,\n    p: ctx.assetPrefix,\n    c: prepareInitialCanonicalUrl(url),\n    m: undefined,\n    i: false,\n    f: [\n      [\n        initialTree,\n        seedData,\n        initialHead,\n        isPossiblyPartialHead,\n      ] as FlightDataPath,\n    ],\n    G: [GlobalError, globalErrorStyles],\n    s: typeof ctx.renderOpts.postponed === 'string',\n    S: workStore.isStaticGeneration,\n  } satisfies InitialRSCPayload\n}\n\nfunction assertClientReferenceManifest(\n  clientReferenceManifest: RenderOpts['clientReferenceManifest']\n): asserts clientReferenceManifest is NonNullable<\n  RenderOpts['clientReferenceManifest']\n> {\n  if (!clientReferenceManifest) {\n    throw new InvariantError('Expected clientReferenceManifest to be defined.')\n  }\n}\n\n// This component must run in an SSR context. It will render the RSC root component\nfunction App<T>({\n  reactServerStream,\n  preinitScripts,\n  clientReferenceManifest,\n  ServerInsertedHTMLProvider,\n  gracefullyDegrade,\n  nonce,\n}: {\n  reactServerStream: BinaryStreamOf<T>\n  preinitScripts: () => void\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>\n  ServerInsertedHTMLProvider: React.ComponentType<{ children: JSX.Element }>\n  gracefullyDegrade: boolean\n  nonce?: string\n}): JSX.Element {\n  preinitScripts()\n  const response = React.use(\n    useFlightStream<InitialRSCPayload>(\n      reactServerStream,\n      clientReferenceManifest,\n      nonce\n    )\n  )\n\n  const initialState = createInitialRouterState({\n    // This is not used during hydration, so we don't have to pass a\n    // real timestamp.\n    navigatedAt: -1,\n    initialFlightData: response.f,\n    initialCanonicalUrlParts: response.c,\n    initialParallelRoutes: new Map(),\n    // location is not initialized in the SSR render\n    // it's set to window.location during hydration\n    location: null,\n    couldBeIntercepted: response.i,\n    postponed: response.s,\n    prerendered: response.S,\n  })\n\n  const actionQueue = createMutableActionQueue(initialState, null)\n\n  const { HeadManagerContext } =\n    require('../../shared/lib/head-manager-context.shared-runtime') as typeof import('../../shared/lib/head-manager-context.shared-runtime')\n\n  return (\n    <HeadManagerContext.Provider\n      value={{\n        appDir: true,\n        nonce,\n      }}\n    >\n      <ServerInsertedHTMLProvider>\n        <AppRouter\n          actionQueue={actionQueue}\n          globalErrorState={response.G}\n          assetPrefix={response.p}\n          gracefullyDegrade={gracefullyDegrade}\n        />\n      </ServerInsertedHTMLProvider>\n    </HeadManagerContext.Provider>\n  )\n}\n\n// @TODO our error stream should be probably just use the same root component. But it was previously\n// different I don't want to figure out if that is meaningful at this time so just keeping the behavior\n// consistent for now.\nfunction ErrorApp<T>({\n  reactServerStream,\n  preinitScripts,\n  clientReferenceManifest,\n  ServerInsertedHTMLProvider,\n  gracefullyDegrade,\n  nonce,\n}: {\n  reactServerStream: BinaryStreamOf<T>\n  preinitScripts: () => void\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>\n  ServerInsertedHTMLProvider: React.ComponentType<{ children: JSX.Element }>\n  gracefullyDegrade: boolean\n  nonce?: string\n}): JSX.Element {\n  preinitScripts()\n  const response = React.use(\n    useFlightStream<InitialRSCPayload>(\n      reactServerStream,\n      clientReferenceManifest,\n      nonce\n    )\n  )\n\n  const initialState = createInitialRouterState({\n    // This is not used during hydration, so we don't have to pass a\n    // real timestamp.\n    navigatedAt: -1,\n    initialFlightData: response.f,\n    initialCanonicalUrlParts: response.c,\n    initialParallelRoutes: new Map(),\n    // location is not initialized in the SSR render\n    // it's set to window.location during hydration\n    location: null,\n    couldBeIntercepted: response.i,\n    postponed: response.s,\n    prerendered: response.S,\n  })\n\n  const actionQueue = createMutableActionQueue(initialState, null)\n\n  return (\n    <ServerInsertedHTMLProvider>\n      <AppRouter\n        actionQueue={actionQueue}\n        globalErrorState={response.G}\n        assetPrefix={response.p}\n        gracefullyDegrade={gracefullyDegrade}\n      />\n    </ServerInsertedHTMLProvider>\n  )\n}\n\n// We use a trick with TS Generics to branch streams with a type so we can\n// consume the parsed value of a Readable Stream if it was constructed with a\n// certain object shape. The generic type is not used directly in the type so it\n// requires a disabling of the eslint rule disallowing unused vars\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type BinaryStreamOf<T> = ReadableStream<Uint8Array>\n\nasync function renderToHTMLOrFlightImpl(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  url: ReturnType<typeof parseRelativeUrl>,\n  pagePath: string,\n  query: NextParsedUrlQuery,\n  renderOpts: RenderOpts,\n  workStore: WorkStore,\n  parsedRequestHeaders: ParsedRequestHeaders,\n  requestEndedState: { ended?: boolean },\n  postponedState: PostponedState | null,\n  serverComponentsHmrCache: ServerComponentsHmrCache | undefined,\n  sharedContext: AppSharedContext\n) {\n  const isNotFoundPath = pagePath === '/404'\n  if (isNotFoundPath) {\n    res.statusCode = 404\n  }\n\n  // A unique request timestamp used by development to ensure that it's\n  // consistent and won't change during this request. This is important to\n  // avoid that resources can be deduped by React Float if the same resource is\n  // rendered or preloaded multiple times: `<link href=\"a.css?v={Date.now()}\"/>`.\n  const requestTimestamp = Date.now()\n\n  const {\n    clientReferenceManifest,\n    serverActionsManifest,\n    ComponentMod,\n    nextFontManifest,\n    serverActions,\n    assetPrefix = '',\n    enableTainting,\n  } = renderOpts\n\n  // We need to expose the bundled `require` API globally for\n  // react-server-dom-webpack. This is a hack until we find a better way.\n  if (ComponentMod.__next_app__) {\n    const instrumented = wrapClientComponentLoader(ComponentMod)\n\n    // When we are prerendering if there is a cacheSignal for tracking\n    // cache reads we track calls to `loadChunk` and `require`. This allows us\n    // to treat chunk/module loading with similar semantics as cache reads to avoid\n    // module loading from causing a prerender to abort too early.\n\n    const shouldTrackModuleLoading = () => {\n      if (!renderOpts.experimental.dynamicIO) {\n        return false\n      }\n      if (renderOpts.dev) {\n        return true\n      }\n      const workUnitStore = workUnitAsyncStorage.getStore()\n      return !!(\n        workUnitStore &&\n        (workUnitStore.type === 'prerender' ||\n          workUnitStore.type === 'prerender-client' ||\n          workUnitStore.type === 'cache')\n      )\n    }\n\n    const __next_require__: typeof instrumented.require = (...args) => {\n      const exportsOrPromise = instrumented.require(...args)\n      if (shouldTrackModuleLoading()) {\n        // requiring an async module returns a promise.\n        trackPendingImport(exportsOrPromise)\n      }\n      return exportsOrPromise\n    }\n    // @ts-expect-error\n    globalThis.__next_require__ = __next_require__\n\n    const __next_chunk_load__: typeof instrumented.loadChunk = (...args) => {\n      const loadingChunk = instrumented.loadChunk(...args)\n      if (shouldTrackModuleLoading()) {\n        trackPendingChunkLoad(loadingChunk)\n      }\n      return loadingChunk\n    }\n    // @ts-expect-error\n    globalThis.__next_chunk_load__ = __next_chunk_load__\n  }\n\n  if (process.env.NODE_ENV === 'development') {\n    // reset isr status at start of request\n    const { pathname } = new URL(req.url || '/', 'http://n')\n    renderOpts.setIsrStatus?.(pathname, null)\n  }\n\n  if (\n    // The type check here ensures that `req` is correctly typed, and the\n    // environment variable check provides dead code elimination.\n    process.env.NEXT_RUNTIME !== 'edge' &&\n    isNodeNextRequest(req)\n  ) {\n    req.originalRequest.on('end', () => {\n      requestEndedState.ended = true\n\n      if ('performance' in globalThis) {\n        const metrics = getClientComponentLoaderMetrics({ reset: true })\n        if (metrics) {\n          getTracer()\n            .startSpan(NextNodeServerSpan.clientComponentLoading, {\n              startTime: metrics.clientComponentLoadStart,\n              attributes: {\n                'next.clientComponentLoadCount':\n                  metrics.clientComponentLoadCount,\n                'next.span_type': NextNodeServerSpan.clientComponentLoading,\n              },\n            })\n            .end(\n              metrics.clientComponentLoadStart +\n                metrics.clientComponentLoadTimes\n            )\n        }\n      }\n    })\n  }\n\n  const metadata: AppPageRenderResultMetadata = {\n    statusCode: isNotFoundPath ? 404 : undefined,\n  }\n\n  const appUsingSizeAdjustment = !!nextFontManifest?.appUsingSizeAdjust\n\n  assertClientReferenceManifest(clientReferenceManifest)\n\n  const serverModuleMap = createServerModuleMap({ serverActionsManifest })\n\n  setReferenceManifestsSingleton({\n    page: workStore.page,\n    clientReferenceManifest,\n    serverActionsManifest,\n    serverModuleMap,\n  })\n\n  ComponentMod.patchFetch()\n\n  // Pull out the hooks/references from the component.\n  const { tree: loaderTree, taintObjectReference } = ComponentMod\n  if (enableTainting) {\n    taintObjectReference(\n      'Do not pass process.env to Client Components since it will leak sensitive data',\n      process.env\n    )\n  }\n\n  workStore.fetchMetrics = []\n  metadata.fetchMetrics = workStore.fetchMetrics\n\n  // don't modify original query object\n  query = { ...query }\n  stripInternalQueries(query)\n\n  const {\n    flightRouterState,\n    isPrefetchRequest,\n    isRSCRequest,\n    isDevWarmupRequest,\n    isHmrRefresh,\n    nonce,\n  } = parsedRequestHeaders\n\n  const { isStaticGeneration, fallbackRouteParams } = workStore\n\n  /**\n   * The metadata items array created in next-app-loader with all relevant information\n   * that we need to resolve the final metadata.\n   */\n  let requestId: string\n\n  if (isStaticGeneration) {\n    requestId = Buffer.from(\n      await crypto.subtle.digest('SHA-1', Buffer.from(req.url))\n    ).toString('hex')\n  } else if (process.env.NEXT_RUNTIME === 'edge') {\n    requestId = crypto.randomUUID()\n  } else {\n    requestId = (\n      require('next/dist/compiled/nanoid') as typeof import('next/dist/compiled/nanoid')\n    ).nanoid()\n  }\n\n  /**\n   * Dynamic parameters. E.g. when you visit `/dashboard/vercel` which is rendered by `/dashboard/[slug]` the value will be {\"slug\": \"vercel\"}.\n   */\n  const params = renderOpts.params ?? {}\n\n  const getDynamicParamFromSegment = makeGetDynamicParamFromSegment(\n    params,\n    pagePath,\n    fallbackRouteParams\n  )\n\n  const isPossibleActionRequest = getIsPossibleServerAction(req)\n\n  const implicitTags = await getImplicitTags(\n    workStore.page,\n    url,\n    fallbackRouteParams\n  )\n\n  const ctx: AppRenderContext = {\n    componentMod: ComponentMod,\n    url,\n    renderOpts,\n    workStore,\n    parsedRequestHeaders,\n    getDynamicParamFromSegment,\n    query,\n    isPrefetch: isPrefetchRequest,\n    isPossibleServerAction: isPossibleActionRequest,\n    requestTimestamp,\n    appUsingSizeAdjustment,\n    flightRouterState,\n    requestId,\n    pagePath,\n    clientReferenceManifest,\n    assetPrefix,\n    isNotFoundPath,\n    nonce,\n    res,\n    sharedContext,\n    implicitTags,\n  }\n\n  getTracer().setRootSpanAttribute('next.route', pagePath)\n\n  if (isStaticGeneration) {\n    // We're either building or revalidating. In either case we need to\n    // prerender our page rather than render it.\n    const prerenderToStreamWithTracing = getTracer().wrap(\n      AppRenderSpan.getBodyResult,\n      {\n        spanName: `prerender route (app) ${pagePath}`,\n        attributes: {\n          'next.route': pagePath,\n        },\n      },\n      prerenderToStream\n    )\n\n    const response = await prerenderToStreamWithTracing(\n      req,\n      res,\n      ctx,\n      metadata,\n      loaderTree\n    )\n\n    // If we're debugging partial prerendering, print all the dynamic API accesses\n    // that occurred during the render.\n    // @TODO move into renderToStream function\n    if (\n      response.dynamicAccess &&\n      accessedDynamicData(response.dynamicAccess) &&\n      renderOpts.isDebugDynamicAccesses\n    ) {\n      warn('The following dynamic usage was detected:')\n      for (const access of formatDynamicAPIAccesses(response.dynamicAccess)) {\n        warn(access)\n      }\n    }\n\n    // If we encountered any unexpected errors during build we fail the\n    // prerendering phase and the build.\n    if (workStore.invalidDynamicUsageError) {\n      throw workStore.invalidDynamicUsageError\n    }\n    if (response.digestErrorsMap.size) {\n      const buildFailingError = response.digestErrorsMap.values().next().value\n      if (buildFailingError) throw buildFailingError\n    }\n    // Pick first userland SSR error, which is also not a RSC error.\n    if (response.ssrErrors.length) {\n      const buildFailingError = response.ssrErrors.find((err) =>\n        isUserLandError(err)\n      )\n      if (buildFailingError) throw buildFailingError\n    }\n\n    const options: RenderResultOptions = {\n      metadata,\n    }\n    // If we have pending revalidates, wait until they are all resolved.\n    if (\n      workStore.pendingRevalidates ||\n      workStore.pendingRevalidateWrites ||\n      workStore.pendingRevalidatedTags\n    ) {\n      const pendingPromise = executeRevalidates(workStore).finally(() => {\n        if (process.env.NEXT_PRIVATE_DEBUG_CACHE) {\n          console.log('pending revalidates promise finished for:', url)\n        }\n      })\n\n      if (renderOpts.waitUntil) {\n        renderOpts.waitUntil(pendingPromise)\n      } else {\n        options.waitUntil = pendingPromise\n      }\n    }\n\n    if (response.collectedTags) {\n      metadata.fetchTags = response.collectedTags.join(',')\n    }\n\n    // Let the client router know how long to keep the cached entry around.\n    const staleHeader = String(response.collectedStale)\n    res.setHeader(NEXT_ROUTER_STALE_TIME_HEADER, staleHeader)\n    metadata.headers ??= {}\n    metadata.headers[NEXT_ROUTER_STALE_TIME_HEADER] = staleHeader\n\n    // If force static is specifically set to false, we should not revalidate\n    // the page.\n    if (workStore.forceStatic === false || response.collectedRevalidate === 0) {\n      metadata.cacheControl = { revalidate: 0, expire: undefined }\n    } else {\n      // Copy the cache control value onto the render result metadata.\n      metadata.cacheControl = {\n        revalidate:\n          response.collectedRevalidate >= INFINITE_CACHE\n            ? false\n            : response.collectedRevalidate,\n        expire:\n          response.collectedExpire >= INFINITE_CACHE\n            ? undefined\n            : response.collectedExpire,\n      }\n    }\n\n    // provide bailout info for debugging\n    if (metadata.cacheControl?.revalidate === 0) {\n      metadata.staticBailoutInfo = {\n        description: workStore.dynamicUsageDescription,\n        stack: workStore.dynamicUsageStack,\n      }\n    }\n\n    if (response.renderResumeDataCache) {\n      metadata.renderResumeDataCache = response.renderResumeDataCache\n    }\n\n    return new RenderResult(await streamToString(response.stream), options)\n  } else {\n    // We're rendering dynamically\n    const renderResumeDataCache =\n      renderOpts.renderResumeDataCache ?? postponedState?.renderResumeDataCache\n\n    const rootParams = getRootParams(loaderTree, ctx.getDynamicParamFromSegment)\n    const requestStore = createRequestStoreForRender(\n      req,\n      res,\n      url,\n      rootParams,\n      implicitTags,\n      renderOpts.onUpdateCookies,\n      renderOpts.previewProps,\n      isHmrRefresh,\n      serverComponentsHmrCache,\n      renderResumeDataCache\n    )\n\n    if (\n      process.env.NODE_ENV === 'development' &&\n      renderOpts.setIsrStatus &&\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      isNodeNextRequest(req) &&\n      !isDevWarmupRequest\n    ) {\n      const setIsrStatus = renderOpts.setIsrStatus\n      req.originalRequest.on('end', () => {\n        if (!requestStore.usedDynamic && !workStore.forceDynamic) {\n          // only node can be ISR so we only need to update the status here\n          const { pathname } = new URL(req.url || '/', 'http://n')\n          setIsrStatus(pathname, true)\n        }\n      })\n    }\n\n    if (isDevWarmupRequest) {\n      return warmupDevRender(req, ctx)\n    } else if (isRSCRequest) {\n      return generateDynamicFlightRenderResult(req, ctx, requestStore)\n    }\n\n    const renderToStreamWithTracing = getTracer().wrap(\n      AppRenderSpan.getBodyResult,\n      {\n        spanName: `render route (app) ${pagePath}`,\n        attributes: {\n          'next.route': pagePath,\n        },\n      },\n      renderToStream\n    )\n\n    let formState: null | any = null\n    if (isPossibleActionRequest) {\n      // For action requests, we handle them differently with a special render result.\n      const actionRequestResult = await handleAction({\n        req,\n        res,\n        ComponentMod,\n        serverModuleMap,\n        generateFlight: generateDynamicFlightRenderResult,\n        workStore,\n        requestStore,\n        serverActions,\n        ctx,\n        metadata,\n      })\n\n      if (actionRequestResult) {\n        if (actionRequestResult.type === 'not-found') {\n          const notFoundLoaderTree = createNotFoundLoaderTree(loaderTree)\n          res.statusCode = 404\n          metadata.statusCode = 404\n          const stream = await renderToStreamWithTracing(\n            requestStore,\n            req,\n            res,\n            ctx,\n            notFoundLoaderTree,\n            formState,\n            postponedState,\n            metadata\n          )\n\n          return new RenderResult(stream, { metadata })\n        } else if (actionRequestResult.type === 'done') {\n          if (actionRequestResult.result) {\n            actionRequestResult.result.assignMetadata(metadata)\n            return actionRequestResult.result\n          } else if (actionRequestResult.formState) {\n            formState = actionRequestResult.formState\n          }\n        }\n      }\n    }\n\n    const options: RenderResultOptions = {\n      metadata,\n    }\n\n    const stream = await renderToStreamWithTracing(\n      requestStore,\n      req,\n      res,\n      ctx,\n      loaderTree,\n      formState,\n      postponedState,\n      metadata\n    )\n\n    if (workStore.invalidDynamicUsageError) {\n      throw workStore.invalidDynamicUsageError\n    }\n\n    // If we have pending revalidates, wait until they are all resolved.\n    if (\n      workStore.pendingRevalidates ||\n      workStore.pendingRevalidateWrites ||\n      workStore.pendingRevalidatedTags\n    ) {\n      const pendingPromise = executeRevalidates(workStore).finally(() => {\n        if (process.env.NEXT_PRIVATE_DEBUG_CACHE) {\n          console.log('pending revalidates promise finished for:', url)\n        }\n      })\n\n      if (renderOpts.waitUntil) {\n        renderOpts.waitUntil(pendingPromise)\n      } else {\n        options.waitUntil = pendingPromise\n      }\n    }\n\n    // Create the new render result for the response.\n    return new RenderResult(stream, options)\n  }\n}\n\nexport type AppPageRender = (\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  pagePath: string,\n  query: NextParsedUrlQuery,\n  fallbackRouteParams: FallbackRouteParams | null,\n  renderOpts: RenderOpts,\n  serverComponentsHmrCache: ServerComponentsHmrCache | undefined,\n  isDevWarmup: boolean,\n  sharedContext: AppSharedContext\n) => Promise<RenderResult<AppPageRenderResultMetadata>>\n\nexport const renderToHTMLOrFlight: AppPageRender = (\n  req,\n  res,\n  pagePath,\n  query,\n  fallbackRouteParams,\n  renderOpts,\n  serverComponentsHmrCache,\n  isDevWarmup,\n  sharedContext\n) => {\n  if (!req.url) {\n    throw new Error('Invalid URL')\n  }\n\n  const url = parseRelativeUrl(req.url, undefined, false)\n\n  // We read these values from the request object as, in certain cases,\n  // base-server will strip them to opt into different rendering behavior.\n  const parsedRequestHeaders = parseRequestHeaders(req.headers, {\n    isDevWarmup,\n    isRoutePPREnabled: renderOpts.experimental.isRoutePPREnabled === true,\n    previewModeId: renderOpts.previewProps?.previewModeId,\n  })\n\n  const { isPrefetchRequest, previouslyRevalidatedTags } = parsedRequestHeaders\n\n  const requestEndedState = { ended: false }\n  let postponedState: PostponedState | null = null\n\n  // If provided, the postpone state should be parsed so it can be provided to\n  // React.\n  if (typeof renderOpts.postponed === 'string') {\n    if (fallbackRouteParams) {\n      throw new InvariantError(\n        'postponed state should not be provided when fallback params are provided'\n      )\n    }\n\n    postponedState = parsePostponedState(\n      renderOpts.postponed,\n      renderOpts.params\n    )\n  }\n\n  if (\n    postponedState?.renderResumeDataCache &&\n    renderOpts.renderResumeDataCache\n  ) {\n    throw new InvariantError(\n      'postponed state and dev warmup immutable resume data cache should not be provided together'\n    )\n  }\n\n  const workStore = createWorkStore({\n    page: renderOpts.routeModule.definition.page,\n    fallbackRouteParams,\n    renderOpts,\n    requestEndedState,\n    // @TODO move to workUnitStore of type Request\n    isPrefetchRequest,\n    buildId: sharedContext.buildId,\n    previouslyRevalidatedTags,\n  })\n\n  return workAsyncStorage.run(\n    workStore,\n    // The function to run\n    renderToHTMLOrFlightImpl,\n    // all of it's args\n    req,\n    res,\n    url,\n    pagePath,\n    query,\n    renderOpts,\n    workStore,\n    parsedRequestHeaders,\n    requestEndedState,\n    postponedState,\n    serverComponentsHmrCache,\n    sharedContext\n  )\n}\n\nasync function renderToStream(\n  requestStore: RequestStore,\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  ctx: AppRenderContext,\n  tree: LoaderTree,\n  formState: any,\n  postponedState: PostponedState | null,\n  metadata: AppPageRenderResultMetadata\n): Promise<ReadableStream<Uint8Array>> {\n  const { assetPrefix, nonce, pagePath, renderOpts } = ctx\n\n  const {\n    basePath,\n    botType,\n    buildManifest,\n    clientReferenceManifest,\n    ComponentMod,\n    crossOrigin,\n    dev = false,\n    experimental,\n    nextExport = false,\n    onInstrumentationRequestError,\n    page,\n    reactMaxHeadersLength,\n    shouldWaitOnAllReady,\n    subresourceIntegrityManifest,\n    supportsDynamicResponse,\n  } = renderOpts\n\n  assertClientReferenceManifest(clientReferenceManifest)\n\n  const { ServerInsertedHTMLProvider, renderServerInsertedHTML } =\n    createServerInsertedHTML()\n  const getServerInsertedMetadata = createServerInsertedMetadata(nonce)\n\n  const tracingMetadata = getTracedMetadata(\n    getTracer().getTracePropagationData(),\n    experimental.clientTraceMetadata\n  )\n\n  const polyfills: JSX.IntrinsicElements['script'][] =\n    buildManifest.polyfillFiles\n      .filter(\n        (polyfill) =>\n          polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n      )\n      .map((polyfill) => ({\n        src: `${assetPrefix}/_next/${polyfill}${getAssetQueryString(\n          ctx,\n          false\n        )}`,\n        integrity: subresourceIntegrityManifest?.[polyfill],\n        crossOrigin,\n        noModule: true,\n        nonce,\n      }))\n\n  const [preinitScripts, bootstrapScript] = getRequiredScripts(\n    buildManifest,\n    // Why is assetPrefix optional on renderOpts?\n    // @TODO make it default empty string on renderOpts and get rid of it from ctx\n    assetPrefix,\n    crossOrigin,\n    subresourceIntegrityManifest,\n    getAssetQueryString(ctx, true),\n    nonce,\n    page\n  )\n\n  const reactServerErrorsByDigest: Map<string, DigestedError> = new Map()\n  const silenceLogger = false\n  function onHTMLRenderRSCError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components')\n    )\n  }\n  const serverComponentsErrorHandler = createHTMLReactServerErrorHandler(\n    dev,\n    nextExport,\n    reactServerErrorsByDigest,\n    silenceLogger,\n    onHTMLRenderRSCError\n  )\n\n  function onHTMLRenderSSRError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'server-rendering')\n    )\n  }\n\n  const allCapturedErrors: Array<unknown> = []\n  const htmlRendererErrorHandler = createHTMLErrorHandler(\n    dev,\n    nextExport,\n    reactServerErrorsByDigest,\n    allCapturedErrors,\n    silenceLogger,\n    onHTMLRenderSSRError\n  )\n\n  let reactServerResult: null | ReactServerResult = null\n\n  const setHeader = res.setHeader.bind(res)\n  const appendHeader = res.appendHeader.bind(res)\n\n  try {\n    if (\n      // We only want this behavior when running `next dev`\n      dev &&\n      // We only want this behavior when we have React's dev builds available\n      process.env.NODE_ENV === 'development' &&\n      // Edge routes never prerender so we don't have a Prerender environment for anything in edge runtime\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      // We only have a Prerender environment for projects opted into dynamicIO\n      experimental.dynamicIO\n    ) {\n      // This is a dynamic render. We don't do dynamic tracking because we're not prerendering\n      const RSCPayload: InitialRSCPayload & {\n        /** Only available during dynamicIO development builds. Used for logging errors. */\n        _validation?: Promise<React.ReactNode>\n      } = await workUnitAsyncStorage.run(\n        requestStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      const [resolveValidation, validationOutlet] = createValidationOutlet()\n      RSCPayload._validation = validationOutlet\n\n      const reactServerStream = await workUnitAsyncStorage.run(\n        requestStore,\n        scheduleInSequentialTasks,\n        () => {\n          requestStore.prerenderPhase = true\n          return ComponentMod.renderToReadableStream(\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError: serverComponentsErrorHandler,\n              environmentName: () =>\n                requestStore.prerenderPhase === true ? 'Prerender' : 'Server',\n              filterStackFrame,\n            }\n          )\n        },\n        () => {\n          requestStore.prerenderPhase = false\n        }\n      )\n\n      spawnDynamicValidationInDev(\n        resolveValidation,\n        tree,\n        ctx,\n        res.statusCode === 404,\n        clientReferenceManifest,\n        requestStore\n      )\n\n      reactServerResult = new ReactServerResult(reactServerStream)\n    } else {\n      // This is a dynamic render. We don't do dynamic tracking because we're not prerendering\n      const RSCPayload = await workUnitAsyncStorage.run(\n        requestStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n\n      reactServerResult = new ReactServerResult(\n        workUnitAsyncStorage.run(\n          requestStore,\n          ComponentMod.renderToReadableStream,\n          RSCPayload,\n          clientReferenceManifest.clientModules,\n          {\n            filterStackFrame,\n            onError: serverComponentsErrorHandler,\n          }\n        )\n      )\n    }\n\n    // React doesn't start rendering synchronously but we want the RSC render to have a chance to start\n    // before we begin SSR rendering because we want to capture any available preload headers so we tick\n    // one task before continuing\n    await waitAtLeastOneReactRenderTask()\n\n    // If provided, the postpone state should be parsed as JSON so it can be\n    // provided to React.\n    if (typeof renderOpts.postponed === 'string') {\n      if (postponedState?.type === DynamicState.DATA) {\n        // We have a complete HTML Document in the prerender but we need to\n        // still include the new server component render because it was not included\n        // in the static prelude.\n        const inlinedReactServerDataStream = createInlinedDataReadableStream(\n          reactServerResult.tee(),\n          nonce,\n          formState\n        )\n\n        return chainStreams(\n          inlinedReactServerDataStream,\n          createDocumentClosingStream()\n        )\n      } else if (postponedState) {\n        // We assume we have dynamic HTML requiring a resume render to complete\n        const postponed = getPostponedFromState(postponedState)\n\n        const resume = (\n          require('react-dom/server') as typeof import('react-dom/server')\n        ).resume\n\n        const htmlStream = await workUnitAsyncStorage.run(\n          requestStore,\n          resume,\n          <App\n            reactServerStream={reactServerResult.tee()}\n            preinitScripts={preinitScripts}\n            clientReferenceManifest={clientReferenceManifest}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            nonce={nonce}\n            gracefullyDegrade={!!botType}\n          />,\n          postponed,\n          { onError: htmlRendererErrorHandler, nonce }\n        )\n\n        const getServerInsertedHTML = makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: allCapturedErrors,\n          basePath,\n          tracingMetadata: tracingMetadata,\n        })\n        return await continueDynamicHTMLResume(htmlStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            reactServerResult.consume(),\n            nonce,\n            formState\n          ),\n          getServerInsertedHTML,\n          getServerInsertedMetadata,\n        })\n      }\n    }\n\n    // This is a regular dynamic render\n    const renderToReadableStream = (\n      require('react-dom/server') as typeof import('react-dom/server')\n    ).renderToReadableStream\n\n    const htmlStream = await workUnitAsyncStorage.run(\n      requestStore,\n      renderToReadableStream,\n      <App\n        reactServerStream={reactServerResult.tee()}\n        preinitScripts={preinitScripts}\n        clientReferenceManifest={clientReferenceManifest}\n        ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n        gracefullyDegrade={!!botType}\n        nonce={nonce}\n      />,\n      {\n        onError: htmlRendererErrorHandler,\n        nonce,\n        onHeaders: (headers: Headers) => {\n          headers.forEach((value, key) => {\n            appendHeader(key, value)\n          })\n        },\n        maxHeadersLength: reactMaxHeadersLength,\n        bootstrapScripts: [bootstrapScript],\n        formState,\n      }\n    )\n\n    const getServerInsertedHTML = makeGetServerInsertedHTML({\n      polyfills,\n      renderServerInsertedHTML,\n      serverCapturedErrors: allCapturedErrors,\n      basePath,\n      tracingMetadata: tracingMetadata,\n    })\n    /**\n     * Rules of Static & Dynamic HTML:\n     *\n     *    1.) We must generate static HTML unless the caller explicitly opts\n     *        in to dynamic HTML support.\n     *\n     *    2.) If dynamic HTML support is requested, we must honor that request\n     *        or throw an error. It is the sole responsibility of the caller to\n     *        ensure they aren't e.g. requesting dynamic HTML for an AMP page.\n     *\n     *   3.) If `shouldWaitOnAllReady` is true, which indicates we need to\n     *       resolve all suspenses and generate a full HTML. e.g. when it's a\n     *       html limited bot requests, we produce the full HTML content.\n     *\n     * These rules help ensure that other existing features like request caching,\n     * coalescing, and ISR continue working as intended.\n     */\n    const generateStaticHTML =\n      supportsDynamicResponse !== true || !!shouldWaitOnAllReady\n\n    return await continueFizzStream(htmlStream, {\n      inlinedDataStream: createInlinedDataReadableStream(\n        reactServerResult.consume(),\n        nonce,\n        formState\n      ),\n      isStaticGeneration: generateStaticHTML,\n      isBuildTimePrerendering: ctx.workStore.isBuildTimePrerendering === true,\n      buildId: ctx.workStore.buildId,\n      getServerInsertedHTML,\n      getServerInsertedMetadata,\n      validateRootLayout: dev,\n    })\n  } catch (err) {\n    if (\n      isStaticGenBailoutError(err) ||\n      (typeof err === 'object' &&\n        err !== null &&\n        'message' in err &&\n        typeof err.message === 'string' &&\n        err.message.includes(\n          'https://nextjs.org/docs/advanced-features/static-html-export'\n        ))\n    ) {\n      // Ensure that \"next dev\" prints the red error overlay\n      throw err\n    }\n\n    // If a bailout made it to this point, it means it wasn't wrapped inside\n    // a suspense boundary.\n    const shouldBailoutToCSR = isBailoutToCSRError(err)\n    if (shouldBailoutToCSR) {\n      const stack = getStackWithoutErrorMessage(err)\n      error(\n        `${err.reason} should be wrapped in a suspense boundary at page \"${pagePath}\". Read more: https://nextjs.org/docs/messages/missing-suspense-with-csr-bailout\\n${stack}`\n      )\n\n      throw err\n    }\n\n    let errorType: MetadataErrorType | 'redirect' | undefined\n\n    if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n      metadata.statusCode = res.statusCode\n      errorType = getAccessFallbackErrorTypeByStatus(res.statusCode)\n    } else if (isRedirectError(err)) {\n      errorType = 'redirect'\n      res.statusCode = getRedirectStatusCodeFromError(err)\n      metadata.statusCode = res.statusCode\n\n      const redirectUrl = addPathPrefix(getURLFromRedirectError(err), basePath)\n\n      // If there were mutable cookies set, we need to set them on the\n      // response.\n      const headers = new Headers()\n      if (appendMutableCookies(headers, requestStore.mutableCookies)) {\n        setHeader('set-cookie', Array.from(headers.values()))\n      }\n\n      setHeader('location', redirectUrl)\n    } else if (!shouldBailoutToCSR) {\n      res.statusCode = 500\n      metadata.statusCode = res.statusCode\n    }\n\n    const [errorPreinitScripts, errorBootstrapScript] = getRequiredScripts(\n      buildManifest,\n      assetPrefix,\n      crossOrigin,\n      subresourceIntegrityManifest,\n      getAssetQueryString(ctx, false),\n      nonce,\n      '/_not-found/page'\n    )\n\n    const errorRSCPayload = await workUnitAsyncStorage.run(\n      requestStore,\n      getErrorRSCPayload,\n      tree,\n      ctx,\n      reactServerErrorsByDigest.has((err as any).digest) ? null : err,\n      errorType\n    )\n\n    const errorServerStream = workUnitAsyncStorage.run(\n      requestStore,\n      ComponentMod.renderToReadableStream,\n      errorRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        filterStackFrame,\n        onError: serverComponentsErrorHandler,\n      }\n    )\n\n    if (reactServerResult === null) {\n      // We errored when we did not have an RSC stream to read from. This is not just a render\n      // error, we need to throw early\n      throw err\n    }\n\n    try {\n      const fizzStream = await workUnitAsyncStorage.run(\n        requestStore,\n        renderToInitialFizzStream,\n        {\n          ReactDOMServer:\n            require('react-dom/server') as typeof import('react-dom/server'),\n          element: (\n            <ErrorApp\n              reactServerStream={errorServerStream}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              preinitScripts={errorPreinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              gracefullyDegrade={!!botType}\n              nonce={nonce}\n            />\n          ),\n          streamOptions: {\n            nonce,\n            // Include hydration scripts in the HTML\n            bootstrapScripts: [errorBootstrapScript],\n            formState,\n          },\n        }\n      )\n\n      /**\n       * Rules of Static & Dynamic HTML:\n       *\n       *    1.) We must generate static HTML unless the caller explicitly opts\n       *        in to dynamic HTML support.\n       *\n       *    2.) If dynamic HTML support is requested, we must honor that request\n       *        or throw an error. It is the sole responsibility of the caller to\n       *        ensure they aren't e.g. requesting dynamic HTML for an AMP page.\n       *    3.) If `shouldWaitOnAllReady` is true, which indicates we need to\n       *        resolve all suspenses and generate a full HTML. e.g. when it's a\n       *        html limited bot requests, we produce the full HTML content.\n       *\n       * These rules help ensure that other existing features like request caching,\n       * coalescing, and ISR continue working as intended.\n       */\n      const generateStaticHTML =\n        supportsDynamicResponse !== true || !!shouldWaitOnAllReady\n      return await continueFizzStream(fizzStream, {\n        inlinedDataStream: createInlinedDataReadableStream(\n          // This is intentionally using the readable datastream from the\n          // main render rather than the flight data from the error page\n          // render\n          reactServerResult.consume(),\n          nonce,\n          formState\n        ),\n        isStaticGeneration: generateStaticHTML,\n        isBuildTimePrerendering: ctx.workStore.isBuildTimePrerendering === true,\n        buildId: ctx.workStore.buildId,\n        getServerInsertedHTML: makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: [],\n          basePath,\n          tracingMetadata: tracingMetadata,\n        }),\n        getServerInsertedMetadata,\n        validateRootLayout: dev,\n      })\n    } catch (finalErr: any) {\n      if (\n        process.env.NODE_ENV === 'development' &&\n        isHTTPAccessFallbackError(finalErr)\n      ) {\n        const { bailOnRootNotFound } =\n          require('../../client/components/dev-root-http-access-fallback-boundary') as typeof import('../../client/components/dev-root-http-access-fallback-boundary')\n        bailOnRootNotFound()\n      }\n      throw finalErr\n    }\n  }\n}\n\nfunction createValidationOutlet() {\n  let resolveValidation: (value: React.ReactNode) => void\n  let outlet = new Promise<React.ReactNode>((resolve) => {\n    resolveValidation = resolve\n  })\n  return [resolveValidation!, outlet] as const\n}\n\n/**\n * This function is a fork of prerenderToStream dynamicIO branch.\n * While it doesn't return a stream we want it to have identical\n * prerender semantics to prerenderToStream and should update it\n * in conjunction with any changes to that function.\n */\nasync function spawnDynamicValidationInDev(\n  resolveValidation: (validatingElement: React.ReactNode) => void,\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  isNotFound: boolean,\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>,\n  requestStore: RequestStore\n): Promise<void> {\n  const {\n    componentMod: ComponentMod,\n    getDynamicParamFromSegment,\n    implicitTags,\n    nonce,\n    renderOpts,\n    workStore,\n  } = ctx\n\n  const { allowEmptyStaticShell = false, botType } = renderOpts\n\n  // These values are placeholder values for this validating render\n  // that are provided during the actual prerenderToStream.\n  const preinitScripts = () => {}\n  const { ServerInsertedHTMLProvider } = createServerInsertedHTML()\n\n  const rootParams = getRootParams(\n    ComponentMod.tree,\n    getDynamicParamFromSegment\n  )\n\n  const hmrRefreshHash = requestStore.cookies.get(\n    NEXT_HMR_REFRESH_HASH_COOKIE\n  )?.value\n\n  // Prerender controller represents the lifetime of the prerender.\n  // It will be aborted when a Task is complete or a synchronously aborting\n  // API is called. Notably during cache-filling renders this does not actually\n  // terminate the render itself which will continue until all caches are filled\n  const initialServerPrerenderController = new AbortController()\n\n  // This controller represents the lifetime of the React render call. Notably\n  // during the cache-filling render it is different from the prerender controller\n  // because we don't want to end the react render until all caches are filled.\n  const initialServerRenderController = new AbortController()\n\n  // The cacheSignal helps us track whether caches are still filling or we are ready\n  // to cut the render off.\n  const cacheSignal = new CacheSignal()\n\n  const captureOwnerStackClient = React.captureOwnerStack\n  const captureOwnerStackServer = ComponentMod.captureOwnerStack\n\n  // The resume data cache here should use a fresh instance as it's\n  // performing a fresh prerender. If we get to implementing the\n  // prerendering of an already prerendered page, we should use the passed\n  // resume data cache instead.\n  const prerenderResumeDataCache = createPrerenderResumeDataCache()\n  const initialServerPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: initialServerRenderController.signal,\n    controller: initialServerPrerenderController,\n    // During the initial prerender we need to track all cache reads to ensure\n    // we render long enough to fill every cache it is possible to visit during\n    // the final prerender.\n    cacheSignal,\n    dynamicTracking: null,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash,\n    captureOwnerStack: captureOwnerStackServer,\n  }\n\n  // We're not going to use the result of this render because the only time it could be used\n  // is if it completes in a microtask and that's likely very rare for any non-trivial app\n  const initialServerPayload = await workUnitAsyncStorage.run(\n    initialServerPrerenderStore,\n    getRSCPayload,\n    tree,\n    ctx,\n    isNotFound\n  )\n\n  const pendingInitialServerResult = workUnitAsyncStorage.run(\n    initialServerPrerenderStore,\n    ComponentMod.prerender,\n    initialServerPayload,\n    clientReferenceManifest.clientModules,\n    {\n      filterStackFrame,\n      onError: (err) => {\n        const digest = getDigestForWellKnownError(err)\n\n        if (digest) {\n          return digest\n        }\n\n        if (isReactLargeShellError(err)) {\n          // TODO: Aggregate\n          console.error(err)\n          return undefined\n        }\n\n        if (initialServerPrerenderController.signal.aborted) {\n          // The render aborted before this error was handled which indicates\n          // the error is caused by unfinished components within the render\n          return\n        } else if (\n          process.env.NEXT_DEBUG_BUILD ||\n          process.env.__NEXT_VERBOSE_LOGGING\n        ) {\n          printDebugThrownValueForProspectiveRender(err, workStore.route)\n        }\n      },\n      // we don't care to track postpones during the prospective render because we need\n      // to always do a final render anyway\n      onPostpone: undefined,\n      // We don't want to stop rendering until the cacheSignal is complete so we pass\n      // a different signal to this render call than is used by dynamic APIs to signify\n      // transitioning out of the prerender environment\n      signal: initialServerRenderController.signal,\n    }\n  )\n\n  // Wait for all caches to be finished filling and for async imports to resolve\n  trackPendingModules(cacheSignal)\n  await cacheSignal.cacheReady()\n\n  initialServerRenderController.abort()\n  initialServerPrerenderController.abort()\n\n  // We don't need to continue the prerender process if we already\n  // detected invalid dynamic usage in the initial prerender phase.\n  if (workStore.invalidDynamicUsageError) {\n    resolveValidation(\n      <LogSafely\n        fn={() => {\n          console.error(workStore.invalidDynamicUsageError)\n        }}\n      />\n    )\n    return\n  }\n\n  let initialServerResult\n  try {\n    initialServerResult = await createReactServerPrerenderResult(\n      pendingInitialServerResult\n    )\n  } catch (err) {\n    if (\n      initialServerRenderController.signal.aborted ||\n      initialServerPrerenderController.signal.aborted\n    ) {\n      // These are expected errors that might error the prerender. we ignore them.\n    } else if (\n      process.env.NEXT_DEBUG_BUILD ||\n      process.env.__NEXT_VERBOSE_LOGGING\n    ) {\n      // We don't normally log these errors because we are going to retry anyway but\n      // it can be useful for debugging Next.js itself to get visibility here when needed\n      printDebugThrownValueForProspectiveRender(err, workStore.route)\n    }\n  }\n\n  if (initialServerResult) {\n    const initialClientRenderController = new AbortController()\n    const initialClientPrerenderController = new AbortController()\n    const initialClientPrerenderStore: PrerenderStore = {\n      type: 'prerender-client',\n      phase: 'render',\n      rootParams,\n      implicitTags,\n      renderSignal: initialClientRenderController.signal,\n      controller: initialClientPrerenderController,\n      // For HTML Generation the only cache tracked activity\n      // is module loading, which has it's own cache signal\n      cacheSignal: null,\n      dynamicTracking: null,\n      allowEmptyStaticShell,\n      revalidate: INFINITE_CACHE,\n      expire: INFINITE_CACHE,\n      stale: INFINITE_CACHE,\n      tags: [...implicitTags.tags],\n      prerenderResumeDataCache,\n      renderResumeDataCache: null,\n      hmrRefreshHash: undefined,\n      captureOwnerStack: captureOwnerStackClient,\n    }\n\n    const prerender = (\n      require('react-dom/static') as typeof import('react-dom/static')\n    ).prerender\n    const pendingInitialClientResult = workUnitAsyncStorage.run(\n      initialClientPrerenderStore,\n      prerender,\n      <App\n        reactServerStream={initialServerResult.asUnclosingStream()}\n        preinitScripts={preinitScripts}\n        clientReferenceManifest={clientReferenceManifest}\n        ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n        gracefullyDegrade={!!botType}\n        nonce={nonce}\n      />,\n      {\n        signal: initialClientRenderController.signal,\n        onError: (err) => {\n          const digest = getDigestForWellKnownError(err)\n\n          if (digest) {\n            return digest\n          }\n\n          if (isReactLargeShellError(err)) {\n            // TODO: Aggregate\n            console.error(err)\n            return undefined\n          }\n\n          if (initialClientRenderController.signal.aborted) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, workStore.route)\n          }\n        },\n        // We don't need bootstrap scripts in this prerender\n        // bootstrapScripts: [bootstrapScript],\n      }\n    )\n\n    pendingInitialClientResult.catch((err) => {\n      if (\n        initialServerRenderController.signal.aborted ||\n        isPrerenderInterruptedError(err)\n      ) {\n        // These are expected errors that might error the prerender. we ignore them.\n      } else if (\n        process.env.NEXT_DEBUG_BUILD ||\n        process.env.__NEXT_VERBOSE_LOGGING\n      ) {\n        // We don't normally log these errors because we are going to retry anyway but\n        // it can be useful for debugging Next.js itself to get visibility here when needed\n        printDebugThrownValueForProspectiveRender(err, workStore.route)\n      }\n    })\n\n    // This is mostly needed for dynamic `import()`s in client components.\n    // Promises passed to client were already awaited above (assuming that they came from cached functions)\n    trackPendingModules(cacheSignal)\n    await cacheSignal.cacheReady()\n    initialClientRenderController.abort()\n  }\n\n  const finalServerController = new AbortController()\n  const serverDynamicTracking = createDynamicTrackingState(\n    false // isDebugDynamicAccesses\n  )\n\n  const finalServerPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: finalServerController.signal,\n    controller: finalServerController,\n    // All caches we could read must already be filled so no tracking is necessary\n    cacheSignal: null,\n    dynamicTracking: serverDynamicTracking,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash,\n    captureOwnerStack: captureOwnerStackServer,\n  }\n\n  const finalAttemptRSCPayload = await workUnitAsyncStorage.run(\n    finalServerPrerenderStore,\n    getRSCPayload,\n    tree,\n    ctx,\n    isNotFound\n  )\n\n  const reactServerResult = await createReactServerPrerenderResult(\n    prerenderAndAbortInSequentialTasks(\n      async () => {\n        const prerenderResult = await workUnitAsyncStorage.run(\n          // The store to scope\n          finalServerPrerenderStore,\n          // The function to run\n          ComponentMod.prerender,\n          // ... the arguments for the function to run\n          finalAttemptRSCPayload,\n          clientReferenceManifest.clientModules,\n          {\n            filterStackFrame,\n            onError: (err: unknown) => {\n              if (\n                finalServerController.signal.aborted &&\n                isPrerenderInterruptedError(err)\n              ) {\n                return err.digest\n              }\n\n              if (isReactLargeShellError(err)) {\n                // TODO: Aggregate\n                console.error(err)\n                return undefined\n              }\n\n              return getDigestForWellKnownError(err)\n            },\n            signal: finalServerController.signal,\n          }\n        )\n        return prerenderResult\n      },\n      () => {\n        finalServerController.abort()\n      }\n    )\n  )\n\n  const clientDynamicTracking = createDynamicTrackingState(\n    false //isDebugDynamicAccesses\n  )\n  const finalClientController = new AbortController()\n  const finalClientPrerenderStore: PrerenderStore = {\n    type: 'prerender-client',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: finalClientController.signal,\n    controller: finalClientController,\n    // No APIs require a cacheSignal through the workUnitStore during the HTML prerender\n    cacheSignal: null,\n    dynamicTracking: clientDynamicTracking,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash,\n    captureOwnerStack: captureOwnerStackClient,\n  }\n\n  let dynamicValidation = createDynamicValidationState()\n\n  try {\n    const prerender = (\n      require('react-dom/static') as typeof import('react-dom/static')\n    ).prerender\n    let { prelude: unprocessedPrelude } =\n      await prerenderAndAbortInSequentialTasks(\n        () =>\n          workUnitAsyncStorage.run(\n            finalClientPrerenderStore,\n            prerender,\n            <App\n              reactServerStream={reactServerResult.asUnclosingStream()}\n              preinitScripts={preinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              gracefullyDegrade={!!botType}\n              nonce={nonce}\n            />,\n            {\n              signal: finalClientController.signal,\n              onError: (err: unknown, errorInfo: ErrorInfo) => {\n                if (\n                  isPrerenderInterruptedError(err) ||\n                  finalClientController.signal.aborted\n                ) {\n                  const componentStack = errorInfo.componentStack\n                  if (typeof componentStack === 'string') {\n                    trackAllowedDynamicAccess(\n                      workStore,\n                      componentStack,\n                      dynamicValidation,\n                      clientDynamicTracking\n                    )\n                  }\n                  return\n                }\n\n                if (isReactLargeShellError(err)) {\n                  // TODO: Aggregate\n                  console.error(err)\n                  return undefined\n                }\n\n                return getDigestForWellKnownError(err)\n              },\n              // We don't need bootstrap scripts in this prerender\n              // bootstrapScripts: [bootstrapScript],\n            }\n          ),\n        () => {\n          finalClientController.abort()\n        }\n      )\n\n    const { preludeIsEmpty } = await processPrelude(unprocessedPrelude)\n    resolveValidation(\n      <LogSafely\n        fn={throwIfDisallowedDynamic.bind(\n          null,\n          workStore,\n          preludeIsEmpty ? PreludeState.Empty : PreludeState.Full,\n          dynamicValidation,\n          serverDynamicTracking\n        )}\n      />\n    )\n  } catch (thrownValue) {\n    // Even if the root errors we still want to report any dynamic IO errors\n    // that were discovered before the root errored.\n\n    let loggingFunction = throwIfDisallowedDynamic.bind(\n      null,\n      workStore,\n      PreludeState.Errored,\n      dynamicValidation,\n      serverDynamicTracking\n    )\n\n    if (process.env.NEXT_DEBUG_BUILD || process.env.__NEXT_VERBOSE_LOGGING) {\n      // We don't normally log these errors because we are going to retry anyway but\n      // it can be useful for debugging Next.js itself to get visibility here when needed\n      const originalLoggingFunction = loggingFunction\n      loggingFunction = () => {\n        console.error(\n          'During dynamic validation the root of the page errored. The next logged error is the thrown value. It may be a duplicate of errors reported during the normal development mode render.'\n        )\n        console.error(thrownValue)\n        originalLoggingFunction()\n      }\n    }\n\n    resolveValidation(<LogSafely fn={loggingFunction} />)\n  }\n}\n\nasync function LogSafely({ fn }: { fn: () => unknown }) {\n  try {\n    await fn()\n  } catch {}\n  return null\n}\n\ntype PrerenderToStreamResult = {\n  stream: ReadableStream<Uint8Array>\n  digestErrorsMap: Map<string, DigestedError>\n  ssrErrors: Array<unknown>\n  dynamicAccess?: null | Array<DynamicAccess>\n  collectedRevalidate: number\n  collectedExpire: number\n  collectedStale: number\n  collectedTags: null | string[]\n  renderResumeDataCache?: RenderResumeDataCache\n}\n\n/**\n * Determines whether we should generate static flight data.\n */\nfunction shouldGenerateStaticFlightData(workStore: WorkStore): boolean {\n  const { isStaticGeneration } = workStore\n  if (!isStaticGeneration) return false\n\n  return true\n}\n\nasync function prerenderToStream(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  ctx: AppRenderContext,\n  metadata: AppPageRenderResultMetadata,\n  tree: LoaderTree\n): Promise<PrerenderToStreamResult> {\n  // When prerendering formState is always null. We still include it\n  // because some shared APIs expect a formState value and this is slightly\n  // more explicit than making it an optional function argument\n  const formState = null\n\n  const {\n    assetPrefix,\n    getDynamicParamFromSegment,\n    implicitTags,\n    nonce,\n    pagePath,\n    renderOpts,\n    workStore,\n  } = ctx\n\n  const {\n    allowEmptyStaticShell = false,\n    basePath,\n    botType,\n    buildManifest,\n    clientReferenceManifest,\n    ComponentMod,\n    crossOrigin,\n    dev = false,\n    experimental,\n    isDebugDynamicAccesses,\n    nextExport = false,\n    onInstrumentationRequestError,\n    page,\n    reactMaxHeadersLength,\n    subresourceIntegrityManifest,\n  } = renderOpts\n\n  assertClientReferenceManifest(clientReferenceManifest)\n\n  const rootParams = getRootParams(tree, getDynamicParamFromSegment)\n  const fallbackRouteParams = workStore.fallbackRouteParams\n\n  const { ServerInsertedHTMLProvider, renderServerInsertedHTML } =\n    createServerInsertedHTML()\n  const getServerInsertedMetadata = createServerInsertedMetadata(nonce)\n\n  const tracingMetadata = getTracedMetadata(\n    getTracer().getTracePropagationData(),\n    experimental.clientTraceMetadata\n  )\n\n  const polyfills: JSX.IntrinsicElements['script'][] =\n    buildManifest.polyfillFiles\n      .filter(\n        (polyfill) =>\n          polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n      )\n      .map((polyfill) => ({\n        src: `${assetPrefix}/_next/${polyfill}${getAssetQueryString(\n          ctx,\n          false\n        )}`,\n        integrity: subresourceIntegrityManifest?.[polyfill],\n        crossOrigin,\n        noModule: true,\n        nonce,\n      }))\n\n  const [preinitScripts, bootstrapScript] = getRequiredScripts(\n    buildManifest,\n    // Why is assetPrefix optional on renderOpts?\n    // @TODO make it default empty string on renderOpts and get rid of it from ctx\n    assetPrefix,\n    crossOrigin,\n    subresourceIntegrityManifest,\n    getAssetQueryString(ctx, true),\n    nonce,\n    page\n  )\n\n  const reactServerErrorsByDigest: Map<string, DigestedError> = new Map()\n  // We don't report errors during prerendering through our instrumentation hooks\n  const silenceLogger = !!experimental.isRoutePPREnabled\n  function onHTMLRenderRSCError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components')\n    )\n  }\n  const serverComponentsErrorHandler = createHTMLReactServerErrorHandler(\n    dev,\n    nextExport,\n    reactServerErrorsByDigest,\n    silenceLogger,\n    onHTMLRenderRSCError\n  )\n\n  function onHTMLRenderSSRError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'server-rendering')\n    )\n  }\n  const allCapturedErrors: Array<unknown> = []\n  const htmlRendererErrorHandler = createHTMLErrorHandler(\n    dev,\n    nextExport,\n    reactServerErrorsByDigest,\n    allCapturedErrors,\n    silenceLogger,\n    onHTMLRenderSSRError\n  )\n\n  let reactServerPrerenderResult:\n    | null\n    | ReactServerPrerenderResult\n    | ServerPrerenderStreamResult = null\n  const setMetadataHeader = (name: string) => {\n    metadata.headers ??= {}\n    metadata.headers[name] = res.getHeader(name)\n  }\n  const setHeader = (name: string, value: string | string[]) => {\n    res.setHeader(name, value)\n    setMetadataHeader(name)\n    return res\n  }\n  const appendHeader = (name: string, value: string | string[]) => {\n    if (Array.isArray(value)) {\n      value.forEach((item) => {\n        res.appendHeader(name, item)\n      })\n    } else {\n      res.appendHeader(name, value)\n    }\n    setMetadataHeader(name)\n  }\n\n  const selectStaleTime = (stale: number) =>\n    stale === INFINITE_CACHE &&\n    typeof experimental.staleTimes?.static === 'number'\n      ? experimental.staleTimes.static\n      : stale\n\n  let prerenderStore: PrerenderStore | null = null\n\n  try {\n    if (experimental.dynamicIO) {\n      /**\n       * dynamicIO with PPR\n       *\n       * The general approach is to render the RSC stream first allowing any cache reads to resolve.\n       * Once we have settled all cache reads we restart the render and abort after a single Task.\n       *\n       * Unlike with the non PPR case we can't synchronously abort the render when a dynamic API is used\n       * during the initial render because we need to ensure all caches can be filled as part of the initial Task\n       * and a synchronous abort might prevent us from filling all caches.\n       *\n       * Once the render is complete we allow the SSR render to finish and use a combination of the postponed state\n       * and the reactServerIsDynamic value to determine how to treat the resulting render\n       */\n\n      // Prerender controller represents the lifetime of the prerender.\n      // It will be aborted when a Task is complete or a synchronously aborting\n      // API is called. Notably during cache-filling renders this does not actually\n      // terminate the render itself which will continue until all caches are filled\n      const initialServerPrerenderController = new AbortController()\n\n      // This controller represents the lifetime of the React render call. Notably\n      // during the cache-filling render it is different from the prerender controller\n      // because we don't want to end the react render until all caches are filled.\n      const initialServerRenderController = new AbortController()\n\n      // The cacheSignal helps us track whether caches are still filling or we are ready\n      // to cut the render off.\n      const cacheSignal = new CacheSignal()\n\n      let resumeDataCache: RenderResumeDataCache | PrerenderResumeDataCache\n      let renderResumeDataCache: RenderResumeDataCache | null = null\n      let prerenderResumeDataCache: PrerenderResumeDataCache | null = null\n\n      if (renderOpts.renderResumeDataCache) {\n        // If a prefilled immutable render resume data cache is provided, e.g.\n        // when prerendering an optional fallback shell after having prerendered\n        // pages with defined params, we use this instead of a prerender resume\n        // data cache.\n        resumeDataCache = renderResumeDataCache =\n          renderOpts.renderResumeDataCache\n      } else {\n        // Otherwise we create a new mutable prerender resume data cache.\n        resumeDataCache = prerenderResumeDataCache =\n          createPrerenderResumeDataCache()\n      }\n\n      const initialServerPrerenderStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender',\n        phase: 'render',\n        rootParams,\n        implicitTags,\n        renderSignal: initialServerRenderController.signal,\n        controller: initialServerPrerenderController,\n        // During the initial prerender we need to track all cache reads to ensure\n        // we render long enough to fill every cache it is possible to visit during\n        // the final prerender.\n        cacheSignal,\n        dynamicTracking: null,\n        allowEmptyStaticShell,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n        renderResumeDataCache,\n        hmrRefreshHash: undefined,\n        captureOwnerStack: undefined, // Not available in production.\n      })\n\n      // We're not going to use the result of this render because the only time it could be used\n      // is if it completes in a microtask and that's likely very rare for any non-trivial app\n      const initialServerPayload = await workUnitAsyncStorage.run(\n        initialServerPrerenderStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n\n      const pendingInitialServerResult = workUnitAsyncStorage.run(\n        initialServerPrerenderStore,\n        ComponentMod.prerender,\n        initialServerPayload,\n        clientReferenceManifest.clientModules,\n        {\n          filterStackFrame,\n          onError: (err) => {\n            const digest = getDigestForWellKnownError(err)\n\n            if (digest) {\n              return digest\n            }\n\n            if (isReactLargeShellError(err)) {\n              // TODO: Aggregate\n              console.error(err)\n              return undefined\n            }\n\n            if (initialServerPrerenderController.signal.aborted) {\n              // The render aborted before this error was handled which indicates\n              // the error is caused by unfinished components within the render\n              return\n            } else if (\n              process.env.NEXT_DEBUG_BUILD ||\n              process.env.__NEXT_VERBOSE_LOGGING\n            ) {\n              printDebugThrownValueForProspectiveRender(err, workStore.route)\n            }\n          },\n          // we don't care to track postpones during the prospective render because we need\n          // to always do a final render anyway\n          onPostpone: undefined,\n          // We don't want to stop rendering until the cacheSignal is complete so we pass\n          // a different signal to this render call than is used by dynamic APIs to signify\n          // transitioning out of the prerender environment\n          signal: initialServerRenderController.signal,\n        }\n      )\n\n      // Wait for all caches to be finished filling and for async imports to resolve\n      trackPendingModules(cacheSignal)\n      await cacheSignal.cacheReady()\n\n      initialServerRenderController.abort()\n      initialServerPrerenderController.abort()\n\n      // We don't need to continue the prerender process if we already\n      // detected invalid dynamic usage in the initial prerender phase.\n      if (workStore.invalidDynamicUsageError) {\n        throw workStore.invalidDynamicUsageError\n      }\n\n      let initialServerResult\n      try {\n        initialServerResult = await createReactServerPrerenderResult(\n          pendingInitialServerResult\n        )\n      } catch (err) {\n        if (\n          initialServerRenderController.signal.aborted ||\n          initialServerPrerenderController.signal.aborted\n        ) {\n          // These are expected errors that might error the prerender. we ignore them.\n        } else if (\n          process.env.NEXT_DEBUG_BUILD ||\n          process.env.__NEXT_VERBOSE_LOGGING\n        ) {\n          // We don't normally log these errors because we are going to retry anyway but\n          // it can be useful for debugging Next.js itself to get visibility here when needed\n          printDebugThrownValueForProspectiveRender(err, workStore.route)\n        }\n      }\n\n      if (initialServerResult) {\n        const initialClientRenderController = new AbortController()\n        const initialClientPrerenderController = new AbortController()\n        const initialClientPrerenderStore: PrerenderStore = {\n          type: 'prerender-client',\n          phase: 'render',\n          rootParams,\n          implicitTags,\n          renderSignal: initialClientRenderController.signal,\n          controller: initialClientPrerenderController,\n          // For HTML Generation the only cache tracked activity\n          // is module loading, which has it's own cache signal\n          cacheSignal: null,\n          dynamicTracking: null,\n          allowEmptyStaticShell,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags.tags],\n          prerenderResumeDataCache,\n          renderResumeDataCache,\n          hmrRefreshHash: undefined,\n          captureOwnerStack: undefined, // Not available in production.\n        }\n\n        const prerender = (\n          require('react-dom/static') as typeof import('react-dom/static')\n        ).prerender\n        const pendingInitialClientResult = workUnitAsyncStorage.run(\n          initialClientPrerenderStore,\n          prerender,\n          <App\n            reactServerStream={initialServerResult.asUnclosingStream()}\n            preinitScripts={preinitScripts}\n            clientReferenceManifest={clientReferenceManifest}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            gracefullyDegrade={!!botType}\n            nonce={nonce}\n          />,\n          {\n            signal: initialClientRenderController.signal,\n            onError: (err) => {\n              const digest = getDigestForWellKnownError(err)\n\n              if (digest) {\n                return digest\n              }\n\n              if (isReactLargeShellError(err)) {\n                // TODO: Aggregate\n                console.error(err)\n                return undefined\n              }\n\n              if (initialClientRenderController.signal.aborted) {\n                // These are expected errors that might error the prerender. we ignore them.\n              } else if (\n                process.env.NEXT_DEBUG_BUILD ||\n                process.env.__NEXT_VERBOSE_LOGGING\n              ) {\n                // We don't normally log these errors because we are going to retry anyway but\n                // it can be useful for debugging Next.js itself to get visibility here when needed\n                printDebugThrownValueForProspectiveRender(err, workStore.route)\n              }\n            },\n            bootstrapScripts: [bootstrapScript],\n          }\n        )\n\n        pendingInitialClientResult.catch((err) => {\n          if (\n            initialServerRenderController.signal.aborted ||\n            isPrerenderInterruptedError(err)\n          ) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, workStore.route)\n          }\n        })\n\n        // This is mostly needed for dynamic `import()`s in client components.\n        // Promises passed to client were already awaited above (assuming that they came from cached functions)\n        trackPendingModules(cacheSignal)\n        await cacheSignal.cacheReady()\n        initialClientRenderController.abort()\n      }\n\n      let serverIsDynamic = false\n      const finalServerController = new AbortController()\n      const serverDynamicTracking = createDynamicTrackingState(\n        isDebugDynamicAccesses\n      )\n\n      const finalServerPrerenderStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender',\n        phase: 'render',\n        rootParams,\n        implicitTags,\n        renderSignal: finalServerController.signal,\n        controller: finalServerController,\n        // All caches we could read must already be filled so no tracking is necessary\n        cacheSignal: null,\n        dynamicTracking: serverDynamicTracking,\n        allowEmptyStaticShell,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n        renderResumeDataCache,\n        hmrRefreshHash: undefined,\n        captureOwnerStack: undefined, // Not available in production.\n      })\n\n      const finalAttemptRSCPayload = await workUnitAsyncStorage.run(\n        finalServerPrerenderStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      let prerenderIsPending = true\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResult(\n          prerenderAndAbortInSequentialTasks(\n            async () => {\n              const prerenderResult = await workUnitAsyncStorage.run(\n                // The store to scope\n                finalServerPrerenderStore,\n                // The function to run\n                ComponentMod.prerender,\n                // ... the arguments for the function to run\n                finalAttemptRSCPayload,\n                clientReferenceManifest.clientModules,\n                {\n                  filterStackFrame,\n                  onError: (err: unknown) => {\n                    return serverComponentsErrorHandler(err)\n                  },\n                  signal: finalServerController.signal,\n                }\n              )\n              prerenderIsPending = false\n              return prerenderResult\n            },\n            () => {\n              if (finalServerController.signal.aborted) {\n                // If the server controller is already aborted we must have called something\n                // that required aborting the prerender synchronously such as with new Date()\n                serverIsDynamic = true\n                return\n              }\n\n              if (prerenderIsPending) {\n                // If prerenderIsPending then we have blocked for longer than a Task and we assume\n                // there is something unfinished.\n                serverIsDynamic = true\n              }\n              finalServerController.abort()\n            }\n          )\n        ))\n\n      const clientDynamicTracking = createDynamicTrackingState(\n        isDebugDynamicAccesses\n      )\n      const finalClientController = new AbortController()\n      const finalClientPrerenderStore: PrerenderStore = {\n        type: 'prerender-client',\n        phase: 'render',\n        rootParams,\n        implicitTags,\n        renderSignal: finalClientController.signal,\n        controller: finalClientController,\n        // No APIs require a cacheSignal through the workUnitStore during the HTML prerender\n        cacheSignal: null,\n        dynamicTracking: clientDynamicTracking,\n        allowEmptyStaticShell,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n        renderResumeDataCache,\n        hmrRefreshHash: undefined,\n        captureOwnerStack: undefined, // Not available in production.\n      }\n\n      let clientIsDynamic = false\n      let dynamicValidation = createDynamicValidationState()\n\n      const prerender = (\n        require('react-dom/static') as typeof import('react-dom/static')\n      ).prerender\n      let { prelude: unprocessedPrelude, postponed } =\n        await prerenderAndAbortInSequentialTasks(\n          () =>\n            workUnitAsyncStorage.run(\n              finalClientPrerenderStore,\n              prerender,\n              <App\n                reactServerStream={reactServerResult.asUnclosingStream()}\n                preinitScripts={preinitScripts}\n                clientReferenceManifest={clientReferenceManifest}\n                ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                gracefullyDegrade={!!botType}\n                nonce={nonce}\n              />,\n              {\n                signal: finalClientController.signal,\n                onError: (err: unknown, errorInfo: ErrorInfo) => {\n                  if (\n                    isPrerenderInterruptedError(err) ||\n                    finalClientController.signal.aborted\n                  ) {\n                    clientIsDynamic = true\n\n                    const componentStack: string | undefined = (\n                      errorInfo as any\n                    ).componentStack\n                    if (typeof componentStack === 'string') {\n                      trackAllowedDynamicAccess(\n                        workStore,\n                        componentStack,\n                        dynamicValidation,\n                        clientDynamicTracking\n                      )\n                    }\n                    return\n                  }\n\n                  return htmlRendererErrorHandler(err, errorInfo)\n                },\n                onHeaders: (headers: Headers) => {\n                  headers.forEach((value, key) => {\n                    appendHeader(key, value)\n                  })\n                },\n                maxHeadersLength: reactMaxHeadersLength,\n                bootstrapScripts: [bootstrapScript],\n              }\n            ),\n          () => {\n            finalClientController.abort()\n          }\n        )\n\n      const { prelude, preludeIsEmpty } =\n        await processPrelude(unprocessedPrelude)\n\n      // If we've disabled throwing on empty static shell, then we don't need to\n      // track any dynamic access that occurs above the suspense boundary because\n      // we'll do so in the route shell.\n      if (!allowEmptyStaticShell) {\n        throwIfDisallowedDynamic(\n          workStore,\n          preludeIsEmpty ? PreludeState.Empty : PreludeState.Full,\n          dynamicValidation,\n          serverDynamicTracking\n        )\n      }\n\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath,\n        tracingMetadata: tracingMetadata,\n      })\n\n      const flightData = await streamToBuffer(reactServerResult.asStream())\n      metadata.flightData = flightData\n      metadata.segmentData = await collectSegmentData(\n        flightData,\n        finalServerPrerenderStore,\n        ComponentMod,\n        renderOpts,\n        fallbackRouteParams\n      )\n\n      if (serverIsDynamic || clientIsDynamic) {\n        if (postponed != null) {\n          // Dynamic HTML case\n          metadata.postponed = await getDynamicHTMLPostponedState(\n            postponed,\n            fallbackRouteParams,\n            resumeDataCache\n          )\n        } else {\n          // Dynamic Data case\n          metadata.postponed =\n            await getDynamicDataPostponedState(resumeDataCache)\n        }\n        reactServerResult.consume()\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: consumeDynamicAccess(\n            serverDynamicTracking,\n            clientDynamicTracking\n          ),\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: finalServerPrerenderStore.revalidate,\n          collectedExpire: finalServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(finalServerPrerenderStore.stale),\n          collectedTags: finalServerPrerenderStore.tags,\n          renderResumeDataCache: createRenderResumeDataCache(resumeDataCache),\n        }\n      } else {\n        // Static case\n        if (workStore.forceDynamic) {\n          throw new StaticGenBailoutError(\n            'Invariant: a Page with `dynamic = \"force-dynamic\"` did not trigger the dynamic pathway. This is a bug in Next.js'\n          )\n        }\n\n        let htmlStream = prelude\n        if (postponed != null) {\n          // We postponed but nothing dynamic was used. We resume the render now and immediately abort it\n          // so we can set all the postponed boundaries to client render mode before we store the HTML response\n          const resume = (\n            require('react-dom/server') as typeof import('react-dom/server')\n          ).resume\n\n          // We don't actually want to render anything so we just pass a stream\n          // that never resolves. The resume call is going to abort immediately anyway\n          const foreverStream = new ReadableStream<Uint8Array>()\n\n          const resumeStream = await resume(\n            <App\n              reactServerStream={foreverStream}\n              preinitScripts={() => {}}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              gracefullyDegrade={!!botType}\n              nonce={nonce}\n            />,\n            JSON.parse(JSON.stringify(postponed)),\n            {\n              signal: createPostponedAbortSignal('static prerender resume'),\n              onError: htmlRendererErrorHandler,\n              nonce,\n            }\n          )\n\n          // First we write everything from the prerender, then we write everything from the aborted resume render\n          htmlStream = chainStreams(prelude, resumeStream)\n        }\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueStaticPrerender(htmlStream, {\n            inlinedDataStream: createInlinedDataReadableStream(\n              reactServerResult.consumeAsStream(),\n              nonce,\n              formState\n            ),\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n            isBuildTimePrerendering:\n              ctx.workStore.isBuildTimePrerendering === true,\n            buildId: ctx.workStore.buildId,\n          }),\n          dynamicAccess: consumeDynamicAccess(\n            serverDynamicTracking,\n            clientDynamicTracking\n          ),\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: finalServerPrerenderStore.revalidate,\n          collectedExpire: finalServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(finalServerPrerenderStore.stale),\n          collectedTags: finalServerPrerenderStore.tags,\n          renderResumeDataCache: createRenderResumeDataCache(resumeDataCache),\n        }\n      }\n    } else if (experimental.isRoutePPREnabled) {\n      // We're statically generating with PPR and need to do dynamic tracking\n      let dynamicTracking = createDynamicTrackingState(isDebugDynamicAccesses)\n\n      const prerenderResumeDataCache = createPrerenderResumeDataCache()\n      const reactServerPrerenderStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender-ppr',\n        phase: 'render',\n        rootParams,\n        implicitTags,\n        dynamicTracking,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n      })\n      const RSCPayload = await workUnitAsyncStorage.run(\n        reactServerPrerenderStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResultFromRender(\n          workUnitAsyncStorage.run(\n            reactServerPrerenderStore,\n            ComponentMod.renderToReadableStream,\n            // ... the arguments for the function to run\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              filterStackFrame,\n              onError: serverComponentsErrorHandler,\n            }\n          )\n        ))\n\n      const ssrPrerenderStore: PrerenderStore = {\n        type: 'prerender-ppr',\n        phase: 'render',\n        rootParams,\n        implicitTags,\n        dynamicTracking,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n      }\n      const prerender = (\n        require('react-dom/static') as typeof import('react-dom/static')\n      ).prerender\n      const { prelude, postponed } = await workUnitAsyncStorage.run(\n        ssrPrerenderStore,\n        prerender,\n        <App\n          reactServerStream={reactServerResult.asUnclosingStream()}\n          preinitScripts={preinitScripts}\n          clientReferenceManifest={clientReferenceManifest}\n          ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n          gracefullyDegrade={!!botType}\n          nonce={nonce}\n        />,\n        {\n          onError: htmlRendererErrorHandler,\n          onHeaders: (headers: Headers) => {\n            headers.forEach((value, key) => {\n              appendHeader(key, value)\n            })\n          },\n          maxHeadersLength: reactMaxHeadersLength,\n          bootstrapScripts: [bootstrapScript],\n        }\n      )\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath,\n        tracingMetadata: tracingMetadata,\n      })\n\n      // After awaiting here we've waited for the entire RSC render to complete. Crucially this means\n      // that when we detect whether we've used dynamic APIs below we know we'll have picked up even\n      // parts of the React Server render that might not be used in the SSR render.\n      const flightData = await streamToBuffer(reactServerResult.asStream())\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          ssrPrerenderStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n      }\n\n      /**\n       * When prerendering there are three outcomes to consider\n       *\n       *   Dynamic HTML:      The prerender has dynamic holes (caused by using Next.js Dynamic Rendering APIs)\n       *                      We will need to resume this result when requests are handled and we don't include\n       *                      any server inserted HTML or inlined flight data in the static HTML\n       *\n       *   Dynamic Data:      The prerender has no dynamic holes but dynamic APIs were used. We will not\n       *                      resume this render when requests are handled but we will generate new inlined\n       *                      flight data since it is dynamic and differences may end up reconciling on the client\n       *\n       *   Static:            The prerender has no dynamic holes and no dynamic APIs were used. We statically encode\n       *                      all server inserted HTML and flight data\n       */\n      // First we check if we have any dynamic holes in our HTML prerender\n      if (accessedDynamicData(dynamicTracking.dynamicAccesses)) {\n        if (postponed != null) {\n          // Dynamic HTML case.\n          metadata.postponed = await getDynamicHTMLPostponedState(\n            postponed,\n            fallbackRouteParams,\n            prerenderResumeDataCache\n          )\n        } else {\n          // Dynamic Data case.\n          metadata.postponed = await getDynamicDataPostponedState(\n            prerenderResumeDataCache\n          )\n        }\n        // Regardless of whether this is the Dynamic HTML or Dynamic Data case we need to ensure we include\n        // server inserted html in the static response because the html that is part of the prerender may depend on it\n        // It is possible in the set of stream transforms for Dynamic HTML vs Dynamic Data may differ but currently both states\n        // require the same set so we unify the code path here\n        reactServerResult.consume()\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(reactServerPrerenderStore.stale),\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      } else if (fallbackRouteParams && fallbackRouteParams.size > 0) {\n        // Rendering the fallback case.\n        metadata.postponed = await getDynamicDataPostponedState(\n          prerenderResumeDataCache\n        )\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(reactServerPrerenderStore.stale),\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      } else {\n        // Static case\n        // We still have not used any dynamic APIs. At this point we can produce an entirely static prerender response\n        if (workStore.forceDynamic) {\n          throw new StaticGenBailoutError(\n            'Invariant: a Page with `dynamic = \"force-dynamic\"` did not trigger the dynamic pathway. This is a bug in Next.js'\n          )\n        }\n\n        let htmlStream = prelude\n        if (postponed != null) {\n          // We postponed but nothing dynamic was used. We resume the render now and immediately abort it\n          // so we can set all the postponed boundaries to client render mode before we store the HTML response\n          const resume = (\n            require('react-dom/server') as typeof import('react-dom/server')\n          ).resume\n\n          // We don't actually want to render anything so we just pass a stream\n          // that never resolves. The resume call is going to abort immediately anyway\n          const foreverStream = new ReadableStream<Uint8Array>()\n\n          const resumeStream = await resume(\n            <App\n              reactServerStream={foreverStream}\n              preinitScripts={() => {}}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              gracefullyDegrade={!!botType}\n              nonce={nonce}\n            />,\n            JSON.parse(JSON.stringify(postponed)),\n            {\n              signal: createPostponedAbortSignal('static prerender resume'),\n              onError: htmlRendererErrorHandler,\n              nonce,\n            }\n          )\n\n          // First we write everything from the prerender, then we write everything from the aborted resume render\n          htmlStream = chainStreams(prelude, resumeStream)\n        }\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueStaticPrerender(htmlStream, {\n            inlinedDataStream: createInlinedDataReadableStream(\n              reactServerResult.consumeAsStream(),\n              nonce,\n              formState\n            ),\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n            isBuildTimePrerendering:\n              ctx.workStore.isBuildTimePrerendering === true,\n            buildId: ctx.workStore.buildId,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(reactServerPrerenderStore.stale),\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      }\n    } else {\n      const prerenderLegacyStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender-legacy',\n        phase: 'render',\n        rootParams,\n        implicitTags,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n      })\n      // This is a regular static generation. We don't do dynamic tracking because we rely on\n      // the old-school dynamic error handling to bail out of static generation\n      const RSCPayload = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResultFromRender(\n          workUnitAsyncStorage.run(\n            prerenderLegacyStore,\n            ComponentMod.renderToReadableStream,\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              filterStackFrame,\n              onError: serverComponentsErrorHandler,\n            }\n          )\n        ))\n\n      const renderToReadableStream = (\n        require('react-dom/server') as typeof import('react-dom/server')\n      ).renderToReadableStream\n      const htmlStream = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        renderToReadableStream,\n        <App\n          reactServerStream={reactServerResult.asUnclosingStream()}\n          preinitScripts={preinitScripts}\n          clientReferenceManifest={clientReferenceManifest}\n          ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n          gracefullyDegrade={!!botType}\n          nonce={nonce}\n        />,\n        {\n          onError: htmlRendererErrorHandler,\n          nonce,\n          bootstrapScripts: [bootstrapScript],\n        }\n      )\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        const flightData = await streamToBuffer(reactServerResult.asStream())\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          prerenderLegacyStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n      }\n\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath,\n        tracingMetadata: tracingMetadata,\n      })\n      return {\n        digestErrorsMap: reactServerErrorsByDigest,\n        ssrErrors: allCapturedErrors,\n        stream: await continueFizzStream(htmlStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            reactServerResult.consumeAsStream(),\n            nonce,\n            formState\n          ),\n          isStaticGeneration: true,\n          isBuildTimePrerendering:\n            ctx.workStore.isBuildTimePrerendering === true,\n          buildId: ctx.workStore.buildId,\n          getServerInsertedHTML,\n          getServerInsertedMetadata,\n        }),\n        // TODO: Should this include the SSR pass?\n        collectedRevalidate: prerenderLegacyStore.revalidate,\n        collectedExpire: prerenderLegacyStore.expire,\n        collectedStale: selectStaleTime(prerenderLegacyStore.stale),\n        collectedTags: prerenderLegacyStore.tags,\n      }\n    }\n  } catch (err) {\n    if (\n      isStaticGenBailoutError(err) ||\n      (typeof err === 'object' &&\n        err !== null &&\n        'message' in err &&\n        typeof err.message === 'string' &&\n        err.message.includes(\n          'https://nextjs.org/docs/advanced-features/static-html-export'\n        ))\n    ) {\n      // Ensure that \"next dev\" prints the red error overlay\n      throw err\n    }\n\n    // If this is a static generation error, we need to throw it so that it\n    // can be handled by the caller if we're in static generation mode.\n    if (isDynamicServerError(err)) {\n      throw err\n    }\n\n    // If a bailout made it to this point, it means it wasn't wrapped inside\n    // a suspense boundary.\n    const shouldBailoutToCSR = isBailoutToCSRError(err)\n    if (shouldBailoutToCSR) {\n      const stack = getStackWithoutErrorMessage(err)\n      error(\n        `${err.reason} should be wrapped in a suspense boundary at page \"${pagePath}\". Read more: https://nextjs.org/docs/messages/missing-suspense-with-csr-bailout\\n${stack}`\n      )\n\n      throw err\n    }\n\n    // If we errored when we did not have an RSC stream to read from. This is\n    // not just a render error, we need to throw early.\n    if (reactServerPrerenderResult === null) {\n      throw err\n    }\n\n    let errorType: MetadataErrorType | 'redirect' | undefined\n\n    if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n      metadata.statusCode = res.statusCode\n      errorType = getAccessFallbackErrorTypeByStatus(res.statusCode)\n    } else if (isRedirectError(err)) {\n      errorType = 'redirect'\n      res.statusCode = getRedirectStatusCodeFromError(err)\n      metadata.statusCode = res.statusCode\n\n      const redirectUrl = addPathPrefix(getURLFromRedirectError(err), basePath)\n\n      setHeader('location', redirectUrl)\n    } else if (!shouldBailoutToCSR) {\n      res.statusCode = 500\n      metadata.statusCode = res.statusCode\n    }\n\n    const [errorPreinitScripts, errorBootstrapScript] = getRequiredScripts(\n      buildManifest,\n      assetPrefix,\n      crossOrigin,\n      subresourceIntegrityManifest,\n      getAssetQueryString(ctx, false),\n      nonce,\n      '/_not-found/page'\n    )\n\n    const prerenderLegacyStore: PrerenderStore = (prerenderStore = {\n      type: 'prerender-legacy',\n      phase: 'render',\n      rootParams,\n      implicitTags: implicitTags,\n      revalidate:\n        typeof prerenderStore?.revalidate !== 'undefined'\n          ? prerenderStore.revalidate\n          : INFINITE_CACHE,\n      expire:\n        typeof prerenderStore?.expire !== 'undefined'\n          ? prerenderStore.expire\n          : INFINITE_CACHE,\n      stale:\n        typeof prerenderStore?.stale !== 'undefined'\n          ? prerenderStore.stale\n          : INFINITE_CACHE,\n      tags: [...(prerenderStore?.tags || implicitTags.tags)],\n    })\n    const errorRSCPayload = await workUnitAsyncStorage.run(\n      prerenderLegacyStore,\n      getErrorRSCPayload,\n      tree,\n      ctx,\n      reactServerErrorsByDigest.has((err as any).digest) ? undefined : err,\n      errorType\n    )\n\n    const errorServerStream = workUnitAsyncStorage.run(\n      prerenderLegacyStore,\n      ComponentMod.renderToReadableStream,\n      errorRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        filterStackFrame,\n        onError: serverComponentsErrorHandler,\n      }\n    )\n\n    try {\n      // TODO we should use the same prerender semantics that we initially rendered\n      // with in this case too. The only reason why this is ok atm is because it's essentially\n      // an empty page and no user code runs.\n      const fizzStream = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        renderToInitialFizzStream,\n        {\n          ReactDOMServer:\n            require('react-dom/server') as typeof import('react-dom/server'),\n          element: (\n            <ErrorApp\n              reactServerStream={errorServerStream}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              preinitScripts={errorPreinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              gracefullyDegrade={!!botType}\n              nonce={nonce}\n            />\n          ),\n          streamOptions: {\n            nonce,\n            // Include hydration scripts in the HTML\n            bootstrapScripts: [errorBootstrapScript],\n            formState,\n          },\n        }\n      )\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        const flightData = await streamToBuffer(\n          reactServerPrerenderResult.asStream()\n        )\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          prerenderLegacyStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n      }\n\n      // This is intentionally using the readable datastream from the main\n      // render rather than the flight data from the error page render\n      const flightStream =\n        reactServerPrerenderResult instanceof ServerPrerenderStreamResult\n          ? reactServerPrerenderResult.asStream()\n          : reactServerPrerenderResult.consumeAsStream()\n\n      return {\n        // Returning the error that was thrown so it can be used to handle\n        // the response in the caller.\n        digestErrorsMap: reactServerErrorsByDigest,\n        ssrErrors: allCapturedErrors,\n        stream: await continueFizzStream(fizzStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            flightStream,\n            nonce,\n            formState\n          ),\n          isStaticGeneration: true,\n          isBuildTimePrerendering:\n            ctx.workStore.isBuildTimePrerendering === true,\n          buildId: ctx.workStore.buildId,\n          getServerInsertedHTML: makeGetServerInsertedHTML({\n            polyfills,\n            renderServerInsertedHTML,\n            serverCapturedErrors: [],\n            basePath,\n            tracingMetadata: tracingMetadata,\n          }),\n          getServerInsertedMetadata,\n          validateRootLayout: dev,\n        }),\n        dynamicAccess: null,\n        collectedRevalidate:\n          prerenderStore !== null ? prerenderStore.revalidate : INFINITE_CACHE,\n        collectedExpire:\n          prerenderStore !== null ? prerenderStore.expire : INFINITE_CACHE,\n        collectedStale: selectStaleTime(\n          prerenderStore !== null ? prerenderStore.stale : INFINITE_CACHE\n        ),\n        collectedTags: prerenderStore !== null ? prerenderStore.tags : null,\n      }\n    } catch (finalErr: any) {\n      if (\n        process.env.NODE_ENV === 'development' &&\n        isHTTPAccessFallbackError(finalErr)\n      ) {\n        const { bailOnRootNotFound } =\n          require('../../client/components/dev-root-http-access-fallback-boundary') as typeof import('../../client/components/dev-root-http-access-fallback-boundary')\n        bailOnRootNotFound()\n      }\n      throw finalErr\n    }\n  }\n}\n\nconst getGlobalErrorStyles = async (\n  tree: LoaderTree,\n  ctx: AppRenderContext\n): Promise<{\n  GlobalError: GlobalErrorComponent\n  styles: React.ReactNode | undefined\n}> => {\n  const {\n    modules: { 'global-error': globalErrorModule },\n  } = parseLoaderTree(tree)\n\n  const GlobalErrorComponent: GlobalErrorComponent =\n    ctx.componentMod.GlobalError\n  let globalErrorStyles\n  if (globalErrorModule) {\n    const [, styles] = await createComponentStylesAndScripts({\n      ctx,\n      filePath: globalErrorModule[1],\n      getComponent: globalErrorModule[0],\n      injectedCSS: new Set(),\n      injectedJS: new Set(),\n    })\n    globalErrorStyles = styles\n  }\n  if (ctx.renderOpts.dev) {\n    const dir =\n      process.env.NEXT_RUNTIME === 'edge'\n        ? process.env.__NEXT_EDGE_PROJECT_DIR!\n        : ctx.renderOpts.dir || ''\n\n    const globalErrorModulePath = normalizeConventionFilePath(\n      dir,\n      globalErrorModule?.[1]\n    )\n    if (ctx.renderOpts.devtoolSegmentExplorer && globalErrorModulePath) {\n      const SegmentViewNode = ctx.componentMod.SegmentViewNode\n      globalErrorStyles = (\n        // This will be rendered next to GlobalError component under ErrorBoundary,\n        // it requires a key to avoid React warning about duplicate keys.\n        <SegmentViewNode\n          key=\"ge-svn\"\n          type=\"global-error\"\n          pagePath={globalErrorModulePath}\n        >\n          {globalErrorStyles}\n        </SegmentViewNode>\n      )\n    }\n  }\n\n  return {\n    GlobalError: GlobalErrorComponent,\n    styles: globalErrorStyles,\n  }\n}\n\nasync function collectSegmentData(\n  fullPageDataBuffer: Buffer,\n  prerenderStore: PrerenderStore,\n  ComponentMod: AppPageModule,\n  renderOpts: RenderOpts,\n  fallbackRouteParams: FallbackRouteParams | null\n): Promise<Map<string, Buffer> | undefined> {\n  // Per-segment prefetch data\n  //\n  // All of the segments for a page are generated simultaneously, including\n  // during revalidations. This is to ensure consistency, because it's\n  // possible for a mismatch between a layout and page segment can cause the\n  // client to error during rendering. We want to preserve the ability of the\n  // client to recover from such a mismatch by re-requesting all the segments\n  // to get a consistent view of the page.\n  //\n  // For performance, we reuse the Flight output that was created when\n  // generating the initial page HTML. The Flight stream for the whole page is\n  // decomposed into a separate stream per segment.\n\n  const clientReferenceManifest = renderOpts.clientReferenceManifest\n  if (\n    !clientReferenceManifest ||\n    // Do not generate per-segment data unless the experimental Segment Cache\n    // flag is enabled.\n    //\n    // We also skip generating segment data if flag is set to \"client-only\",\n    // rather than true. (The \"client-only\" option only affects the behavior of\n    // the client-side implementation; per-segment prefetches are intentionally\n    // disabled in that configuration).\n    renderOpts.experimental.clientSegmentCache !== true\n  ) {\n    return\n  }\n\n  // Manifest passed to the Flight client for reading the full-page Flight\n  // stream. Based off similar code in use-cache-wrapper.ts.\n  const isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n  const serverConsumerManifest = {\n    // moduleLoading must be null because we don't want to trigger preloads of ClientReferences\n    // to be added to the consumer. Instead, we'll wait for any ClientReference to be emitted\n    // which themselves will handle the preloading.\n    moduleLoading: null,\n    moduleMap: isEdgeRuntime\n      ? clientReferenceManifest.edgeRscModuleMapping\n      : clientReferenceManifest.rscModuleMapping,\n    serverModuleMap: getServerModuleMap(),\n  }\n\n  const staleTime = prerenderStore.stale\n  return await ComponentMod.collectSegmentData(\n    fullPageDataBuffer,\n    staleTime,\n    clientReferenceManifest.clientModules as ManifestNode,\n    serverConsumerManifest,\n    fallbackRouteParams\n  )\n}\n"], "names": ["workAsyncStorage", "React", "RenderResult", "chainStreams", "renderToInitialFizzStream", "createDocumentClosingStream", "continueFizzStream", "continueDynamicPrerender", "continueStaticP<PERSON><PERSON>", "continueDynamicHTMLResume", "streamToBuffer", "streamToString", "stripInternalQueries", "NEXT_HMR_REFRESH_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_ROUTER_STALE_TIME_HEADER", "NEXT_URL", "RSC_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_HMR_REFRESH_HASH_COOKIE", "createMetadataContext", "createRequestStoreForRender", "createWorkStore", "getAccessFallbackErrorTypeByStatus", "getAccessFallbackHTTPStatus", "isHTTPAccessFallbackError", "getURLFromRedirectError", "getRedirectStatusCodeFromError", "isRedirectError", "getImplicitTags", "AppRenderSpan", "NextNodeServerSpan", "getTracer", "FlightRenderResult", "createFlightReactServerErrorHandler", "createHTMLReactServerErrorHandler", "createHTMLErrorHandler", "isUserLandError", "getDigestForWellKnownError", "getShortDynamicParamType", "dynamicParamTypes", "getSegmentParam", "getScriptNonceFromHeader", "parseAndValidateFlightRouterState", "createFlightRouterStateFromLoaderTree", "handleAction", "isBailoutToCSRError", "warn", "error", "appendMutableCookies", "createServerInsertedHTML", "getRequiredScripts", "addPathPrefix", "makeGetServerInsertedHTML", "walkTreeWithFlightRouterState", "createComponentTree", "getRootParams", "getAssetQueryString", "getServerModuleMap", "setReferenceManifestsSingleton", "DynamicState", "parsePostponedState", "getDynamicDataPostponedState", "getDynamicHTMLPostponedState", "getPostponedFromState", "isDynamicServerError", "useFlightStream", "createInlinedDataReadableStream", "StaticGenBailoutError", "isStaticGenBailoutError", "getStackWithoutErrorMessage", "accessedDynamicData", "createPostponedAbortSignal", "formatDynamicAPIAccesses", "isPrerenderInterruptedError", "createDynamicTrackingState", "createDynamicValidationState", "trackAllowedDynamicAccess", "throwIfDisallowedDynamic", "PreludeState", "consumeDynamicAccess", "getClientComponentLoaderMetrics", "wrapClientComponentLoader", "createServerModuleMap", "isNodeNextRequest", "parseParameter", "parseRelativeUrl", "AppRouter", "getIsPossibleServerAction", "createInitialRouterState", "createMutableActionQueue", "getRevalidateReason", "PAGE_SEGMENT_KEY", "ServerPrerenderStreamResult", "processPrelude", "ReactServerResult", "createReactServerPrerenderResult", "createReactServerPrerenderResultFromRender", "prerenderAndAbortInSequentialTasks", "printDebugThrownValueForProspectiveRender", "scheduleInSequentialTasks", "waitAtLeastOneReactRenderTask", "workUnitAsyncStorage", "CacheSignal", "getTracedMetadata", "InvariantError", "INFINITE_CACHE", "createComponentStylesAndScripts", "parseLoaderTree", "createPrerenderResumeDataCache", "createRenderResumeDataCache", "isError", "createServerInsertedMetadata", "getPreviouslyRevalidatedTags", "executeRevalidates", "trackPendingChunkLoad", "trackPendingImport", "trackPendingModules", "isReactLargeShellError", "normalizeConventionFilePath", "flightDataPathHeadKey", "getFlightViewportKey", "requestId", "getFlightMetadataKey", "filterStackFrame", "process", "env", "NODE_ENV", "require", "filterStackFrameDEV", "undefined", "parseRequestHeaders", "headers", "options", "isDevWarmupRequest", "isDevWarmup", "isPrefetchRequest", "toLowerCase", "isHmrRefresh", "isRSCRequest", "shouldProvideFlightRouterState", "isRoutePPREnabled", "flightRouterState", "isRouteTreePrefetchRequest", "csp", "nonce", "previouslyRevalidatedTags", "previewModeId", "createNotFoundLoaderTree", "loaderTree", "components", "hasGlobalNotFound", "children", "page", "makeGetDynamicParamFromSegment", "params", "pagePath", "fallbackRouteParams", "getDynamicParamFromSegment", "segment", "segmentParam", "key", "param", "value", "has", "get", "Array", "isArray", "map", "i", "encodeURIComponent", "isCatchall", "type", "isOptionalCatchall", "dynamicParamType", "treeSegment", "split", "slice", "flatMap", "pathSegment", "join", "NonIndex", "statusCode", "isPossibleServerAction", "is404Page", "isInvalidStatusCode", "meta", "name", "content", "generateDynamicRSCPayload", "ctx", "flightData", "componentMod", "tree", "createMetadataComponents", "MetadataBoundary", "ViewportBoundary", "appUsingSizeAdjustment", "query", "workStore", "url", "serveStreamingMetadata", "renderOpts", "skipFlight", "preloadCallbacks", "ViewportTree", "MetadataTree", "getViewportReady", "getMetadataReady", "StreamingMetadataOutlet", "parsed<PERSON><PERSON><PERSON>", "pathname", "metadataContext", "loaderTreeToFilter", "parentParams", "rscHead", "Fragment", "res", "injectedCSS", "Set", "injectedJS", "injectedFontPreloadTags", "rootLayoutIncluded", "path", "actionResult", "a", "f", "b", "sharedContext", "buildId", "S", "isStaticGeneration", "createErrorContext", "renderSource", "routerKind", "routePath", "routeType", "revalidateReason", "generateDynamicFlightRenderResult", "req", "requestStore", "onFlightDataRenderError", "err", "onInstrumentationRequestError", "onError", "dev", "RSCPayload", "run", "experimental", "dynamicIO", "resolveValidation", "validationOutlet", "createValidationOutlet", "_validation", "spawnDynamicValidationInDev", "clientReferenceManifest", "flightReadableStream", "renderToReadableStream", "clientModules", "temporaryReferences", "fetchMetrics", "warmupDevRender", "ComponentMod", "implicitTags", "allowEmptyStaticShell", "rootParams", "prerenderResumeDataCache", "renderController", "AbortController", "prerenderController", "cacheSignal", "prerenderStore", "phase", "renderSignal", "signal", "controller", "dynamicTracking", "revalidate", "expire", "stale", "tags", "renderResumeDataCache", "hmrRefreshHash", "cookies", "captureOwnerStack", "rscPayload", "cacheReady", "abort", "prepareInitialCanonicalUrl", "search", "getRSCPayload", "is404", "missingSlots", "initialTree", "errorType", "seedData", "authInterrupts", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "couldBeIntercepted", "includes", "initialHead", "GlobalError", "styles", "globalErrorStyles", "getGlobalErrorStyles", "isPossiblyPartialHead", "P", "Preloads", "p", "assetPrefix", "c", "m", "G", "s", "postponed", "for<PERSON>ach", "preloadFn", "getErrorRSCPayload", "ssrError", "Error", "html", "id", "head", "body", "template", "data-next-error-message", "message", "data-next-error-digest", "digest", "data-next-error-stack", "stack", "assertClientReferenceManifest", "App", "reactServerStream", "preinitScripts", "ServerInsertedHTMLProvider", "gracefully<PERSON><PERSON><PERSON>", "response", "use", "initialState", "navigatedAt", "initialFlightData", "initialCanonicalUrlParts", "initialParallelRoutes", "Map", "location", "prerendered", "actionQueue", "HeadManagerContext", "Provider", "appDir", "globalErrorState", "ErrorApp", "renderToHTMLOrFlightImpl", "parsedRequestHeaders", "requestEndedState", "postponedState", "serverComponentsHmrCache", "isNotFoundPath", "requestTimestamp", "Date", "now", "serverActionsManifest", "nextFontManifest", "serverActions", "enableTainting", "__next_app__", "instrumented", "shouldTrackModuleLoading", "workUnitStore", "getStore", "__next_require__", "args", "exportsOrPromise", "globalThis", "__next_chunk_load__", "loadingChunk", "loadChunk", "URL", "setIsrStatus", "NEXT_RUNTIME", "originalRequest", "on", "ended", "metrics", "reset", "startSpan", "clientComponentLoading", "startTime", "clientComponentLoadStart", "attributes", "clientComponentLoadCount", "end", "clientComponentLoadTimes", "metadata", "appUsingSizeAdjust", "serverModuleMap", "patchFetch", "taintObjectReference", "<PERSON><PERSON><PERSON>", "from", "crypto", "subtle", "toString", "randomUUID", "nanoid", "isPossibleActionRequest", "isPrefetch", "setRootSpanAttribute", "prerenderToStreamWithTracing", "wrap", "getBodyResult", "spanName", "prerenderToStream", "dynamicAccess", "isDebugDynamicAccesses", "access", "invalidDynamicUsageError", "digestErrorsMap", "size", "buildFailingError", "values", "next", "ssrErrors", "length", "find", "pendingRevalidates", "pendingRevalidateWrites", "pendingRevalidatedTags", "pendingPromise", "finally", "NEXT_PRIVATE_DEBUG_CACHE", "console", "log", "waitUntil", "collectedTags", "fetchTags", "staleHeader", "String", "collectedStale", "<PERSON><PERSON><PERSON><PERSON>", "forceStatic", "collectedRevalidate", "cacheControl", "collectedExpire", "staticBailoutInfo", "description", "dynamicUsageDescription", "dynamicUsageStack", "stream", "onUpdateCookies", "previewProps", "usedDynamic", "forceDynamic", "renderToStreamWithTracing", "renderToStream", "formState", "actionRequestResult", "generateFlight", "notFoundLoaderTree", "result", "assignMetadata", "renderToHTMLOrFlight", "routeModule", "definition", "basePath", "botType", "buildManifest", "crossOrigin", "nextExport", "reactMaxHeadersLength", "shouldWaitOnAllReady", "subresourceIntegrityManifest", "supportsDynamicResponse", "renderServerInsertedHTML", "getServerInsertedMetadata", "tracingMetadata", "getTracePropagationData", "clientTraceMetadata", "polyfills", "polyfillFiles", "filter", "polyfill", "endsWith", "src", "integrity", "noModule", "bootstrapScript", "reactServerErrorsByDigest", "silenceLogger", "onHTMLRenderRSCError", "serverComponentsErrorHandler", "onHTMLRenderSSRError", "allCapturedErrors", "htmlRendererErrorHandler", "reactServerResult", "bind", "append<PERSON><PERSON>er", "prerenderPhase", "environmentName", "DATA", "inlinedReactServerDataStream", "tee", "resume", "htmlStream", "getServerInsertedHTML", "serverCapturedErrors", "inlinedDataStream", "consume", "onHeaders", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bootstrapScripts", "generateStaticHTML", "isBuildTimePrerendering", "validateRootLayout", "shouldBailoutToCSR", "reason", "redirectUrl", "Headers", "mutableCookies", "errorPreinitScripts", "errorBootstrapScript", "errorRSCPayload", "errorServerStream", "fizzStream", "ReactDOMServer", "element", "streamOptions", "finalErr", "bailOnRootNotFound", "outlet", "Promise", "resolve", "isNotFound", "initialServerPrerenderController", "initialServerRenderController", "captureOwnerStackClient", "captureOwnerStackServer", "initialServerPrerenderStore", "initialServerPayload", "pendingInitialServerResult", "prerender", "aborted", "NEXT_DEBUG_BUILD", "__NEXT_VERBOSE_LOGGING", "route", "onPostpone", "LogSafely", "fn", "initialServerResult", "initialClientRenderController", "initialClientPrerenderController", "initialClientPrerenderStore", "pendingInitialClientResult", "asUnclosingStream", "catch", "finalServerController", "serverDynamicTracking", "finalServerPrerenderStore", "finalAttemptRSCPayload", "prerenderResult", "clientDynamicTracking", "finalClientController", "finalClientPrerenderStore", "dynamicValidation", "prelude", "unprocessedPrelude", "errorInfo", "componentStack", "preludeIsEmpty", "Empty", "Full", "thrownValue", "loggingFunction", "Errored", "originalLoggingFunction", "shouldGenerateStaticFlightData", "reactServerPrerenderResult", "setMetadataHeader", "item", "selectStaleTime", "staleTimes", "static", "resumeDataCache", "serverIsDynamic", "prerenderIsPending", "clientIsDynamic", "asStream", "segmentData", "collectSegmentData", "foreverStream", "ReadableStream", "resumeStream", "JSON", "parse", "stringify", "consumeAsStream", "reactServerPrerenderStore", "ssrPrerenderStore", "dynamicAccesses", "prerenderLegacyStore", "flightStream", "modules", "globalErrorModule", "GlobalErrorComponent", "filePath", "getComponent", "dir", "__NEXT_EDGE_PROJECT_DIR", "globalErrorModulePath", "devtoolSegmentExplorer", "SegmentViewNode", "fullPageDataBuffer", "clientSegmentCache", "isEdgeRuntime", "serverConsumerManifest", "moduleLoading", "moduleMap", "edgeRscModuleMapping", "rscModuleMapping", "staleTime"], "mappings": ";AAaA,SACEA,gBAAgB,QAEX,4CAA2C;AAalD,OAAOC,WAAyC,QAAO;AAEvD,OAAOC,kBAGA,mBAAkB;AACzB,SACEC,YAAY,EACZC,yBAAyB,EACzBC,2BAA2B,EAC3BC,kBAAkB,EAClBC,wBAAwB,EACxBC,uBAAuB,EACvBC,yBAAyB,EACzBC,cAAc,EACdC,cAAc,QACT,0CAAyC;AAChD,SAASC,oBAAoB,QAAQ,oBAAmB;AACxD,SACEC,uBAAuB,EACvBC,2BAA2B,EAC3BC,6BAA6B,EAC7BC,6BAA6B,EAC7BC,QAAQ,EACRC,UAAU,EACVC,mCAAmC,EACnCC,4BAA4B,QACvB,6CAA4C;AACnD,SAASC,qBAAqB,QAAQ,sCAAqC;AAC3E,SAASC,2BAA2B,QAAQ,iCAAgC;AAC5E,SAASC,eAAe,QAAQ,8BAA6B;AAC7D,SACEC,kCAAkC,EAClCC,2BAA2B,EAC3BC,yBAAyB,QACpB,oEAAmE;AAC1E,SACEC,uBAAuB,EACvBC,8BAA8B,QACzB,mCAAkC;AACzC,SAASC,eAAe,QAAQ,yCAAwC;AACxE,SAASC,eAAe,QAA2B,uBAAsB;AACzE,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,yBAAwB;AAC1E,SAASC,SAAS,QAAQ,sBAAqB;AAC/C,SAASC,kBAAkB,QAAQ,yBAAwB;AAC3D,SACEC,mCAAmC,EACnCC,iCAAiC,EACjCC,sBAAsB,EAEtBC,eAAe,EACfC,0BAA0B,QACrB,yBAAwB;AAC/B,SACEC,wBAAwB,EACxBC,iBAAiB,QACZ,iCAAgC;AACvC,SAASC,eAAe,QAAQ,sBAAqB;AACrD,SAASC,wBAAwB,QAAQ,iCAAgC;AACzE,SAASC,iCAAiC,QAAQ,2CAA0C;AAC5F,SAASC,qCAAqC,QAAQ,gDAA+C;AACrG,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,mBAAmB,QAAQ,+CAA8C;AAClF,SAASC,IAAI,EAAEC,KAAK,QAAQ,yBAAwB;AACpD,SAASC,oBAAoB,QAAQ,iDAAgD;AACrF,SAASC,wBAAwB,QAAQ,yBAAwB;AACjE,SAASC,kBAAkB,QAAQ,qBAAoB;AACvD,SAASC,aAAa,QAAQ,gDAA+C;AAC7E,SAASC,yBAAyB,QAAQ,kCAAiC;AAC3E,SAASC,6BAA6B,QAAQ,uCAAsC;AACpF,SAASC,mBAAmB,EAAEC,aAAa,QAAQ,0BAAyB;AAC5E,SAASC,mBAAmB,QAAQ,2BAA0B;AAC9D,SACEC,kBAAkB,EAClBC,8BAA8B,QACzB,qBAAoB;AAC3B,SACEC,YAAY,EAEZC,mBAAmB,QACd,oBAAmB;AAC1B,SACEC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,qBAAqB,QAChB,oBAAmB;AAC1B,SAASC,oBAAoB,QAAQ,+CAA8C;AACnF,SACEC,eAAe,EACfC,+BAA+B,QAC1B,wBAAuB;AAC9B,SACEC,qBAAqB,EACrBC,uBAAuB,QAClB,oDAAmD;AAC1D,SAASC,2BAA2B,QAAQ,gCAA+B;AAC3E,SACEC,mBAAmB,EACnBC,0BAA0B,EAC1BC,wBAAwB,EACxBC,2BAA2B,EAC3BC,0BAA0B,EAC1BC,4BAA4B,EAC5BC,yBAAyB,EACzBC,wBAAwB,EACxBC,YAAY,EACZC,oBAAoB,QAEf,sBAAqB;AAC5B,SACEC,+BAA+B,EAC/BC,yBAAyB,QACpB,sCAAqC;AAC5C,SAASC,qBAAqB,QAAQ,iBAAgB;AACtD,SAASC,iBAAiB,QAAQ,uBAAsB;AACxD,SAASC,cAAc,QAAQ,4CAA2C;AAC1E,SAASC,gBAAgB,QAAQ,mDAAkD;AACnF,OAAOC,eAAe,qCAAoC;AAG1D,SAASC,yBAAyB,QAAQ,oCAAmC;AAC7E,SAASC,wBAAwB,QAAQ,qEAAoE;AAC7G,SAASC,wBAAwB,QAAQ,8CAA6C;AACtF,SAASC,mBAAmB,QAAQ,2BAA0B;AAC9D,SAASC,gBAAgB,QAAQ,2BAA0B;AAE3D,SACEC,2BAA2B,EAC3BC,cAAc,QACT,+BAA8B;AACrC,SAEEC,iBAAiB,EACjBC,gCAAgC,EAChCC,0CAA0C,EAC1CC,kCAAkC,QAC7B,+BAA8B;AACrC,SAASC,yCAAyC,QAAQ,6BAA4B;AACtF,SAASC,yBAAyB,QAAQ,4BAA2B;AACrE,SAASC,6BAA6B,QAAQ,sBAAqB;AACnE,SACEC,oBAAoB,QAEf,qCAAoC;AAC3C,SAASC,WAAW,QAAQ,iBAAgB;AAC5C,SAASC,iBAAiB,QAAQ,qBAAoB;AACtD,SAASC,cAAc,QAAQ,mCAAkC;AAEjE,SAASC,cAAc,QAAQ,sBAAqB;AACpD,SAASC,+BAA+B,QAAQ,wCAAuC;AACvF,SAASC,eAAe,QAAQ,sBAAqB;AACrD,SACEC,8BAA8B,EAC9BC,2BAA2B,QAGtB,yCAAwC;AAE/C,OAAOC,aAAa,qBAAoB;AACxC,SAASC,4BAA4B,QAAQ,uDAAsD;AACnG,SAASC,4BAA4B,QAAQ,kBAAiB;AAC9D,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SACEC,qBAAqB,EACrBC,kBAAkB,EAClBC,mBAAmB,QACd,iDAAgD;AACvD,SAASC,sBAAsB,QAAQ,4BAA2B;AAElE,SAASC,2BAA2B,QAAQ,0BAAyB;AAqDrE,MAAMC,wBAAwB;AAC9B,MAAMC,uBAAuB,CAACC,YAAsBA,YAAY;AAChE,MAAMC,uBAAuB,CAACD,YAAsBA,YAAY;AAEhE,MAAME,mBACJC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACrB,AAACC,QAAQ,sBACNC,mBAAmB,GACtBC;AAmBN,SAASC,oBACPC,OAA4B,EAC5BC,OAAmC;IAEnC,MAAMC,qBAAqBD,QAAQE,WAAW,KAAK;IAEnD,2DAA2D;IAC3D,MAAMC,oBACJF,sBACAF,OAAO,CAACvH,4BAA4B4H,WAAW,GAAG,KAAKP;IAEzD,MAAMQ,eACJN,OAAO,CAACxH,wBAAwB6H,WAAW,GAAG,KAAKP;IAErD,2DAA2D;IAC3D,MAAMS,eACJL,sBAAsBF,OAAO,CAACnH,WAAWwH,WAAW,GAAG,KAAKP;IAE9D,MAAMU,iCACJD,gBAAiB,CAAA,CAACH,qBAAqB,CAACH,QAAQQ,iBAAiB,AAAD;IAElE,MAAMC,oBAAoBF,iCACtBjG,kCACEyF,OAAO,CAACtH,8BAA8B2H,WAAW,GAAG,IAEtDP;IAEJ,sEAAsE;IACtE,MAAMa,6BACJX,OAAO,CAAClH,oCAAoCuH,WAAW,GAAG,KAAK;IAEjE,MAAMO,MACJZ,OAAO,CAAC,0BAA0B,IAClCA,OAAO,CAAC,sCAAsC;IAEhD,MAAMa,QACJ,OAAOD,QAAQ,WAAWtG,yBAAyBsG,OAAOd;IAE5D,MAAMgB,4BAA4BjC,6BAChCmB,SACAC,QAAQc,aAAa;IAGvB,OAAO;QACLL;QACAN;QACAO;QACAL;QACAC;QACAL;QACAW;QACAC;IACF;AACF;AAEA,SAASE,yBAAyBC,UAAsB;IACtD,MAAMC,aAAaD,UAAU,CAAC,EAAE;IAChC,MAAME,oBAAoB,CAAC,CAACD,UAAU,CAAC,mBAAmB;IAC1D,OAAO;QACL;QACA;YACEE,UAAU;gBACR5D;gBACA,CAAC;gBACD;oBACE6D,MAAMH,UAAU,CAAC,mBAAmB,IAAIA,UAAU,CAAC,YAAY;gBACjE;aACD;QACH;QACA,gEAAgE;QAChEC,oBAAoBD,aAAa,CAAC;KACnC;AACH;AAEA;;CAEC,GACD,SAASI,+BACPC,MAA8B,EAC9BC,QAAgB,EAChBC,mBAA+C;IAE/C,OAAO,SAASC,2BACd,gCAAgC;IAChCC,OAAe;QAEf,MAAMC,eAAevH,gBAAgBsH;QACrC,IAAI,CAACC,cAAc;YACjB,OAAO;QACT;QAEA,MAAMC,MAAMD,aAAaE,KAAK;QAE9B,IAAIC,QAAQR,MAAM,CAACM,IAAI;QAEvB,IAAIJ,uBAAuBA,oBAAoBO,GAAG,CAACJ,aAAaE,KAAK,GAAG;YACtEC,QAAQN,oBAAoBQ,GAAG,CAACL,aAAaE,KAAK;QACpD,OAAO,IAAII,MAAMC,OAAO,CAACJ,QAAQ;YAC/BA,QAAQA,MAAMK,GAAG,CAAC,CAACC,IAAMC,mBAAmBD;QAC9C,OAAO,IAAI,OAAON,UAAU,UAAU;YACpCA,QAAQO,mBAAmBP;QAC7B;QAEA,IAAI,CAACA,OAAO;YACV,MAAMQ,aAAaX,aAAaY,IAAI,KAAK;YACzC,MAAMC,qBAAqBb,aAAaY,IAAI,KAAK;YAEjD,IAAID,cAAcE,oBAAoB;gBACpC,MAAMC,mBAAmBtI,iBAAiB,CAACwH,aAAaY,IAAI,CAAC;gBAC7D,oEAAoE;gBACpE,6DAA6D;gBAC7D,IAAIC,oBAAoB;oBACtB,OAAO;wBACLX,OAAOD;wBACPE,OAAO;wBACPS,MAAME;wBACNC,aAAa;4BAACd;4BAAK;4BAAIa;yBAAiB;oBAC1C;gBACF;gBAEA,+EAA+E;gBAC/E,wFAAwF;gBACxFX,QAAQP,SACLoB,KAAK,CAAC,IACP,gCAAgC;iBAC/BC,KAAK,CAAC,EACP,oDAAoD;iBACnDC,OAAO,CAAC,CAACC;oBACR,MAAMjB,QAAQ7E,eAAe8F;oBAC7B,yDAAyD;oBACzD,wDAAwD;oBACxD,OAAOxB,MAAM,CAACO,MAAMD,GAAG,CAAC,IAAIC,MAAMD,GAAG;gBACvC;gBAEF,OAAO;oBACLC,OAAOD;oBACPE;oBACAS,MAAME;oBACN,wCAAwC;oBACxCC,aAAa;wBAACd;wBAAKE,MAAMiB,IAAI,CAAC;wBAAMN;qBAAiB;gBACvD;YACF;QACF;QAEA,MAAMF,OAAOrI,yBAAyByH,aAAaY,IAAI;QAEvD,OAAO;YACLV,OAAOD;YACP,yCAAyC;YACzCE,OAAOA;YACP,iDAAiD;YACjDY,aAAa;gBAACd;gBAAKK,MAAMC,OAAO,CAACJ,SAASA,MAAMiB,IAAI,CAAC,OAAOjB;gBAAOS;aAAK;YACxEA,MAAMA;QACR;IACF;AACF;AAEA,SAASS,SAAS,EAChBzB,QAAQ,EACR0B,UAAU,EACVC,sBAAsB,EAKvB;IACC,MAAMC,YAAY5B,aAAa;IAC/B,MAAM6B,sBAAsB,OAAOH,eAAe,YAAYA,aAAa;IAE3E,gEAAgE;IAChE,yEAAyE;IACzE,IAAI,CAACC,0BAA2BC,CAAAA,aAAaC,mBAAkB,GAAI;QACjE,qBAAO,KAACC;YAAKC,MAAK;YAASC,SAAQ;;IACrC;IACA,OAAO;AACT;AAEA;;;;CAIC,GACD,eAAeC,0BACbC,GAAqB,EACrBzD,OAGC;IAED,yDAAyD;IACzD,0GAA0G;IAE1G,gGAAgG;IAChG,mGAAmG;IACnG,0GAA0G;IAC1G,mFAAmF;IACnF,IAAI0D,aAAyB;IAE7B,MAAM,EACJC,cAAc,EACZC,MAAM5C,UAAU,EAChB6C,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACDtC,0BAA0B,EAC1BuC,sBAAsB,EACtBC,KAAK,EACL5E,SAAS,EACToB,iBAAiB,EACjByD,SAAS,EACTC,GAAG,EACJ,GAAGV;IAEJ,MAAMW,yBAAyB,CAAC,CAACX,IAAIY,UAAU,CAACD,sBAAsB;IAEtE,IAAI,EAACpE,2BAAAA,QAASsE,UAAU,GAAE;QACxB,MAAMC,mBAAqC,EAAE;QAE7C,MAAM,EACJC,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,uBAAuB,EACxB,GAAGf,yBAAyB;YAC3BD,MAAM5C;YACN6D,aAAaZ;YACba,UAAUX,IAAIW,QAAQ;YACtBC,iBAAiBhM,sBAAsB0K,IAAIY,UAAU;YACrD5C;YACAuC;YACAE;YACAJ;YACAC;YACAK;QACF;QAEAV,aAAa,AACX,CAAA,MAAMzI,8BAA8B;YAClCwI;YACAuB,oBAAoBhE;YACpBiE,cAAc,CAAC;YACfxE;YACA,+CAA+C;YAC/CyE,uBACE,MAACvN,MAAMwN,QAAQ;;kCAEb,KAACnC;wBACCzB,UAAUkC,IAAIlC,QAAQ;wBACtB0B,YAAYQ,IAAI2B,GAAG,CAACnC,UAAU;wBAC9BC,wBAAwBO,IAAIP,sBAAsB;;kCAGpD,KAACsB,kBAAkBpF,qBAAqBC;kCACxC,KAACoF,kBAAkBnF,qBAAqBD;;eATrBF;YAYvBkG,aAAa,IAAIC;YACjBC,YAAY,IAAID;YAChBE,yBAAyB,IAAIF;YAC7BG,oBAAoB;YACpBf;YACAC;YACAJ;YACAK;QACF,EAAC,EACDzC,GAAG,CAAC,CAACuD,OAASA,KAAK9C,KAAK,CAAC,IAAI,+BAA+B;;IAChE;IAEA,sEAAsE;IACtE,+EAA+E;IAC/E,wBAAwB;IACxB,IAAI5C,2BAAAA,QAAS2F,YAAY,EAAE;QACzB,OAAO;YACLC,GAAG5F,QAAQ2F,YAAY;YACvBE,GAAGnC;YACHoC,GAAGrC,IAAIsC,aAAa,CAACC,OAAO;QAC9B;IACF;IAEA,0CAA0C;IAC1C,OAAO;QACLF,GAAGrC,IAAIsC,aAAa,CAACC,OAAO;QAC5BH,GAAGnC;QACHuC,GAAG/B,UAAUgC,kBAAkB;IACjC;AACF;AAEA,SAASC,mBACP1C,GAAqB,EACrB2C,YAAiD;IAEjD,OAAO;QACLC,YAAY;QACZC,WAAW7C,IAAIlC,QAAQ;QACvB,yEAAyE;QACzEgF,WAAW9C,IAAIP,sBAAsB,GAAG,WAAW;QACnDkD;QACAI,kBAAkBlJ,oBAAoBmG,IAAIS,SAAS;IACrD;AACF;AACA;;;CAGC,GACD,eAAeuC,kCACbC,GAAoB,EACpBjD,GAAqB,EACrBkD,YAA0B,EAC1B3G,OAMC;IAED,MAAMqE,aAAaZ,IAAIY,UAAU;IAEjC,SAASuC,wBAAwBC,GAAkB;QACjD,OAAOxC,WAAWyC,6BAA6B,oBAAxCzC,WAAWyC,6BAA6B,MAAxCzC,YACLwC,KACAH,KACAP,mBAAmB1C,KAAK;IAE5B;IACA,MAAMsD,UAAUlN,oCACd,CAAC,CAACwK,WAAW2C,GAAG,EAChBJ;IAGF,MAAMK,aAGF,MAAMhJ,qBAAqBiJ,GAAG,CAChCP,cACAnD,2BACAC,KACAzD;IAGF,IACE,qDAAqD;IACrDqE,WAAW2C,GAAG,IACd,uEAAuE;IACvExH,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,yEAAyE;IACzE2E,WAAW8C,YAAY,CAACC,SAAS,EACjC;QACA,MAAM,CAACC,mBAAmBC,iBAAiB,GAAGC;QAC9CN,WAAWO,WAAW,GAAGF;QAEzBG,4BACEJ,mBACA5D,IAAIE,YAAY,CAACC,IAAI,EACrBH,KACA,OACAA,IAAIiE,uBAAuB,EAC3Bf;IAEJ;IAEA,0FAA0F;IAC1F,mCAAmC;IACnC,MAAMgB,uBAAuB1J,qBAAqBiJ,GAAG,CACnDP,cACAlD,IAAIE,YAAY,CAACiE,sBAAsB,EACvCX,YACAxD,IAAIiE,uBAAuB,CAACG,aAAa,EACzC;QACEd;QACAe,mBAAmB,EAAE9H,2BAAAA,QAAS8H,mBAAmB;QACjDvI;IACF;IAGF,OAAO,IAAI3F,mBAAmB+N,sBAAsB;QAClDI,cAActE,IAAIS,SAAS,CAAC6D,YAAY;IAC1C;AACF;AAEA;;;;;;CAMC,GACD,eAAeC,gBACbtB,GAAoB,EACpBjD,GAAqB;IAErB,MAAM,EACJiE,uBAAuB,EACvB/D,cAAcsE,YAAY,EAC1BxG,0BAA0B,EAC1ByG,YAAY,EACZ7D,UAAU,EACVH,SAAS,EACV,GAAGT;IAEJ,MAAM,EACJ0E,wBAAwB,KAAK,EAC7BnB,GAAG,EACHF,6BAA6B,EAC9B,GAAGzC;IAEJ,IAAI,CAAC2C,KAAK;QACR,MAAM,qBAEL,CAFK,IAAI5I,eACR,mFADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMgK,aAAajN,cACjB8M,aAAarE,IAAI,EACjBnC;IAGF,SAASmF,wBAAwBC,GAAkB;QACjD,OAAOC,iDAAAA,8BACLD,KACAH,KACAP,mBAAmB1C,KAAK;IAE5B;IACA,MAAMsD,UAAUlN,oCACd,MACA+M;IAGF,2EAA2E;IAC3E,kBAAkB;IAClB,MAAMyB,2BAA2B7J;IAEjC,MAAM8J,mBAAmB,IAAIC;IAC7B,MAAMC,sBAAsB,IAAID;IAChC,MAAME,cAAc,IAAIvK;IAExB,MAAMwK,iBAAiC;QACrCnG,MAAM;QACNoG,OAAO;QACPP;QACAF;QACAU,cAAcN,iBAAiBO,MAAM;QACrCC,YAAYN;QACZC;QACAM,iBAAiB;QACjBZ;QACAa,YAAY3K;QACZ4K,QAAQ5K;QACR6K,OAAO7K;QACP8K,MAAM,EAAE;QACRd;QACAe,uBAAuB;QACvBC,gBAAgB3C,IAAI4C,OAAO,CAACxQ,6BAA6B;QACzDyQ,mBAAmBtB,aAAasB,iBAAiB;IACnD;IAEA,MAAMC,aAAa,MAAMvL,qBAAqBiJ,GAAG,CAC/CwB,gBACAlF,2BACAC;IAGF,0FAA0F;IAC1F,mCAAmC;IACnCxF,qBAAqBiJ,GAAG,CACtBwB,gBACAT,aAAaL,sBAAsB,EACnC4B,YACA9B,wBAAwBG,aAAa,EACrC;QACEtI;QACAwH;QACA8B,QAAQP,iBAAiBO,MAAM;IACjC;IAGF,8EAA8E;IAC9E7J,oBAAoByJ;IACpB,MAAMA,YAAYgB,UAAU;IAE5B,uFAAuF;IACvFf,eAAeL,wBAAwB,GAAG;IAC1C,mBAAmB;IACnBC,iBAAiBoB,KAAK;IAEtB,0EAA0E;IAC1E,+EAA+E;IAC/E,+EAA+E;IAC/E,OAAO,IAAI9P,mBAAmB,IAAI;QAChCmO,cAAc7D,UAAU6D,YAAY;QACpCqB,uBAAuB3K,4BACrB4J;IAEJ;AACF;AAEA;;;;;CAKC,GACD,SAASsB,2BAA2BxF,GAAwB;IAC1D,OAAO,AAACA,CAAAA,IAAIW,QAAQ,GAAGX,IAAIyF,MAAM,AAAD,EAAGjH,KAAK,CAAC;AAC3C;AAEA,wFAAwF;AACxF,eAAekH,cACbjG,IAAgB,EAChBH,GAAqB,EACrBqG,KAAc;IAEd,MAAMzE,cAAc,IAAIC;IACxB,MAAMC,aAAa,IAAID;IACvB,MAAME,0BAA0B,IAAIF;IACpC,IAAIyE;IAEJ,sDAAsD;IACtD,IAAIvK,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CqK,eAAe,IAAIzE;IACrB;IAEA,MAAM,EACJ7D,0BAA0B,EAC1BwC,KAAK,EACLD,sBAAsB,EACtBL,cAAc,EACZE,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACDI,GAAG,EACHD,SAAS,EACV,GAAGT;IAEJ,MAAMuG,cAAczP,sCAClBqJ,MACAnC,4BACAwC;IAEF,MAAMG,yBAAyB,CAAC,CAACX,IAAIY,UAAU,CAACD,sBAAsB;IACtE,MAAMlD,oBAAoB,CAAC,CAAC0C,IAAI,CAAC,EAAE,CAAC,mBAAmB;IAEvD,MAAM,EACJY,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,uBAAuB,EACxB,GAAGf,yBAAyB;QAC3BD;QACA,6FAA6F;QAC7F,0BAA0B;QAC1B,wFAAwF;QACxF,2CAA2C;QAC3C,yFAAyF;QACzFqG,WAAWH,SAAS,CAAC5I,oBAAoB,cAAcrB;QACvDgF,aAAaZ;QACba,UAAUX,IAAIW,QAAQ;QACtBC,iBAAiBhM,sBAAsB0K,IAAIY,UAAU;QACrD5C;QACAuC;QACAE;QACAJ;QACAC;QACAK;IACF;IAEA,MAAMG,mBAAqC,EAAE;IAE7C,MAAM2F,WAAW,MAAMhP,oBAAoB;QACzCuI;QACAzC,YAAY4C;QACZqB,cAAc,CAAC;QACfI;QACAE;QACAC;QACAC,oBAAoB;QACpBf;QACAC;QACAoF;QACAxF;QACA4F,gBAAgB1G,IAAIY,UAAU,CAAC8C,YAAY,CAACgD,cAAc;QAC1DvF;IACF;IAEA,0FAA0F;IAC1F,6FAA6F;IAC7F,2FAA2F;IAC3F,MAAMwF,aAAa3G,IAAI2B,GAAG,CAACiF,SAAS,CAAC;IACrC,MAAMC,qBACJ,OAAOF,eAAe,YAAYA,WAAWG,QAAQ,CAAC5R;IAExD,MAAM6R,4BACJ,MAAC7S,MAAMwN,QAAQ;;0BACb,KAACnC;gBACCzB,UAAUkC,IAAIlC,QAAQ;gBACtB0B,YAAYQ,IAAI2B,GAAG,CAACnC,UAAU;gBAC9BC,wBAAwBO,IAAIP,sBAAsB;;0BAEpD,KAACsB;0BACD,KAACC;;OAPkBtF;IAWvB,MAAM,EAAEsL,WAAW,EAAEC,QAAQC,iBAAiB,EAAE,GAAG,MAAMC,qBACvDhH,MACAH;IAGF,uEAAuE;IACvE,2EAA2E;IAC3E,wEAAwE;IACxE,8CAA8C;IAC9C,EAAE;IACF,qEAAqE;IACrE,MAAMoH,wBACJ3G,UAAUgC,kBAAkB,IAC5BzC,IAAIY,UAAU,CAAC8C,YAAY,CAAC3G,iBAAiB,KAAK;IAEpD,OAAO;QACL,6FAA6F;QAC7FsK,iBAAG,KAACC;YAASxG,kBAAkBA;;QAC/BuB,GAAGrC,IAAIsC,aAAa,CAACC,OAAO;QAC5BgF,GAAGvH,IAAIwH,WAAW;QAClBC,GAAGvB,2BAA2BxF;QAC9B/B,GAAG,CAAC,CAACkI;QACLzE,GAAG;YACD;gBACEmE;gBACAE;gBACAM;gBACAK;aACD;SACF;QACDM,GAAGpB;QACHqB,GAAG;YAACX;YAAaE;SAAkB;QACnCU,GAAG,OAAO5H,IAAIY,UAAU,CAACiH,SAAS,KAAK;QACvCrF,GAAG/B,UAAUgC,kBAAkB;IACjC;AACF;AAEA;;;;;CAKC,GACD,SAAS6E,SAAS,EAAExG,gBAAgB,EAAoC;IACtEA,iBAAiBgH,OAAO,CAAC,CAACC,YAAcA;IACxC,OAAO;AACT;AAEA,sFAAsF;AACtF,eAAeC,mBACb7H,IAAgB,EAChBH,GAAqB,EACrBiI,QAAiB,EACjBzB,SAAqD;IAErD,MAAM,EACJxI,0BAA0B,EAC1BwC,KAAK,EACLD,sBAAsB,EACtBL,cAAc,EACZE,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACDI,GAAG,EACHD,SAAS,EACV,GAAGT;IAEJ,MAAMW,yBAAyB,CAAC,CAACX,IAAIY,UAAU,CAACD,sBAAsB;IACtE,MAAM,EAAEK,YAAY,EAAED,YAAY,EAAE,GAAGX,yBAAyB;QAC9DD;QACAiB,aAAaZ;QACba,UAAUX,IAAIW,QAAQ;QACtBC,iBAAiBhM,sBAAsB0K,IAAIY,UAAU;QACrD4F;QACAxI;QACAuC;QACAE;QACAJ;QACAC;QACAK,wBAAwBA;IAC1B;IAEA,MAAMoG,4BACJ,MAAC7S,MAAMwN,QAAQ;;0BACb,KAACnC;gBACCzB,UAAUkC,IAAIlC,QAAQ;gBACtB0B,YAAYQ,IAAI2B,GAAG,CAACnC,UAAU;gBAC9BC,wBAAwBO,IAAIP,sBAAsB;;0BAEpD,KAACsB;YACAhF,QAAQC,GAAG,CAACC,QAAQ,KAAK,+BACxB,KAAC2D;gBAAKC,MAAK;gBAAaC,SAAQ;;0BAElC,KAACkB;;OAVkBtF;IAcvB,MAAM6K,cAAczP,sCAClBqJ,MACAnC,4BACAwC;IAGF,IAAI4C,MAAyBhH;IAC7B,IAAI6L,UAAU;QACZ7E,MAAMnI,QAAQgN,YAAYA,WAAW,qBAAwB,CAAxB,IAAIC,MAAMD,WAAW,KAArB,qBAAA;mBAAA;wBAAA;0BAAA;QAAuB;IAC9D;IAEA,0EAA0E;IAC1E,+CAA+C;IAC/C,MAAMxB,WAA8B;QAClCF,WAAW,CAAC,EAAE;sBACd,MAAC4B;YAAKC,IAAG;;8BACP,KAACC;8BACD,KAACC;8BACEvM,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgBmH,oBACxC,KAACmF;wBACCC,2BAAyBpF,IAAIqF,OAAO;wBACpCC,0BAAwB,YAAYtF,MAAMA,IAAIuF,MAAM,GAAG;wBACvDC,yBAAuBxF,IAAIyF,KAAK;yBAEhC;;;;QAGR,CAAC;QACD;QACA;KACD;IAED,MAAM,EAAE7B,WAAW,EAAEC,QAAQC,iBAAiB,EAAE,GAAG,MAAMC,qBACvDhH,MACAH;IAGF,MAAMoH,wBACJ3G,UAAUgC,kBAAkB,IAC5BzC,IAAIY,UAAU,CAAC8C,YAAY,CAAC3G,iBAAiB,KAAK;IAEpD,OAAO;QACLsF,GAAGrC,IAAIsC,aAAa,CAACC,OAAO;QAC5BgF,GAAGvH,IAAIwH,WAAW;QAClBC,GAAGvB,2BAA2BxF;QAC9BgH,GAAGtL;QACHuC,GAAG;QACHyD,GAAG;YACD;gBACEmE;gBACAE;gBACAM;gBACAK;aACD;SACF;QACDO,GAAG;YAACX;YAAaE;SAAkB;QACnCU,GAAG,OAAO5H,IAAIY,UAAU,CAACiH,SAAS,KAAK;QACvCrF,GAAG/B,UAAUgC,kBAAkB;IACjC;AACF;AAEA,SAASqG,8BACP7E,uBAA8D;IAI9D,IAAI,CAACA,yBAAyB;QAC5B,MAAM,qBAAqE,CAArE,IAAItJ,eAAe,oDAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAoE;IAC5E;AACF;AAEA,mFAAmF;AACnF,SAASoO,IAAO,EACdC,iBAAiB,EACjBC,cAAc,EACdhF,uBAAuB,EACvBiF,0BAA0B,EAC1BC,iBAAiB,EACjBhM,KAAK,EAQN;IACC8L;IACA,MAAMG,WAAWlV,MAAMmV,GAAG,CACxBjR,gBACE4Q,mBACA/E,yBACA9G;IAIJ,MAAMmM,eAAe3P,yBAAyB;QAC5C,gEAAgE;QAChE,kBAAkB;QAClB4P,aAAa,CAAC;QACdC,mBAAmBJ,SAAShH,CAAC;QAC7BqH,0BAA0BL,SAAS3B,CAAC;QACpCiC,uBAAuB,IAAIC;QAC3B,gDAAgD;QAChD,+CAA+C;QAC/CC,UAAU;QACV/C,oBAAoBuC,SAASzK,CAAC;QAC9BkJ,WAAWuB,SAASxB,CAAC;QACrBiC,aAAaT,SAAS5G,CAAC;IACzB;IAEA,MAAMsH,cAAclQ,yBAAyB0P,cAAc;IAE3D,MAAM,EAAES,kBAAkB,EAAE,GAC1B7N,QAAQ;IAEV,qBACE,KAAC6N,mBAAmBC,QAAQ;QAC1B3L,OAAO;YACL4L,QAAQ;YACR9M;QACF;kBAEA,cAAA,KAAC+L;sBACC,cAAA,KAACzP;gBACCqQ,aAAaA;gBACbI,kBAAkBd,SAASzB,CAAC;gBAC5BH,aAAa4B,SAAS7B,CAAC;gBACvB4B,mBAAmBA;;;;AAK7B;AAEA,oGAAoG;AACpG,uGAAuG;AACvG,sBAAsB;AACtB,SAASgB,SAAY,EACnBnB,iBAAiB,EACjBC,cAAc,EACdhF,uBAAuB,EACvBiF,0BAA0B,EAC1BC,iBAAiB,EACjBhM,KAAK,EAQN;IACC8L;IACA,MAAMG,WAAWlV,MAAMmV,GAAG,CACxBjR,gBACE4Q,mBACA/E,yBACA9G;IAIJ,MAAMmM,eAAe3P,yBAAyB;QAC5C,gEAAgE;QAChE,kBAAkB;QAClB4P,aAAa,CAAC;QACdC,mBAAmBJ,SAAShH,CAAC;QAC7BqH,0BAA0BL,SAAS3B,CAAC;QACpCiC,uBAAuB,IAAIC;QAC3B,gDAAgD;QAChD,+CAA+C;QAC/CC,UAAU;QACV/C,oBAAoBuC,SAASzK,CAAC;QAC9BkJ,WAAWuB,SAASxB,CAAC;QACrBiC,aAAaT,SAAS5G,CAAC;IACzB;IAEA,MAAMsH,cAAclQ,yBAAyB0P,cAAc;IAE3D,qBACE,KAACJ;kBACC,cAAA,KAACzP;YACCqQ,aAAaA;YACbI,kBAAkBd,SAASzB,CAAC;YAC5BH,aAAa4B,SAAS7B,CAAC;YACvB4B,mBAAmBA;;;AAI3B;AASA,eAAeiB,yBACbnH,GAAoB,EACpBtB,GAAqB,EACrBjB,GAAwC,EACxC5C,QAAgB,EAChB0C,KAAyB,EACzBI,UAAsB,EACtBH,SAAoB,EACpB4J,oBAA0C,EAC1CC,iBAAsC,EACtCC,cAAqC,EACrCC,wBAA8D,EAC9DlI,aAA+B;IAE/B,MAAMmI,iBAAiB3M,aAAa;IACpC,IAAI2M,gBAAgB;QAClB9I,IAAInC,UAAU,GAAG;IACnB;IAEA,qEAAqE;IACrE,wEAAwE;IACxE,6EAA6E;IAC7E,+EAA+E;IAC/E,MAAMkL,mBAAmBC,KAAKC,GAAG;IAEjC,MAAM,EACJ3G,uBAAuB,EACvB4G,qBAAqB,EACrBrG,YAAY,EACZsG,gBAAgB,EAChBC,aAAa,EACbvD,cAAc,EAAE,EAChBwD,cAAc,EACf,GAAGpK;IAEJ,2DAA2D;IAC3D,uEAAuE;IACvE,IAAI4D,aAAayG,YAAY,EAAE;QAC7B,MAAMC,eAAe9R,0BAA0BoL;QAE/C,kEAAkE;QAClE,0EAA0E;QAC1E,+EAA+E;QAC/E,8DAA8D;QAE9D,MAAM2G,2BAA2B;YAC/B,IAAI,CAACvK,WAAW8C,YAAY,CAACC,SAAS,EAAE;gBACtC,OAAO;YACT;YACA,IAAI/C,WAAW2C,GAAG,EAAE;gBAClB,OAAO;YACT;YACA,MAAM6H,gBAAgB5Q,qBAAqB6Q,QAAQ;YACnD,OAAO,CAAC,CACND,CAAAA,iBACCA,CAAAA,cAActM,IAAI,KAAK,eACtBsM,cAActM,IAAI,KAAK,sBACvBsM,cAActM,IAAI,KAAK,OAAM,CAAC;QAEpC;QAEA,MAAMwM,mBAAgD,CAAC,GAAGC;YACxD,MAAMC,mBAAmBN,aAAahP,OAAO,IAAIqP;YACjD,IAAIJ,4BAA4B;gBAC9B,+CAA+C;gBAC/C7P,mBAAmBkQ;YACrB;YACA,OAAOA;QACT;QACA,mBAAmB;QACnBC,WAAWH,gBAAgB,GAAGA;QAE9B,MAAMI,sBAAqD,CAAC,GAAGH;YAC7D,MAAMI,eAAeT,aAAaU,SAAS,IAAIL;YAC/C,IAAIJ,4BAA4B;gBAC9B9P,sBAAsBsQ;YACxB;YACA,OAAOA;QACT;QACA,mBAAmB;QACnBF,WAAWC,mBAAmB,GAAGA;IACnC;IAEA,IAAI3P,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,uCAAuC;QACvC,MAAM,EAAEoF,QAAQ,EAAE,GAAG,IAAIwK,IAAI5I,IAAIvC,GAAG,IAAI,KAAK;QAC7CE,WAAWkL,YAAY,oBAAvBlL,WAAWkL,YAAY,MAAvBlL,YAA0BS,UAAU;IACtC;IAEA,IACE,qEAAqE;IACrE,6DAA6D;IAC7DtF,QAAQC,GAAG,CAAC+P,YAAY,KAAK,UAC7BzS,kBAAkB2J,MAClB;QACAA,IAAI+I,eAAe,CAACC,EAAE,CAAC,OAAO;YAC5B3B,kBAAkB4B,KAAK,GAAG;YAE1B,IAAI,iBAAiBT,YAAY;gBAC/B,MAAMU,UAAUhT,gCAAgC;oBAAEiT,OAAO;gBAAK;gBAC9D,IAAID,SAAS;oBACXjW,YACGmW,SAAS,CAACpW,mBAAmBqW,sBAAsB,EAAE;wBACpDC,WAAWJ,QAAQK,wBAAwB;wBAC3CC,YAAY;4BACV,iCACEN,QAAQO,wBAAwB;4BAClC,kBAAkBzW,mBAAmBqW,sBAAsB;wBAC7D;oBACF,GACCK,GAAG,CACFR,QAAQK,wBAAwB,GAC9BL,QAAQS,wBAAwB;gBAExC;YACF;QACF;IACF;IAEA,MAAMC,WAAwC;QAC5CrN,YAAYiL,iBAAiB,MAAMrO;IACrC;IAEA,MAAMmE,yBAAyB,CAAC,EAACuK,oCAAAA,iBAAkBgC,kBAAkB;IAErEhE,8BAA8B7E;IAE9B,MAAM8I,kBAAkB1T,sBAAsB;QAAEwR;IAAsB;IAEtEhT,+BAA+B;QAC7B8F,MAAM8C,UAAU9C,IAAI;QACpBsG;QACA4G;QACAkC;IACF;IAEAvI,aAAawI,UAAU;IAEvB,oDAAoD;IACpD,MAAM,EAAE7M,MAAM5C,UAAU,EAAE0P,oBAAoB,EAAE,GAAGzI;IACnD,IAAIwG,gBAAgB;QAClBiC,qBACE,kFACAlR,QAAQC,GAAG;IAEf;IAEAyE,UAAU6D,YAAY,GAAG,EAAE;IAC3BuI,SAASvI,YAAY,GAAG7D,UAAU6D,YAAY;IAE9C,qCAAqC;IACrC9D,QAAQ;QAAE,GAAGA,KAAK;IAAC;IACnB3L,qBAAqB2L;IAErB,MAAM,EACJxD,iBAAiB,EACjBN,iBAAiB,EACjBG,YAAY,EACZL,kBAAkB,EAClBI,YAAY,EACZO,KAAK,EACN,GAAGkN;IAEJ,MAAM,EAAE5H,kBAAkB,EAAE1E,mBAAmB,EAAE,GAAG0C;IAEpD;;;GAGC,GACD,IAAI7E;IAEJ,IAAI6G,oBAAoB;QACtB7G,YAAYsR,OAAOC,IAAI,CACrB,MAAMC,OAAOC,MAAM,CAAC1E,MAAM,CAAC,SAASuE,OAAOC,IAAI,CAAClK,IAAIvC,GAAG,IACvD4M,QAAQ,CAAC;IACb,OAAO,IAAIvR,QAAQC,GAAG,CAAC+P,YAAY,KAAK,QAAQ;QAC9CnQ,YAAYwR,OAAOG,UAAU;IAC/B,OAAO;QACL3R,YAAY,AACVM,QAAQ,6BACRsR,MAAM;IACV;IAEA;;GAEC,GACD,MAAM3P,SAAS+C,WAAW/C,MAAM,IAAI,CAAC;IAErC,MAAMG,6BAA6BJ,+BACjCC,QACAC,UACAC;IAGF,MAAM0P,0BAA0B/T,0BAA0BuJ;IAE1D,MAAMwB,eAAe,MAAM1O,gBACzB0K,UAAU9C,IAAI,EACd+C,KACA3C;IAGF,MAAMiC,MAAwB;QAC5BE,cAAcsE;QACd9D;QACAE;QACAH;QACA4J;QACArM;QACAwC;QACAkN,YAAYhR;QACZ+C,wBAAwBgO;QACxB/C;QACAnK;QACAvD;QACApB;QACAkC;QACAmG;QACAuD;QACAiD;QACAtN;QACAwE;QACAW;QACAmC;IACF;IAEAvO,YAAYyX,oBAAoB,CAAC,cAAc7P;IAE/C,IAAI2E,oBAAoB;YAwGlBoK;QAvGJ,mEAAmE;QACnE,4CAA4C;QAC5C,MAAMe,+BAA+B1X,YAAY2X,IAAI,CACnD7X,cAAc8X,aAAa,EAC3B;YACEC,UAAU,CAAC,sBAAsB,EAAEjQ,UAAU;YAC7C2O,YAAY;gBACV,cAAc3O;YAChB;QACF,GACAkQ;QAGF,MAAM5E,WAAW,MAAMwE,6BACrB3K,KACAtB,KACA3B,KACA6M,UACAtP;QAGF,8EAA8E;QAC9E,mCAAmC;QACnC,0CAA0C;QAC1C,IACE6L,SAAS6E,aAAa,IACtBxV,oBAAoB2Q,SAAS6E,aAAa,KAC1CrN,WAAWsN,sBAAsB,EACjC;YACAjX,KAAK;YACL,KAAK,MAAMkX,UAAUxV,yBAAyByQ,SAAS6E,aAAa,EAAG;gBACrEhX,KAAKkX;YACP;QACF;QAEA,mEAAmE;QACnE,oCAAoC;QACpC,IAAI1N,UAAU2N,wBAAwB,EAAE;YACtC,MAAM3N,UAAU2N,wBAAwB;QAC1C;QACA,IAAIhF,SAASiF,eAAe,CAACC,IAAI,EAAE;YACjC,MAAMC,oBAAoBnF,SAASiF,eAAe,CAACG,MAAM,GAAGC,IAAI,GAAGpQ,KAAK;YACxE,IAAIkQ,mBAAmB,MAAMA;QAC/B;QACA,gEAAgE;QAChE,IAAInF,SAASsF,SAAS,CAACC,MAAM,EAAE;YAC7B,MAAMJ,oBAAoBnF,SAASsF,SAAS,CAACE,IAAI,CAAC,CAACxL,MACjD7M,gBAAgB6M;YAElB,IAAImL,mBAAmB,MAAMA;QAC/B;QAEA,MAAMhS,UAA+B;YACnCsQ;QACF;QACA,oEAAoE;QACpE,IACEpM,UAAUoO,kBAAkB,IAC5BpO,UAAUqO,uBAAuB,IACjCrO,UAAUsO,sBAAsB,EAChC;YACA,MAAMC,iBAAiB5T,mBAAmBqF,WAAWwO,OAAO,CAAC;gBAC3D,IAAIlT,QAAQC,GAAG,CAACkT,wBAAwB,EAAE;oBACxCC,QAAQC,GAAG,CAAC,6CAA6C1O;gBAC3D;YACF;YAEA,IAAIE,WAAWyO,SAAS,EAAE;gBACxBzO,WAAWyO,SAAS,CAACL;YACvB,OAAO;gBACLzS,QAAQ8S,SAAS,GAAGL;YACtB;QACF;QAEA,IAAI5F,SAASkG,aAAa,EAAE;YAC1BzC,SAAS0C,SAAS,GAAGnG,SAASkG,aAAa,CAAChQ,IAAI,CAAC;QACnD;QAEA,uEAAuE;QACvE,MAAMkQ,cAAcC,OAAOrG,SAASsG,cAAc;QAClD/N,IAAIgO,SAAS,CAAC1a,+BAA+Bua;QAC7C3C,SAASvQ,OAAO,KAAK,CAAC;QACtBuQ,SAASvQ,OAAO,CAACrH,8BAA8B,GAAGua;QAElD,yEAAyE;QACzE,YAAY;QACZ,IAAI/O,UAAUmP,WAAW,KAAK,SAASxG,SAASyG,mBAAmB,KAAK,GAAG;YACzEhD,SAASiD,YAAY,GAAG;gBAAEvK,YAAY;gBAAGC,QAAQpJ;YAAU;QAC7D,OAAO;YACL,gEAAgE;YAChEyQ,SAASiD,YAAY,GAAG;gBACtBvK,YACE6D,SAASyG,mBAAmB,IAAIjV,iBAC5B,QACAwO,SAASyG,mBAAmB;gBAClCrK,QACE4D,SAAS2G,eAAe,IAAInV,iBACxBwB,YACAgN,SAAS2G,eAAe;YAChC;QACF;QAEA,qCAAqC;QACrC,IAAIlD,EAAAA,yBAAAA,SAASiD,YAAY,qBAArBjD,uBAAuBtH,UAAU,MAAK,GAAG;YAC3CsH,SAASmD,iBAAiB,GAAG;gBAC3BC,aAAaxP,UAAUyP,uBAAuB;gBAC9CrH,OAAOpI,UAAU0P,iBAAiB;YACpC;QACF;QAEA,IAAI/G,SAASzD,qBAAqB,EAAE;YAClCkH,SAASlH,qBAAqB,GAAGyD,SAASzD,qBAAqB;QACjE;QAEA,OAAO,IAAIxR,aAAa,MAAMS,eAAewU,SAASgH,MAAM,GAAG7T;IACjE,OAAO;QACL,8BAA8B;QAC9B,MAAMoJ,wBACJ/E,WAAW+E,qBAAqB,KAAI4E,kCAAAA,eAAgB5E,qBAAqB;QAE3E,MAAMhB,aAAajN,cAAc6F,YAAYyC,IAAIhC,0BAA0B;QAC3E,MAAMkF,eAAe3N,4BACnB0N,KACAtB,KACAjB,KACAiE,YACAF,cACA7D,WAAWyP,eAAe,EAC1BzP,WAAW0P,YAAY,EACvB1T,cACA4N,0BACA7E;QAGF,IACE5J,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB2E,WAAWkL,YAAY,IACvB,qEAAqE;QACrE,6DAA6D;QAC7D/P,QAAQC,GAAG,CAAC+P,YAAY,KAAK,UAC7BzS,kBAAkB2J,QAClB,CAACzG,oBACD;YACA,MAAMsP,eAAelL,WAAWkL,YAAY;YAC5C7I,IAAI+I,eAAe,CAACC,EAAE,CAAC,OAAO;gBAC5B,IAAI,CAAC/I,aAAaqN,WAAW,IAAI,CAAC9P,UAAU+P,YAAY,EAAE;oBACxD,iEAAiE;oBACjE,MAAM,EAAEnP,QAAQ,EAAE,GAAG,IAAIwK,IAAI5I,IAAIvC,GAAG,IAAI,KAAK;oBAC7CoL,aAAazK,UAAU;gBACzB;YACF;QACF;QAEA,IAAI7E,oBAAoB;YACtB,OAAO+H,gBAAgBtB,KAAKjD;QAC9B,OAAO,IAAInD,cAAc;YACvB,OAAOmG,kCAAkCC,KAAKjD,KAAKkD;QACrD;QAEA,MAAMuN,4BAA4Bva,YAAY2X,IAAI,CAChD7X,cAAc8X,aAAa,EAC3B;YACEC,UAAU,CAAC,mBAAmB,EAAEjQ,UAAU;YAC1C2O,YAAY;gBACV,cAAc3O;YAChB;QACF,GACA4S;QAGF,IAAIC,YAAwB;QAC5B,IAAIlD,yBAAyB;YAC3B,gFAAgF;YAChF,MAAMmD,sBAAsB,MAAM7Z,aAAa;gBAC7CkM;gBACAtB;gBACA6C;gBACAuI;gBACA8D,gBAAgB7N;gBAChBvC;gBACAyC;gBACA6H;gBACA/K;gBACA6M;YACF;YAEA,IAAI+D,qBAAqB;gBACvB,IAAIA,oBAAoB9R,IAAI,KAAK,aAAa;oBAC5C,MAAMgS,qBAAqBxT,yBAAyBC;oBACpDoE,IAAInC,UAAU,GAAG;oBACjBqN,SAASrN,UAAU,GAAG;oBACtB,MAAM4Q,SAAS,MAAMK,0BACnBvN,cACAD,KACAtB,KACA3B,KACA8Q,oBACAH,WACApG,gBACAsC;oBAGF,OAAO,IAAI1Y,aAAaic,QAAQ;wBAAEvD;oBAAS;gBAC7C,OAAO,IAAI+D,oBAAoB9R,IAAI,KAAK,QAAQ;oBAC9C,IAAI8R,oBAAoBG,MAAM,EAAE;wBAC9BH,oBAAoBG,MAAM,CAACC,cAAc,CAACnE;wBAC1C,OAAO+D,oBAAoBG,MAAM;oBACnC,OAAO,IAAIH,oBAAoBD,SAAS,EAAE;wBACxCA,YAAYC,oBAAoBD,SAAS;oBAC3C;gBACF;YACF;QACF;QAEA,MAAMpU,UAA+B;YACnCsQ;QACF;QAEA,MAAMuD,SAAS,MAAMK,0BACnBvN,cACAD,KACAtB,KACA3B,KACAzC,YACAoT,WACApG,gBACAsC;QAGF,IAAIpM,UAAU2N,wBAAwB,EAAE;YACtC,MAAM3N,UAAU2N,wBAAwB;QAC1C;QAEA,oEAAoE;QACpE,IACE3N,UAAUoO,kBAAkB,IAC5BpO,UAAUqO,uBAAuB,IACjCrO,UAAUsO,sBAAsB,EAChC;YACA,MAAMC,iBAAiB5T,mBAAmBqF,WAAWwO,OAAO,CAAC;gBAC3D,IAAIlT,QAAQC,GAAG,CAACkT,wBAAwB,EAAE;oBACxCC,QAAQC,GAAG,CAAC,6CAA6C1O;gBAC3D;YACF;YAEA,IAAIE,WAAWyO,SAAS,EAAE;gBACxBzO,WAAWyO,SAAS,CAACL;YACvB,OAAO;gBACLzS,QAAQ8S,SAAS,GAAGL;YACtB;QACF;QAEA,iDAAiD;QACjD,OAAO,IAAI7a,aAAaic,QAAQ7T;IAClC;AACF;AAcA,OAAO,MAAM0U,uBAAsC,CACjDhO,KACAtB,KACA7D,UACA0C,OACAzC,qBACA6C,YACA4J,0BACA/N,aACA6F;QAaiB1B;IAXjB,IAAI,CAACqC,IAAIvC,GAAG,EAAE;QACZ,MAAM,qBAAwB,CAAxB,IAAIwH,MAAM,gBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAuB;IAC/B;IAEA,MAAMxH,MAAMlH,iBAAiByJ,IAAIvC,GAAG,EAAEtE,WAAW;IAEjD,qEAAqE;IACrE,wEAAwE;IACxE,MAAMiO,uBAAuBhO,oBAAoB4G,IAAI3G,OAAO,EAAE;QAC5DG;QACAM,mBAAmB6D,WAAW8C,YAAY,CAAC3G,iBAAiB,KAAK;QACjEM,aAAa,GAAEuD,2BAAAA,WAAW0P,YAAY,qBAAvB1P,yBAAyBvD,aAAa;IACvD;IAEA,MAAM,EAAEX,iBAAiB,EAAEU,yBAAyB,EAAE,GAAGiN;IAEzD,MAAMC,oBAAoB;QAAE4B,OAAO;IAAM;IACzC,IAAI3B,iBAAwC;IAE5C,4EAA4E;IAC5E,SAAS;IACT,IAAI,OAAO3J,WAAWiH,SAAS,KAAK,UAAU;QAC5C,IAAI9J,qBAAqB;YACvB,MAAM,qBAEL,CAFK,IAAIpD,eACR,6EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA4P,iBAAiBxS,oBACf6I,WAAWiH,SAAS,EACpBjH,WAAW/C,MAAM;IAErB;IAEA,IACE0M,CAAAA,kCAAAA,eAAgB5E,qBAAqB,KACrC/E,WAAW+E,qBAAqB,EAChC;QACA,MAAM,qBAEL,CAFK,IAAIhL,eACR,+FADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAM8F,YAAYjL,gBAAgB;QAChCmI,MAAMiD,WAAWsQ,WAAW,CAACC,UAAU,CAACxT,IAAI;QAC5CI;QACA6C;QACA0J;QACA,8CAA8C;QAC9C5N;QACA6F,SAASD,cAAcC,OAAO;QAC9BnF;IACF;IAEA,OAAOnJ,iBAAiBwP,GAAG,CACzBhD,WACA,sBAAsB;IACtB2J,0BACA,mBAAmB;IACnBnH,KACAtB,KACAjB,KACA5C,UACA0C,OACAI,YACAH,WACA4J,sBACAC,mBACAC,gBACAC,0BACAlI;AAEJ,EAAC;AAED,eAAeoO,eACbxN,YAA0B,EAC1BD,GAAoB,EACpBtB,GAAqB,EACrB3B,GAAqB,EACrBG,IAAgB,EAChBwQ,SAAc,EACdpG,cAAqC,EACrCsC,QAAqC;IAErC,MAAM,EAAErF,WAAW,EAAErK,KAAK,EAAEW,QAAQ,EAAE8C,UAAU,EAAE,GAAGZ;IAErD,MAAM,EACJoR,QAAQ,EACRC,OAAO,EACPC,aAAa,EACbrN,uBAAuB,EACvBO,YAAY,EACZ+M,WAAW,EACXhO,MAAM,KAAK,EACXG,YAAY,EACZ8N,aAAa,KAAK,EAClBnO,6BAA6B,EAC7B1F,IAAI,EACJ8T,qBAAqB,EACrBC,oBAAoB,EACpBC,4BAA4B,EAC5BC,uBAAuB,EACxB,GAAGhR;IAEJkI,8BAA8B7E;IAE9B,MAAM,EAAEiF,0BAA0B,EAAE2I,wBAAwB,EAAE,GAC5Dza;IACF,MAAM0a,4BAA4B5W,6BAA6BiC;IAE/D,MAAM4U,kBAAkBrX,kBACtBxE,YAAY8b,uBAAuB,IACnCtO,aAAauO,mBAAmB;IAGlC,MAAMC,YACJZ,cAAca,aAAa,CACxBC,MAAM,CACL,CAACC,WACCA,SAASC,QAAQ,CAAC,UAAU,CAACD,SAASC,QAAQ,CAAC,eAElD5T,GAAG,CAAC,CAAC2T,WAAc,CAAA;YAClBE,KAAK,GAAG/K,YAAY,OAAO,EAAE6K,WAAW1a,oBACtCqI,KACA,QACC;YACHwS,SAAS,EAAEb,gDAAAA,4BAA8B,CAACU,SAAS;YACnDd;YACAkB,UAAU;YACVtV;QACF,CAAA;IAEJ,MAAM,CAAC8L,gBAAgByJ,gBAAgB,GAAGrb,mBACxCia,eACA,6CAA6C;IAC7C,8EAA8E;IAC9E9J,aACA+J,aACAI,8BACAha,oBAAoBqI,KAAK,OACzB7C,OACAQ;IAGF,MAAMgV,4BAAwD,IAAIhJ;IAClE,MAAMiJ,gBAAgB;IACtB,SAASC,qBAAqBzP,GAAkB;QAC9C,OAAOC,iDAAAA,8BACLD,KACAH,KACAP,mBAAmB1C,KAAK;IAE5B;IACA,MAAM8S,+BAA+Bzc,kCACnCkN,KACAiO,YACAmB,2BACAC,eACAC;IAGF,SAASE,qBAAqB3P,GAAkB;QAC9C,OAAOC,iDAAAA,8BACLD,KACAH,KACAP,mBAAmB1C,KAAK;IAE5B;IAEA,MAAMgT,oBAAoC,EAAE;IAC5C,MAAMC,2BAA2B3c,uBAC/BiN,KACAiO,YACAmB,2BACAK,mBACAJ,eACAG;IAGF,IAAIG,oBAA8C;IAElD,MAAMvD,YAAYhO,IAAIgO,SAAS,CAACwD,IAAI,CAACxR;IACrC,MAAMyR,eAAezR,IAAIyR,YAAY,CAACD,IAAI,CAACxR;IAE3C,IAAI;QACF,IACE,qDAAqD;QACrD4B,OACA,uEAAuE;QACvExH,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,oGAAoG;QACpGF,QAAQC,GAAG,CAAC+P,YAAY,KAAK,UAC7B,yEAAyE;QACzErI,aAAaC,SAAS,EACtB;YACA,wFAAwF;YACxF,MAAMH,aAGF,MAAMhJ,qBAAqBiJ,GAAG,CAChCP,cACAkD,eACAjG,MACAH,KACA2B,IAAInC,UAAU,KAAK;YAErB,MAAM,CAACoE,mBAAmBC,iBAAiB,GAAGC;YAC9CN,WAAWO,WAAW,GAAGF;YAEzB,MAAMmF,oBAAoB,MAAMxO,qBAAqBiJ,GAAG,CACtDP,cACA5I,2BACA;gBACE4I,aAAamQ,cAAc,GAAG;gBAC9B,OAAO7O,aAAaL,sBAAsB,CACxCX,YACAS,wBAAwBG,aAAa,EACrC;oBACEd,SAASwP;oBACTQ,iBAAiB,IACfpQ,aAAamQ,cAAc,KAAK,OAAO,cAAc;oBACvDvX;gBACF;YAEJ,GACA;gBACEoH,aAAamQ,cAAc,GAAG;YAChC;YAGFrP,4BACEJ,mBACAzD,MACAH,KACA2B,IAAInC,UAAU,KAAK,KACnByE,yBACAf;YAGFgQ,oBAAoB,IAAIjZ,kBAAkB+O;QAC5C,OAAO;YACL,wFAAwF;YACxF,MAAMxF,aAAa,MAAMhJ,qBAAqBiJ,GAAG,CAC/CP,cACAkD,eACAjG,MACAH,KACA2B,IAAInC,UAAU,KAAK;YAGrB0T,oBAAoB,IAAIjZ,kBACtBO,qBAAqBiJ,GAAG,CACtBP,cACAsB,aAAaL,sBAAsB,EACnCX,YACAS,wBAAwBG,aAAa,EACrC;gBACEtI;gBACAwH,SAASwP;YACX;QAGN;QAEA,mGAAmG;QACnG,oGAAoG;QACpG,6BAA6B;QAC7B,MAAMvY;QAEN,wEAAwE;QACxE,qBAAqB;QACrB,IAAI,OAAOqG,WAAWiH,SAAS,KAAK,UAAU;YAC5C,IAAI0C,CAAAA,kCAAAA,eAAgBzL,IAAI,MAAKhH,aAAayb,IAAI,EAAE;gBAC9C,mEAAmE;gBACnE,4EAA4E;gBAC5E,yBAAyB;gBACzB,MAAMC,+BAA+Bnb,gCACnC6a,kBAAkBO,GAAG,IACrBtW,OACAwT;gBAGF,OAAOvc,aACLof,8BACAlf;YAEJ,OAAO,IAAIiW,gBAAgB;gBACzB,uEAAuE;gBACvE,MAAM1C,YAAY3P,sBAAsBqS;gBAExC,MAAMmJ,SAAS,AACbxX,QAAQ,oBACRwX,MAAM;gBAER,MAAMC,aAAa,MAAMnZ,qBAAqBiJ,GAAG,CAC/CP,cACAwQ,sBACA,KAAC3K;oBACCC,mBAAmBkK,kBAAkBO,GAAG;oBACxCxK,gBAAgBA;oBAChBhF,yBAAyBA;oBACzBiF,4BAA4BA;oBAC5B/L,OAAOA;oBACPgM,mBAAmB,CAAC,CAACkI;oBAEvBxJ,WACA;oBAAEvE,SAAS2P;oBAA0B9V;gBAAM;gBAG7C,MAAMyW,wBAAwBrc,0BAA0B;oBACtD2a;oBACAL;oBACAgC,sBAAsBb;oBACtB5B;oBACAW,iBAAiBA;gBACnB;gBACA,OAAO,MAAMrd,0BAA0Bif,YAAY;oBACjDG,mBAAmBzb,gCACjB6a,kBAAkBa,OAAO,IACzB5W,OACAwT;oBAEFiD;oBACA9B;gBACF;YACF;QACF;QAEA,mCAAmC;QACnC,MAAM3N,yBAAyB,AAC7BjI,QAAQ,oBACRiI,sBAAsB;QAExB,MAAMwP,aAAa,MAAMnZ,qBAAqBiJ,GAAG,CAC/CP,cACAiB,sCACA,KAAC4E;YACCC,mBAAmBkK,kBAAkBO,GAAG;YACxCxK,gBAAgBA;YAChBhF,yBAAyBA;YACzBiF,4BAA4BA;YAC5BC,mBAAmB,CAAC,CAACkI;YACrBlU,OAAOA;YAET;YACEmG,SAAS2P;YACT9V;YACA6W,WAAW,CAAC1X;gBACVA,QAAQwL,OAAO,CAAC,CAACzJ,OAAOF;oBACtBiV,aAAajV,KAAKE;gBACpB;YACF;YACA4V,kBAAkBxC;YAClByC,kBAAkB;gBAACxB;aAAgB;YACnC/B;QACF;QAGF,MAAMiD,wBAAwBrc,0BAA0B;YACtD2a;YACAL;YACAgC,sBAAsBb;YACtB5B;YACAW,iBAAiBA;QACnB;QACA;;;;;;;;;;;;;;;;KAgBC,GACD,MAAMoC,qBACJvC,4BAA4B,QAAQ,CAAC,CAACF;QAExC,OAAO,MAAMnd,mBAAmBof,YAAY;YAC1CG,mBAAmBzb,gCACjB6a,kBAAkBa,OAAO,IACzB5W,OACAwT;YAEFlO,oBAAoB0R;YACpBC,yBAAyBpU,IAAIS,SAAS,CAAC2T,uBAAuB,KAAK;YACnE7R,SAASvC,IAAIS,SAAS,CAAC8B,OAAO;YAC9BqR;YACA9B;YACAuC,oBAAoB9Q;QACtB;IACF,EAAE,OAAOH,KAAK;QACZ,IACE7K,wBAAwB6K,QACvB,OAAOA,QAAQ,YACdA,QAAQ,QACR,aAAaA,OACb,OAAOA,IAAIqF,OAAO,KAAK,YACvBrF,IAAIqF,OAAO,CAAC3B,QAAQ,CAClB,iEAEJ;YACA,sDAAsD;YACtD,MAAM1D;QACR;QAEA,wEAAwE;QACxE,uBAAuB;QACvB,MAAMkR,qBAAqBtd,oBAAoBoM;QAC/C,IAAIkR,oBAAoB;YACtB,MAAMzL,QAAQrQ,4BAA4B4K;YAC1ClM,MACE,GAAGkM,IAAImR,MAAM,CAAC,mDAAmD,EAAEzW,SAAS,kFAAkF,EAAE+K,OAAO;YAGzK,MAAMzF;QACR;QAEA,IAAIoD;QAEJ,IAAI7Q,0BAA0ByN,MAAM;YAClCzB,IAAInC,UAAU,GAAG9J,4BAA4B0N;YAC7CyJ,SAASrN,UAAU,GAAGmC,IAAInC,UAAU;YACpCgH,YAAY/Q,mCAAmCkM,IAAInC,UAAU;QAC/D,OAAO,IAAI1J,gBAAgBsN,MAAM;YAC/BoD,YAAY;YACZ7E,IAAInC,UAAU,GAAG3J,+BAA+BuN;YAChDyJ,SAASrN,UAAU,GAAGmC,IAAInC,UAAU;YAEpC,MAAMgV,cAAcld,cAAc1B,wBAAwBwN,MAAMgO;YAEhE,gEAAgE;YAChE,YAAY;YACZ,MAAM9U,UAAU,IAAImY;YACpB,IAAItd,qBAAqBmF,SAAS4G,aAAawR,cAAc,GAAG;gBAC9D/E,UAAU,cAAcnR,MAAM2O,IAAI,CAAC7Q,QAAQkS,MAAM;YACnD;YAEAmB,UAAU,YAAY6E;QACxB,OAAO,IAAI,CAACF,oBAAoB;YAC9B3S,IAAInC,UAAU,GAAG;YACjBqN,SAASrN,UAAU,GAAGmC,IAAInC,UAAU;QACtC;QAEA,MAAM,CAACmV,qBAAqBC,qBAAqB,GAAGvd,mBAClDia,eACA9J,aACA+J,aACAI,8BACAha,oBAAoBqI,KAAK,QACzB7C,OACA;QAGF,MAAM0X,kBAAkB,MAAMra,qBAAqBiJ,GAAG,CACpDP,cACA8E,oBACA7H,MACAH,KACA2S,0BAA0BrU,GAAG,CAAC,AAAC8E,IAAYuF,MAAM,IAAI,OAAOvF,KAC5DoD;QAGF,MAAMsO,oBAAoBta,qBAAqBiJ,GAAG,CAChDP,cACAsB,aAAaL,sBAAsB,EACnC0Q,iBACA5Q,wBAAwBG,aAAa,EACrC;YACEtI;YACAwH,SAASwP;QACX;QAGF,IAAII,sBAAsB,MAAM;YAC9B,wFAAwF;YACxF,gCAAgC;YAChC,MAAM9P;QACR;QAEA,IAAI;YACF,MAAM2R,aAAa,MAAMva,qBAAqBiJ,GAAG,CAC/CP,cACA7O,2BACA;gBACE2gB,gBACE9Y,QAAQ;gBACV+Y,uBACE,KAAC9K;oBACCnB,mBAAmB8L;oBACnB5L,4BAA4BA;oBAC5BD,gBAAgB0L;oBAChB1Q,yBAAyBA;oBACzBkF,mBAAmB,CAAC,CAACkI;oBACrBlU,OAAOA;;gBAGX+X,eAAe;oBACb/X;oBACA,wCAAwC;oBACxC+W,kBAAkB;wBAACU;qBAAqB;oBACxCjE;gBACF;YACF;YAGF;;;;;;;;;;;;;;;OAeC,GACD,MAAMwD,qBACJvC,4BAA4B,QAAQ,CAAC,CAACF;YACxC,OAAO,MAAMnd,mBAAmBwgB,YAAY;gBAC1CjB,mBAAmBzb,gCACjB,+DAA+D;gBAC/D,8DAA8D;gBAC9D,SAAS;gBACT6a,kBAAkBa,OAAO,IACzB5W,OACAwT;gBAEFlO,oBAAoB0R;gBACpBC,yBAAyBpU,IAAIS,SAAS,CAAC2T,uBAAuB,KAAK;gBACnE7R,SAASvC,IAAIS,SAAS,CAAC8B,OAAO;gBAC9BqR,uBAAuBrc,0BAA0B;oBAC/C2a;oBACAL;oBACAgC,sBAAsB,EAAE;oBACxBzC;oBACAW,iBAAiBA;gBACnB;gBACAD;gBACAuC,oBAAoB9Q;YACtB;QACF,EAAE,OAAO4R,UAAe;YACtB,IACEpZ,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBtG,0BAA0Bwf,WAC1B;gBACA,MAAM,EAAEC,kBAAkB,EAAE,GAC1BlZ,QAAQ;gBACVkZ;YACF;YACA,MAAMD;QACR;IACF;AACF;AAEA,SAASrR;IACP,IAAIF;IACJ,IAAIyR,SAAS,IAAIC,QAAyB,CAACC;QACzC3R,oBAAoB2R;IACtB;IACA,OAAO;QAAC3R;QAAoByR;KAAO;AACrC;AAEA;;;;;CAKC,GACD,eAAerR,4BACbJ,iBAA+D,EAC/DzD,IAAgB,EAChBH,GAAqB,EACrBwV,UAAmB,EACnBvR,uBAA2E,EAC3Ef,YAA0B;QAuBHA;IArBvB,MAAM,EACJhD,cAAcsE,YAAY,EAC1BxG,0BAA0B,EAC1ByG,YAAY,EACZtH,KAAK,EACLyD,UAAU,EACVH,SAAS,EACV,GAAGT;IAEJ,MAAM,EAAE0E,wBAAwB,KAAK,EAAE2M,OAAO,EAAE,GAAGzQ;IAEnD,iEAAiE;IACjE,yDAAyD;IACzD,MAAMqI,iBAAiB,KAAO;IAC9B,MAAM,EAAEC,0BAA0B,EAAE,GAAG9R;IAEvC,MAAMuN,aAAajN,cACjB8M,aAAarE,IAAI,EACjBnC;IAGF,MAAM4H,kBAAiB1C,4BAAAA,aAAa2C,OAAO,CAACtH,GAAG,CAC7ClJ,kDADqB6N,0BAEpB7E,KAAK;IAER,iEAAiE;IACjE,yEAAyE;IACzE,6EAA6E;IAC7E,8EAA8E;IAC9E,MAAMoX,mCAAmC,IAAI3Q;IAE7C,4EAA4E;IAC5E,gFAAgF;IAChF,6EAA6E;IAC7E,MAAM4Q,gCAAgC,IAAI5Q;IAE1C,kFAAkF;IAClF,yBAAyB;IACzB,MAAME,cAAc,IAAIvK;IAExB,MAAMkb,0BAA0BzhB,MAAM4R,iBAAiB;IACvD,MAAM8P,0BAA0BpR,aAAasB,iBAAiB;IAE9D,iEAAiE;IACjE,8DAA8D;IAC9D,wEAAwE;IACxE,6BAA6B;IAC7B,MAAMlB,2BAA2B7J;IACjC,MAAM8a,8BAA8C;QAClD/W,MAAM;QACNoG,OAAO;QACPP;QACAF;QACAU,cAAcuQ,8BAA8BtQ,MAAM;QAClDC,YAAYoQ;QACZ,0EAA0E;QAC1E,2EAA2E;QAC3E,uBAAuB;QACvBzQ;QACAM,iBAAiB;QACjBZ;QACAa,YAAY3K;QACZ4K,QAAQ5K;QACR6K,OAAO7K;QACP8K,MAAM;eAAIjB,aAAaiB,IAAI;SAAC;QAC5Bd;QACAe,uBAAuB;QACvBC;QACAE,mBAAmB8P;IACrB;IAEA,0FAA0F;IAC1F,wFAAwF;IACxF,MAAME,uBAAuB,MAAMtb,qBAAqBiJ,GAAG,CACzDoS,6BACAzP,eACAjG,MACAH,KACAwV;IAGF,MAAMO,6BAA6Bvb,qBAAqBiJ,GAAG,CACzDoS,6BACArR,aAAawR,SAAS,EACtBF,sBACA7R,wBAAwBG,aAAa,EACrC;QACEtI;QACAwH,SAAS,CAACF;YACR,MAAMuF,SAASnS,2BAA2B4M;YAE1C,IAAIuF,QAAQ;gBACV,OAAOA;YACT;YAEA,IAAInN,uBAAuB4H,MAAM;gBAC/B,kBAAkB;gBAClB+L,QAAQjY,KAAK,CAACkM;gBACd,OAAOhH;YACT;YAEA,IAAIqZ,iCAAiCrQ,MAAM,CAAC6Q,OAAO,EAAE;gBACnD,mEAAmE;gBACnE,iEAAiE;gBACjE;YACF,OAAO,IACLla,QAAQC,GAAG,CAACka,gBAAgB,IAC5Bna,QAAQC,GAAG,CAACma,sBAAsB,EAClC;gBACA9b,0CAA0C+I,KAAK3C,UAAU2V,KAAK;YAChE;QACF;QACA,iFAAiF;QACjF,qCAAqC;QACrCC,YAAYja;QACZ,+EAA+E;QAC/E,iFAAiF;QACjF,iDAAiD;QACjDgJ,QAAQsQ,8BAA8BtQ,MAAM;IAC9C;IAGF,8EAA8E;IAC9E7J,oBAAoByJ;IACpB,MAAMA,YAAYgB,UAAU;IAE5B0P,8BAA8BzP,KAAK;IACnCwP,iCAAiCxP,KAAK;IAEtC,gEAAgE;IAChE,iEAAiE;IACjE,IAAIxF,UAAU2N,wBAAwB,EAAE;QACtCxK,gCACE,KAAC0S;YACCC,IAAI;gBACFpH,QAAQjY,KAAK,CAACuJ,UAAU2N,wBAAwB;YAClD;;QAGJ;IACF;IAEA,IAAIoI;IACJ,IAAI;QACFA,sBAAsB,MAAMtc,iCAC1B6b;IAEJ,EAAE,OAAO3S,KAAK;QACZ,IACEsS,8BAA8BtQ,MAAM,CAAC6Q,OAAO,IAC5CR,iCAAiCrQ,MAAM,CAAC6Q,OAAO,EAC/C;QACA,4EAA4E;QAC9E,OAAO,IACLla,QAAQC,GAAG,CAACka,gBAAgB,IAC5Bna,QAAQC,GAAG,CAACma,sBAAsB,EAClC;YACA,8EAA8E;YAC9E,mFAAmF;YACnF9b,0CAA0C+I,KAAK3C,UAAU2V,KAAK;QAChE;IACF;IAEA,IAAII,qBAAqB;QACvB,MAAMC,gCAAgC,IAAI3R;QAC1C,MAAM4R,mCAAmC,IAAI5R;QAC7C,MAAM6R,8BAA8C;YAClD7X,MAAM;YACNoG,OAAO;YACPP;YACAF;YACAU,cAAcsR,8BAA8BrR,MAAM;YAClDC,YAAYqR;YACZ,sDAAsD;YACtD,qDAAqD;YACrD1R,aAAa;YACbM,iBAAiB;YACjBZ;YACAa,YAAY3K;YACZ4K,QAAQ5K;YACR6K,OAAO7K;YACP8K,MAAM;mBAAIjB,aAAaiB,IAAI;aAAC;YAC5Bd;YACAe,uBAAuB;YACvBC,gBAAgBxJ;YAChB0J,mBAAmB6P;QACrB;QAEA,MAAMK,YAAY,AAChB9Z,QAAQ,oBACR8Z,SAAS;QACX,MAAMY,6BAA6Bpc,qBAAqBiJ,GAAG,CACzDkT,6BACAX,yBACA,KAACjN;YACCC,mBAAmBwN,oBAAoBK,iBAAiB;YACxD5N,gBAAgBA;YAChBhF,yBAAyBA;YACzBiF,4BAA4BA;YAC5BC,mBAAmB,CAAC,CAACkI;YACrBlU,OAAOA;YAET;YACEiI,QAAQqR,8BAA8BrR,MAAM;YAC5C9B,SAAS,CAACF;gBACR,MAAMuF,SAASnS,2BAA2B4M;gBAE1C,IAAIuF,QAAQ;oBACV,OAAOA;gBACT;gBAEA,IAAInN,uBAAuB4H,MAAM;oBAC/B,kBAAkB;oBAClB+L,QAAQjY,KAAK,CAACkM;oBACd,OAAOhH;gBACT;gBAEA,IAAIqa,8BAA8BrR,MAAM,CAAC6Q,OAAO,EAAE;gBAChD,4EAA4E;gBAC9E,OAAO,IACLla,QAAQC,GAAG,CAACka,gBAAgB,IAC5Bna,QAAQC,GAAG,CAACma,sBAAsB,EAClC;oBACA,8EAA8E;oBAC9E,mFAAmF;oBACnF9b,0CAA0C+I,KAAK3C,UAAU2V,KAAK;gBAChE;YACF;QAGF;QAGFQ,2BAA2BE,KAAK,CAAC,CAAC1T;YAChC,IACEsS,8BAA8BtQ,MAAM,CAAC6Q,OAAO,IAC5Crd,4BAA4BwK,MAC5B;YACA,4EAA4E;YAC9E,OAAO,IACLrH,QAAQC,GAAG,CAACka,gBAAgB,IAC5Bna,QAAQC,GAAG,CAACma,sBAAsB,EAClC;gBACA,8EAA8E;gBAC9E,mFAAmF;gBACnF9b,0CAA0C+I,KAAK3C,UAAU2V,KAAK;YAChE;QACF;QAEA,sEAAsE;QACtE,uGAAuG;QACvG7a,oBAAoByJ;QACpB,MAAMA,YAAYgB,UAAU;QAC5ByQ,8BAA8BxQ,KAAK;IACrC;IAEA,MAAM8Q,wBAAwB,IAAIjS;IAClC,MAAMkS,wBAAwBne,2BAC5B,MAAM,yBAAyB;;IAGjC,MAAMoe,4BAA4C;QAChDnY,MAAM;QACNoG,OAAO;QACPP;QACAF;QACAU,cAAc4R,sBAAsB3R,MAAM;QAC1CC,YAAY0R;QACZ,8EAA8E;QAC9E/R,aAAa;QACbM,iBAAiB0R;QACjBtS;QACAa,YAAY3K;QACZ4K,QAAQ5K;QACR6K,OAAO7K;QACP8K,MAAM;eAAIjB,aAAaiB,IAAI;SAAC;QAC5Bd;QACAe,uBAAuB;QACvBC;QACAE,mBAAmB8P;IACrB;IAEA,MAAMsB,yBAAyB,MAAM1c,qBAAqBiJ,GAAG,CAC3DwT,2BACA7Q,eACAjG,MACAH,KACAwV;IAGF,MAAMtC,oBAAoB,MAAMhZ,iCAC9BE,mCACE;QACE,MAAM+c,kBAAkB,MAAM3c,qBAAqBiJ,GAAG,CACpD,qBAAqB;QACrBwT,2BACA,sBAAsB;QACtBzS,aAAawR,SAAS,EACtB,4CAA4C;QAC5CkB,wBACAjT,wBAAwBG,aAAa,EACrC;YACEtI;YACAwH,SAAS,CAACF;gBACR,IACE2T,sBAAsB3R,MAAM,CAAC6Q,OAAO,IACpCrd,4BAA4BwK,MAC5B;oBACA,OAAOA,IAAIuF,MAAM;gBACnB;gBAEA,IAAInN,uBAAuB4H,MAAM;oBAC/B,kBAAkB;oBAClB+L,QAAQjY,KAAK,CAACkM;oBACd,OAAOhH;gBACT;gBAEA,OAAO5F,2BAA2B4M;YACpC;YACAgC,QAAQ2R,sBAAsB3R,MAAM;QACtC;QAEF,OAAO+R;IACT,GACA;QACEJ,sBAAsB9Q,KAAK;IAC7B;IAIJ,MAAMmR,wBAAwBve,2BAC5B,MAAM,wBAAwB;;IAEhC,MAAMwe,wBAAwB,IAAIvS;IAClC,MAAMwS,4BAA4C;QAChDxY,MAAM;QACNoG,OAAO;QACPP;QACAF;QACAU,cAAckS,sBAAsBjS,MAAM;QAC1CC,YAAYgS;QACZ,oFAAoF;QACpFrS,aAAa;QACbM,iBAAiB8R;QACjB1S;QACAa,YAAY3K;QACZ4K,QAAQ5K;QACR6K,OAAO7K;QACP8K,MAAM;eAAIjB,aAAaiB,IAAI;SAAC;QAC5Bd;QACAe,uBAAuB;QACvBC;QACAE,mBAAmB6P;IACrB;IAEA,IAAI4B,oBAAoBze;IAExB,IAAI;QACF,MAAMkd,YAAY,AAChB9Z,QAAQ,oBACR8Z,SAAS;QACX,IAAI,EAAEwB,SAASC,kBAAkB,EAAE,GACjC,MAAMrd,mCACJ,IACEI,qBAAqBiJ,GAAG,CACtB6T,2BACAtB,yBACA,KAACjN;gBACCC,mBAAmBkK,kBAAkB2D,iBAAiB;gBACtD5N,gBAAgBA;gBAChBhF,yBAAyBA;gBACzBiF,4BAA4BA;gBAC5BC,mBAAmB,CAAC,CAACkI;gBACrBlU,OAAOA;gBAET;gBACEiI,QAAQiS,sBAAsBjS,MAAM;gBACpC9B,SAAS,CAACF,KAAcsU;oBACtB,IACE9e,4BAA4BwK,QAC5BiU,sBAAsBjS,MAAM,CAAC6Q,OAAO,EACpC;wBACA,MAAM0B,iBAAiBD,UAAUC,cAAc;wBAC/C,IAAI,OAAOA,mBAAmB,UAAU;4BACtC5e,0BACE0H,WACAkX,gBACAJ,mBACAH;wBAEJ;wBACA;oBACF;oBAEA,IAAI5b,uBAAuB4H,MAAM;wBAC/B,kBAAkB;wBAClB+L,QAAQjY,KAAK,CAACkM;wBACd,OAAOhH;oBACT;oBAEA,OAAO5F,2BAA2B4M;gBACpC;YAGF,IAEJ;YACEiU,sBAAsBpR,KAAK;QAC7B;QAGJ,MAAM,EAAE2R,cAAc,EAAE,GAAG,MAAM5d,eAAeyd;QAChD7T,gCACE,KAAC0S;YACCC,IAAIvd,yBAAyBma,IAAI,CAC/B,MACA1S,WACAmX,iBAAiB3e,aAAa4e,KAAK,GAAG5e,aAAa6e,IAAI,EACvDP,mBACAP;;IAIR,EAAE,OAAOe,aAAa;QACpB,wEAAwE;QACxE,gDAAgD;QAEhD,IAAIC,kBAAkBhf,yBAAyBma,IAAI,CACjD,MACA1S,WACAxH,aAAagf,OAAO,EACpBV,mBACAP;QAGF,IAAIjb,QAAQC,GAAG,CAACka,gBAAgB,IAAIna,QAAQC,GAAG,CAACma,sBAAsB,EAAE;YACtE,8EAA8E;YAC9E,mFAAmF;YACnF,MAAM+B,0BAA0BF;YAChCA,kBAAkB;gBAChB7I,QAAQjY,KAAK,CACX;gBAEFiY,QAAQjY,KAAK,CAAC6gB;gBACdG;YACF;QACF;QAEAtU,gCAAkB,KAAC0S;YAAUC,IAAIyB;;IACnC;AACF;AAEA,eAAe1B,UAAU,EAAEC,EAAE,EAAyB;IACpD,IAAI;QACF,MAAMA;IACR,EAAE,OAAM,CAAC;IACT,OAAO;AACT;AAcA;;CAEC,GACD,SAAS4B,+BAA+B1X,SAAoB;IAC1D,MAAM,EAAEgC,kBAAkB,EAAE,GAAGhC;IAC/B,IAAI,CAACgC,oBAAoB,OAAO;IAEhC,OAAO;AACT;AAEA,eAAeuL,kBACb/K,GAAoB,EACpBtB,GAAqB,EACrB3B,GAAqB,EACrB6M,QAAqC,EACrC1M,IAAgB;IAEhB,kEAAkE;IAClE,yEAAyE;IACzE,6DAA6D;IAC7D,MAAMwQ,YAAY;IAElB,MAAM,EACJnJ,WAAW,EACXxJ,0BAA0B,EAC1ByG,YAAY,EACZtH,KAAK,EACLW,QAAQ,EACR8C,UAAU,EACVH,SAAS,EACV,GAAGT;IAEJ,MAAM,EACJ0E,wBAAwB,KAAK,EAC7B0M,QAAQ,EACRC,OAAO,EACPC,aAAa,EACbrN,uBAAuB,EACvBO,YAAY,EACZ+M,WAAW,EACXhO,MAAM,KAAK,EACXG,YAAY,EACZwK,sBAAsB,EACtBsD,aAAa,KAAK,EAClBnO,6BAA6B,EAC7B1F,IAAI,EACJ8T,qBAAqB,EACrBE,4BAA4B,EAC7B,GAAG/Q;IAEJkI,8BAA8B7E;IAE9B,MAAMU,aAAajN,cAAcyI,MAAMnC;IACvC,MAAMD,sBAAsB0C,UAAU1C,mBAAmB;IAEzD,MAAM,EAAEmL,0BAA0B,EAAE2I,wBAAwB,EAAE,GAC5Dza;IACF,MAAM0a,4BAA4B5W,6BAA6BiC;IAE/D,MAAM4U,kBAAkBrX,kBACtBxE,YAAY8b,uBAAuB,IACnCtO,aAAauO,mBAAmB;IAGlC,MAAMC,YACJZ,cAAca,aAAa,CACxBC,MAAM,CACL,CAACC,WACCA,SAASC,QAAQ,CAAC,UAAU,CAACD,SAASC,QAAQ,CAAC,eAElD5T,GAAG,CAAC,CAAC2T,WAAc,CAAA;YAClBE,KAAK,GAAG/K,YAAY,OAAO,EAAE6K,WAAW1a,oBACtCqI,KACA,QACC;YACHwS,SAAS,EAAEb,gDAAAA,4BAA8B,CAACU,SAAS;YACnDd;YACAkB,UAAU;YACVtV;QACF,CAAA;IAEJ,MAAM,CAAC8L,gBAAgByJ,gBAAgB,GAAGrb,mBACxCia,eACA,6CAA6C;IAC7C,8EAA8E;IAC9E9J,aACA+J,aACAI,8BACAha,oBAAoBqI,KAAK,OACzB7C,OACAQ;IAGF,MAAMgV,4BAAwD,IAAIhJ;IAClE,+EAA+E;IAC/E,MAAMiJ,gBAAgB,CAAC,CAAClP,aAAa3G,iBAAiB;IACtD,SAAS8V,qBAAqBzP,GAAkB;QAC9C,OAAOC,iDAAAA,8BACLD,KACAH,KACAP,mBAAmB1C,KAAK;IAE5B;IACA,MAAM8S,+BAA+Bzc,kCACnCkN,KACAiO,YACAmB,2BACAC,eACAC;IAGF,SAASE,qBAAqB3P,GAAkB;QAC9C,OAAOC,iDAAAA,8BACLD,KACAH,KACAP,mBAAmB1C,KAAK;IAE5B;IACA,MAAMgT,oBAAoC,EAAE;IAC5C,MAAMC,2BAA2B3c,uBAC/BiN,KACAiO,YACAmB,2BACAK,mBACAJ,eACAG;IAGF,IAAIqF,6BAG8B;IAClC,MAAMC,oBAAoB,CAACxY;QACzBgN,SAASvQ,OAAO,KAAK,CAAC;QACtBuQ,SAASvQ,OAAO,CAACuD,KAAK,GAAG8B,IAAIiF,SAAS,CAAC/G;IACzC;IACA,MAAM8P,YAAY,CAAC9P,MAAcxB;QAC/BsD,IAAIgO,SAAS,CAAC9P,MAAMxB;QACpBga,kBAAkBxY;QAClB,OAAO8B;IACT;IACA,MAAMyR,eAAe,CAACvT,MAAcxB;QAClC,IAAIG,MAAMC,OAAO,CAACJ,QAAQ;YACxBA,MAAMyJ,OAAO,CAAC,CAACwQ;gBACb3W,IAAIyR,YAAY,CAACvT,MAAMyY;YACzB;QACF,OAAO;YACL3W,IAAIyR,YAAY,CAACvT,MAAMxB;QACzB;QACAga,kBAAkBxY;IACpB;IAEA,MAAM0Y,kBAAkB,CAAC9S;YAEhB/B;eADP+B,UAAU7K,kBACV,SAAO8I,2BAAAA,aAAa8U,UAAU,qBAAvB9U,yBAAyB+U,MAAM,MAAK,WACvC/U,aAAa8U,UAAU,CAACC,MAAM,GAC9BhT;;IAEN,IAAIR,iBAAwC;IAE5C,IAAI;QACF,IAAIvB,aAAaC,SAAS,EAAE;YAC1B;;;;;;;;;;;;OAYC,GAED,iEAAiE;YACjE,yEAAyE;YACzE,6EAA6E;YAC7E,8EAA8E;YAC9E,MAAM8R,mCAAmC,IAAI3Q;YAE7C,4EAA4E;YAC5E,gFAAgF;YAChF,6EAA6E;YAC7E,MAAM4Q,gCAAgC,IAAI5Q;YAE1C,kFAAkF;YAClF,yBAAyB;YACzB,MAAME,cAAc,IAAIvK;YAExB,IAAIie;YACJ,IAAI/S,wBAAsD;YAC1D,IAAIf,2BAA4D;YAEhE,IAAIhE,WAAW+E,qBAAqB,EAAE;gBACpC,sEAAsE;gBACtE,wEAAwE;gBACxE,uEAAuE;gBACvE,cAAc;gBACd+S,kBAAkB/S,wBAChB/E,WAAW+E,qBAAqB;YACpC,OAAO;gBACL,iEAAiE;gBACjE+S,kBAAkB9T,2BAChB7J;YACJ;YAEA,MAAM8a,8BAA+C5Q,iBAAiB;gBACpEnG,MAAM;gBACNoG,OAAO;gBACPP;gBACAF;gBACAU,cAAcuQ,8BAA8BtQ,MAAM;gBAClDC,YAAYoQ;gBACZ,0EAA0E;gBAC1E,2EAA2E;gBAC3E,uBAAuB;gBACvBzQ;gBACAM,iBAAiB;gBACjBZ;gBACAa,YAAY3K;gBACZ4K,QAAQ5K;gBACR6K,OAAO7K;gBACP8K,MAAM;uBAAIjB,aAAaiB,IAAI;iBAAC;gBAC5Bd;gBACAe;gBACAC,gBAAgBxJ;gBAChB0J,mBAAmB1J;YACrB;YAEA,0FAA0F;YAC1F,wFAAwF;YACxF,MAAM0Z,uBAAuB,MAAMtb,qBAAqBiJ,GAAG,CACzDoS,6BACAzP,eACAjG,MACAH,KACA2B,IAAInC,UAAU,KAAK;YAGrB,MAAMuW,6BAA6Bvb,qBAAqBiJ,GAAG,CACzDoS,6BACArR,aAAawR,SAAS,EACtBF,sBACA7R,wBAAwBG,aAAa,EACrC;gBACEtI;gBACAwH,SAAS,CAACF;oBACR,MAAMuF,SAASnS,2BAA2B4M;oBAE1C,IAAIuF,QAAQ;wBACV,OAAOA;oBACT;oBAEA,IAAInN,uBAAuB4H,MAAM;wBAC/B,kBAAkB;wBAClB+L,QAAQjY,KAAK,CAACkM;wBACd,OAAOhH;oBACT;oBAEA,IAAIqZ,iCAAiCrQ,MAAM,CAAC6Q,OAAO,EAAE;wBACnD,mEAAmE;wBACnE,iEAAiE;wBACjE;oBACF,OAAO,IACLla,QAAQC,GAAG,CAACka,gBAAgB,IAC5Bna,QAAQC,GAAG,CAACma,sBAAsB,EAClC;wBACA9b,0CAA0C+I,KAAK3C,UAAU2V,KAAK;oBAChE;gBACF;gBACA,iFAAiF;gBACjF,qCAAqC;gBACrCC,YAAYja;gBACZ,+EAA+E;gBAC/E,iFAAiF;gBACjF,iDAAiD;gBACjDgJ,QAAQsQ,8BAA8BtQ,MAAM;YAC9C;YAGF,8EAA8E;YAC9E7J,oBAAoByJ;YACpB,MAAMA,YAAYgB,UAAU;YAE5B0P,8BAA8BzP,KAAK;YACnCwP,iCAAiCxP,KAAK;YAEtC,gEAAgE;YAChE,iEAAiE;YACjE,IAAIxF,UAAU2N,wBAAwB,EAAE;gBACtC,MAAM3N,UAAU2N,wBAAwB;YAC1C;YAEA,IAAIoI;YACJ,IAAI;gBACFA,sBAAsB,MAAMtc,iCAC1B6b;YAEJ,EAAE,OAAO3S,KAAK;gBACZ,IACEsS,8BAA8BtQ,MAAM,CAAC6Q,OAAO,IAC5CR,iCAAiCrQ,MAAM,CAAC6Q,OAAO,EAC/C;gBACA,4EAA4E;gBAC9E,OAAO,IACLla,QAAQC,GAAG,CAACka,gBAAgB,IAC5Bna,QAAQC,GAAG,CAACma,sBAAsB,EAClC;oBACA,8EAA8E;oBAC9E,mFAAmF;oBACnF9b,0CAA0C+I,KAAK3C,UAAU2V,KAAK;gBAChE;YACF;YAEA,IAAII,qBAAqB;gBACvB,MAAMC,gCAAgC,IAAI3R;gBAC1C,MAAM4R,mCAAmC,IAAI5R;gBAC7C,MAAM6R,8BAA8C;oBAClD7X,MAAM;oBACNoG,OAAO;oBACPP;oBACAF;oBACAU,cAAcsR,8BAA8BrR,MAAM;oBAClDC,YAAYqR;oBACZ,sDAAsD;oBACtD,qDAAqD;oBACrD1R,aAAa;oBACbM,iBAAiB;oBACjBZ;oBACAa,YAAY3K;oBACZ4K,QAAQ5K;oBACR6K,OAAO7K;oBACP8K,MAAM;2BAAIjB,aAAaiB,IAAI;qBAAC;oBAC5Bd;oBACAe;oBACAC,gBAAgBxJ;oBAChB0J,mBAAmB1J;gBACrB;gBAEA,MAAM4Z,YAAY,AAChB9Z,QAAQ,oBACR8Z,SAAS;gBACX,MAAMY,6BAA6Bpc,qBAAqBiJ,GAAG,CACzDkT,6BACAX,yBACA,KAACjN;oBACCC,mBAAmBwN,oBAAoBK,iBAAiB;oBACxD5N,gBAAgBA;oBAChBhF,yBAAyBA;oBACzBiF,4BAA4BA;oBAC5BC,mBAAmB,CAAC,CAACkI;oBACrBlU,OAAOA;oBAET;oBACEiI,QAAQqR,8BAA8BrR,MAAM;oBAC5C9B,SAAS,CAACF;wBACR,MAAMuF,SAASnS,2BAA2B4M;wBAE1C,IAAIuF,QAAQ;4BACV,OAAOA;wBACT;wBAEA,IAAInN,uBAAuB4H,MAAM;4BAC/B,kBAAkB;4BAClB+L,QAAQjY,KAAK,CAACkM;4BACd,OAAOhH;wBACT;wBAEA,IAAIqa,8BAA8BrR,MAAM,CAAC6Q,OAAO,EAAE;wBAChD,4EAA4E;wBAC9E,OAAO,IACLla,QAAQC,GAAG,CAACka,gBAAgB,IAC5Bna,QAAQC,GAAG,CAACma,sBAAsB,EAClC;4BACA,8EAA8E;4BAC9E,mFAAmF;4BACnF9b,0CAA0C+I,KAAK3C,UAAU2V,KAAK;wBAChE;oBACF;oBACAlC,kBAAkB;wBAACxB;qBAAgB;gBACrC;gBAGFkE,2BAA2BE,KAAK,CAAC,CAAC1T;oBAChC,IACEsS,8BAA8BtQ,MAAM,CAAC6Q,OAAO,IAC5Crd,4BAA4BwK,MAC5B;oBACA,4EAA4E;oBAC9E,OAAO,IACLrH,QAAQC,GAAG,CAACka,gBAAgB,IAC5Bna,QAAQC,GAAG,CAACma,sBAAsB,EAClC;wBACA,8EAA8E;wBAC9E,mFAAmF;wBACnF9b,0CAA0C+I,KAAK3C,UAAU2V,KAAK;oBAChE;gBACF;gBAEA,sEAAsE;gBACtE,uGAAuG;gBACvG7a,oBAAoByJ;gBACpB,MAAMA,YAAYgB,UAAU;gBAC5ByQ,8BAA8BxQ,KAAK;YACrC;YAEA,IAAI0S,kBAAkB;YACtB,MAAM5B,wBAAwB,IAAIjS;YAClC,MAAMkS,wBAAwBne,2BAC5BqV;YAGF,MAAM+I,4BAA6ChS,iBAAiB;gBAClEnG,MAAM;gBACNoG,OAAO;gBACPP;gBACAF;gBACAU,cAAc4R,sBAAsB3R,MAAM;gBAC1CC,YAAY0R;gBACZ,8EAA8E;gBAC9E/R,aAAa;gBACbM,iBAAiB0R;gBACjBtS;gBACAa,YAAY3K;gBACZ4K,QAAQ5K;gBACR6K,OAAO7K;gBACP8K,MAAM;uBAAIjB,aAAaiB,IAAI;iBAAC;gBAC5Bd;gBACAe;gBACAC,gBAAgBxJ;gBAChB0J,mBAAmB1J;YACrB;YAEA,MAAM8a,yBAAyB,MAAM1c,qBAAqBiJ,GAAG,CAC3DwT,2BACA7Q,eACAjG,MACAH,KACA2B,IAAInC,UAAU,KAAK;YAErB,IAAIoZ,qBAAqB;YACzB,MAAM1F,oBAAqBkF,6BACzB,MAAMle,iCACJE,mCACE;gBACE,MAAM+c,kBAAkB,MAAM3c,qBAAqBiJ,GAAG,CACpD,qBAAqB;gBACrBwT,2BACA,sBAAsB;gBACtBzS,aAAawR,SAAS,EACtB,4CAA4C;gBAC5CkB,wBACAjT,wBAAwBG,aAAa,EACrC;oBACEtI;oBACAwH,SAAS,CAACF;wBACR,OAAO0P,6BAA6B1P;oBACtC;oBACAgC,QAAQ2R,sBAAsB3R,MAAM;gBACtC;gBAEFwT,qBAAqB;gBACrB,OAAOzB;YACT,GACA;gBACE,IAAIJ,sBAAsB3R,MAAM,CAAC6Q,OAAO,EAAE;oBACxC,4EAA4E;oBAC5E,6EAA6E;oBAC7E0C,kBAAkB;oBAClB;gBACF;gBAEA,IAAIC,oBAAoB;oBACtB,kFAAkF;oBAClF,iCAAiC;oBACjCD,kBAAkB;gBACpB;gBACA5B,sBAAsB9Q,KAAK;YAC7B;YAIN,MAAMmR,wBAAwBve,2BAC5BqV;YAEF,MAAMmJ,wBAAwB,IAAIvS;YAClC,MAAMwS,4BAA4C;gBAChDxY,MAAM;gBACNoG,OAAO;gBACPP;gBACAF;gBACAU,cAAckS,sBAAsBjS,MAAM;gBAC1CC,YAAYgS;gBACZ,oFAAoF;gBACpFrS,aAAa;gBACbM,iBAAiB8R;gBACjB1S;gBACAa,YAAY3K;gBACZ4K,QAAQ5K;gBACR6K,OAAO7K;gBACP8K,MAAM;uBAAIjB,aAAaiB,IAAI;iBAAC;gBAC5Bd;gBACAe;gBACAC,gBAAgBxJ;gBAChB0J,mBAAmB1J;YACrB;YAEA,IAAIyc,kBAAkB;YACtB,IAAItB,oBAAoBze;YAExB,MAAMkd,YAAY,AAChB9Z,QAAQ,oBACR8Z,SAAS;YACX,IAAI,EAAEwB,SAASC,kBAAkB,EAAE5P,SAAS,EAAE,GAC5C,MAAMzN,mCACJ,IACEI,qBAAqBiJ,GAAG,CACtB6T,2BACAtB,yBACA,KAACjN;oBACCC,mBAAmBkK,kBAAkB2D,iBAAiB;oBACtD5N,gBAAgBA;oBAChBhF,yBAAyBA;oBACzBiF,4BAA4BA;oBAC5BC,mBAAmB,CAAC,CAACkI;oBACrBlU,OAAOA;oBAET;oBACEiI,QAAQiS,sBAAsBjS,MAAM;oBACpC9B,SAAS,CAACF,KAAcsU;wBACtB,IACE9e,4BAA4BwK,QAC5BiU,sBAAsBjS,MAAM,CAAC6Q,OAAO,EACpC;4BACA4C,kBAAkB;4BAElB,MAAMlB,iBAAqC,AACzCD,UACAC,cAAc;4BAChB,IAAI,OAAOA,mBAAmB,UAAU;gCACtC5e,0BACE0H,WACAkX,gBACAJ,mBACAH;4BAEJ;4BACA;wBACF;wBAEA,OAAOnE,yBAAyB7P,KAAKsU;oBACvC;oBACA1D,WAAW,CAAC1X;wBACVA,QAAQwL,OAAO,CAAC,CAACzJ,OAAOF;4BACtBiV,aAAajV,KAAKE;wBACpB;oBACF;oBACA4V,kBAAkBxC;oBAClByC,kBAAkB;wBAACxB;qBAAgB;gBACrC,IAEJ;gBACE2E,sBAAsBpR,KAAK;YAC7B;YAGJ,MAAM,EAAEuR,OAAO,EAAEI,cAAc,EAAE,GAC/B,MAAM5d,eAAeyd;YAEvB,0EAA0E;YAC1E,2EAA2E;YAC3E,kCAAkC;YAClC,IAAI,CAAC/S,uBAAuB;gBAC1B1L,yBACEyH,WACAmX,iBAAiB3e,aAAa4e,KAAK,GAAG5e,aAAa6e,IAAI,EACvDP,mBACAP;YAEJ;YAEA,MAAMpD,wBAAwBrc,0BAA0B;gBACtD2a;gBACAL;gBACAgC,sBAAsBb;gBACtB5B;gBACAW,iBAAiBA;YACnB;YAEA,MAAM9R,aAAa,MAAMtL,eAAeue,kBAAkB4F,QAAQ;YAClEjM,SAAS5M,UAAU,GAAGA;YACtB4M,SAASkM,WAAW,GAAG,MAAMC,mBAC3B/Y,YACAgX,2BACAzS,cACA5D,YACA7C;YAGF,IAAI4a,mBAAmBE,iBAAiB;gBACtC,IAAIhR,aAAa,MAAM;oBACrB,oBAAoB;oBACpBgF,SAAShF,SAAS,GAAG,MAAM5P,6BACzB4P,WACA9J,qBACA2a;gBAEJ,OAAO;oBACL,oBAAoB;oBACpB7L,SAAShF,SAAS,GAChB,MAAM7P,6BAA6B0gB;gBACvC;gBACAxF,kBAAkBa,OAAO;gBACzB,OAAO;oBACL1F,iBAAiBsE;oBACjBjE,WAAWsE;oBACX5C,QAAQ,MAAM5b,yBAAyBgjB,SAAS;wBAC9C5D;wBACA9B;oBACF;oBACA7D,eAAe/U,qBACb8d,uBACAI;oBAEF,0CAA0C;oBAC1CvH,qBAAqBoH,0BAA0B1R,UAAU;oBACzDwK,iBAAiBkH,0BAA0BzR,MAAM;oBACjDkK,gBAAgB6I,gBAAgBtB,0BAA0BxR,KAAK;oBAC/D6J,eAAe2H,0BAA0BvR,IAAI;oBAC7CC,uBAAuB3K,4BAA4B0d;gBACrD;YACF,OAAO;gBACL,cAAc;gBACd,IAAIjY,UAAU+P,YAAY,EAAE;oBAC1B,MAAM,qBAEL,CAFK,IAAIlY,sBACR,qHADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,IAAIqb,aAAa6D;gBACjB,IAAI3P,aAAa,MAAM;oBACrB,+FAA+F;oBAC/F,qGAAqG;oBACrG,MAAM6L,SAAS,AACbxX,QAAQ,oBACRwX,MAAM;oBAER,qEAAqE;oBACrE,4EAA4E;oBAC5E,MAAMuF,gBAAgB,IAAIC;oBAE1B,MAAMC,eAAe,MAAMzF,qBACzB,KAAC3K;wBACCC,mBAAmBiQ;wBACnBhQ,gBAAgB,KAAO;wBACvBhF,yBAAyBA;wBACzBiF,4BAA4BA;wBAC5BC,mBAAmB,CAAC,CAACkI;wBACrBlU,OAAOA;wBAETic,KAAKC,KAAK,CAACD,KAAKE,SAAS,CAACzR,aAC1B;wBACEzC,QAAQ1M,2BAA2B;wBACnC4K,SAAS2P;wBACT9V;oBACF;oBAGF,wGAAwG;oBACxGwW,aAAavf,aAAaojB,SAAS2B;gBACrC;gBAEA,OAAO;oBACL9K,iBAAiBsE;oBACjBjE,WAAWsE;oBACX5C,QAAQ,MAAM3b,wBAAwBkf,YAAY;wBAChDG,mBAAmBzb,gCACjB6a,kBAAkBqG,eAAe,IACjCpc,OACAwT;wBAEFiD;wBACA9B;wBACAsC,yBACEpU,IAAIS,SAAS,CAAC2T,uBAAuB,KAAK;wBAC5C7R,SAASvC,IAAIS,SAAS,CAAC8B,OAAO;oBAChC;oBACA0L,eAAe/U,qBACb8d,uBACAI;oBAEF,0CAA0C;oBAC1CvH,qBAAqBoH,0BAA0B1R,UAAU;oBACzDwK,iBAAiBkH,0BAA0BzR,MAAM;oBACjDkK,gBAAgB6I,gBAAgBtB,0BAA0BxR,KAAK;oBAC/D6J,eAAe2H,0BAA0BvR,IAAI;oBAC7CC,uBAAuB3K,4BAA4B0d;gBACrD;YACF;QACF,OAAO,IAAIhV,aAAa3G,iBAAiB,EAAE;YACzC,uEAAuE;YACvE,IAAIuI,kBAAkBzM,2BAA2BqV;YAEjD,MAAMtJ,2BAA2B7J;YACjC,MAAMye,4BAA6CvU,iBAAiB;gBAClEnG,MAAM;gBACNoG,OAAO;gBACPP;gBACAF;gBACAa;gBACAC,YAAY3K;gBACZ4K,QAAQ5K;gBACR6K,OAAO7K;gBACP8K,MAAM;uBAAIjB,aAAaiB,IAAI;iBAAC;gBAC5Bd;YACF;YACA,MAAMpB,aAAa,MAAMhJ,qBAAqBiJ,GAAG,CAC/C+V,2BACApT,eACAjG,MACAH,KACA2B,IAAInC,UAAU,KAAK;YAErB,MAAM0T,oBAAqBkF,6BACzB,MAAMje,2CACJK,qBAAqBiJ,GAAG,CACtB+V,2BACAhV,aAAaL,sBAAsB,EACnC,4CAA4C;YAC5CX,YACAS,wBAAwBG,aAAa,EACrC;gBACEtI;gBACAwH,SAASwP;YACX;YAIN,MAAM2G,oBAAoC;gBACxC3a,MAAM;gBACNoG,OAAO;gBACPP;gBACAF;gBACAa;gBACAC,YAAY3K;gBACZ4K,QAAQ5K;gBACR6K,OAAO7K;gBACP8K,MAAM;uBAAIjB,aAAaiB,IAAI;iBAAC;gBAC5Bd;YACF;YACA,MAAMoR,YAAY,AAChB9Z,QAAQ,oBACR8Z,SAAS;YACX,MAAM,EAAEwB,OAAO,EAAE3P,SAAS,EAAE,GAAG,MAAMrN,qBAAqBiJ,GAAG,CAC3DgW,mBACAzD,yBACA,KAACjN;gBACCC,mBAAmBkK,kBAAkB2D,iBAAiB;gBACtD5N,gBAAgBA;gBAChBhF,yBAAyBA;gBACzBiF,4BAA4BA;gBAC5BC,mBAAmB,CAAC,CAACkI;gBACrBlU,OAAOA;gBAET;gBACEmG,SAAS2P;gBACTe,WAAW,CAAC1X;oBACVA,QAAQwL,OAAO,CAAC,CAACzJ,OAAOF;wBACtBiV,aAAajV,KAAKE;oBACpB;gBACF;gBACA4V,kBAAkBxC;gBAClByC,kBAAkB;oBAACxB;iBAAgB;YACrC;YAEF,MAAMkB,wBAAwBrc,0BAA0B;gBACtD2a;gBACAL;gBACAgC,sBAAsBb;gBACtB5B;gBACAW,iBAAiBA;YACnB;YAEA,+FAA+F;YAC/F,8FAA8F;YAC9F,6EAA6E;YAC7E,MAAM9R,aAAa,MAAMtL,eAAeue,kBAAkB4F,QAAQ;YAElE,IAAIX,+BAA+B1X,YAAY;gBAC7CoM,SAAS5M,UAAU,GAAGA;gBACtB4M,SAASkM,WAAW,GAAG,MAAMC,mBAC3B/Y,YACAwZ,mBACAjV,cACA5D,YACA7C;YAEJ;YAEA;;;;;;;;;;;;;OAaC,GACD,oEAAoE;YACpE,IAAItF,oBAAoB6M,gBAAgBoU,eAAe,GAAG;gBACxD,IAAI7R,aAAa,MAAM;oBACrB,qBAAqB;oBACrBgF,SAAShF,SAAS,GAAG,MAAM5P,6BACzB4P,WACA9J,qBACA6G;gBAEJ,OAAO;oBACL,qBAAqB;oBACrBiI,SAAShF,SAAS,GAAG,MAAM7P,6BACzB4M;gBAEJ;gBACA,mGAAmG;gBACnG,8GAA8G;gBAC9G,uHAAuH;gBACvH,sDAAsD;gBACtDsO,kBAAkBa,OAAO;gBACzB,OAAO;oBACL1F,iBAAiBsE;oBACjBjE,WAAWsE;oBACX5C,QAAQ,MAAM5b,yBAAyBgjB,SAAS;wBAC9C5D;wBACA9B;oBACF;oBACA7D,eAAe3I,gBAAgBoU,eAAe;oBAC9C,0CAA0C;oBAC1C7J,qBAAqB2J,0BAA0BjU,UAAU;oBACzDwK,iBAAiByJ,0BAA0BhU,MAAM;oBACjDkK,gBAAgB6I,gBAAgBiB,0BAA0B/T,KAAK;oBAC/D6J,eAAekK,0BAA0B9T,IAAI;gBAC/C;YACF,OAAO,IAAI3H,uBAAuBA,oBAAoBuQ,IAAI,GAAG,GAAG;gBAC9D,+BAA+B;gBAC/BzB,SAAShF,SAAS,GAAG,MAAM7P,6BACzB4M;gBAGF,OAAO;oBACLyJ,iBAAiBsE;oBACjBjE,WAAWsE;oBACX5C,QAAQ,MAAM5b,yBAAyBgjB,SAAS;wBAC9C5D;wBACA9B;oBACF;oBACA7D,eAAe3I,gBAAgBoU,eAAe;oBAC9C,0CAA0C;oBAC1C7J,qBAAqB2J,0BAA0BjU,UAAU;oBACzDwK,iBAAiByJ,0BAA0BhU,MAAM;oBACjDkK,gBAAgB6I,gBAAgBiB,0BAA0B/T,KAAK;oBAC/D6J,eAAekK,0BAA0B9T,IAAI;gBAC/C;YACF,OAAO;gBACL,cAAc;gBACd,8GAA8G;gBAC9G,IAAIjF,UAAU+P,YAAY,EAAE;oBAC1B,MAAM,qBAEL,CAFK,IAAIlY,sBACR,qHADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,IAAIqb,aAAa6D;gBACjB,IAAI3P,aAAa,MAAM;oBACrB,+FAA+F;oBAC/F,qGAAqG;oBACrG,MAAM6L,SAAS,AACbxX,QAAQ,oBACRwX,MAAM;oBAER,qEAAqE;oBACrE,4EAA4E;oBAC5E,MAAMuF,gBAAgB,IAAIC;oBAE1B,MAAMC,eAAe,MAAMzF,qBACzB,KAAC3K;wBACCC,mBAAmBiQ;wBACnBhQ,gBAAgB,KAAO;wBACvBhF,yBAAyBA;wBACzBiF,4BAA4BA;wBAC5BC,mBAAmB,CAAC,CAACkI;wBACrBlU,OAAOA;wBAETic,KAAKC,KAAK,CAACD,KAAKE,SAAS,CAACzR,aAC1B;wBACEzC,QAAQ1M,2BAA2B;wBACnC4K,SAAS2P;wBACT9V;oBACF;oBAGF,wGAAwG;oBACxGwW,aAAavf,aAAaojB,SAAS2B;gBACrC;gBAEA,OAAO;oBACL9K,iBAAiBsE;oBACjBjE,WAAWsE;oBACX5C,QAAQ,MAAM3b,wBAAwBkf,YAAY;wBAChDG,mBAAmBzb,gCACjB6a,kBAAkBqG,eAAe,IACjCpc,OACAwT;wBAEFiD;wBACA9B;wBACAsC,yBACEpU,IAAIS,SAAS,CAAC2T,uBAAuB,KAAK;wBAC5C7R,SAASvC,IAAIS,SAAS,CAAC8B,OAAO;oBAChC;oBACA0L,eAAe3I,gBAAgBoU,eAAe;oBAC9C,0CAA0C;oBAC1C7J,qBAAqB2J,0BAA0BjU,UAAU;oBACzDwK,iBAAiByJ,0BAA0BhU,MAAM;oBACjDkK,gBAAgB6I,gBAAgBiB,0BAA0B/T,KAAK;oBAC/D6J,eAAekK,0BAA0B9T,IAAI;gBAC/C;YACF;QACF,OAAO;YACL,MAAMiU,uBAAwC1U,iBAAiB;gBAC7DnG,MAAM;gBACNoG,OAAO;gBACPP;gBACAF;gBACAc,YAAY3K;gBACZ4K,QAAQ5K;gBACR6K,OAAO7K;gBACP8K,MAAM;uBAAIjB,aAAaiB,IAAI;iBAAC;YAC9B;YACA,uFAAuF;YACvF,yEAAyE;YACzE,MAAMlC,aAAa,MAAMhJ,qBAAqBiJ,GAAG,CAC/CkW,sBACAvT,eACAjG,MACAH,KACA2B,IAAInC,UAAU,KAAK;YAGrB,MAAM0T,oBAAqBkF,6BACzB,MAAMje,2CACJK,qBAAqBiJ,GAAG,CACtBkW,sBACAnV,aAAaL,sBAAsB,EACnCX,YACAS,wBAAwBG,aAAa,EACrC;gBACEtI;gBACAwH,SAASwP;YACX;YAIN,MAAM3O,yBAAyB,AAC7BjI,QAAQ,oBACRiI,sBAAsB;YACxB,MAAMwP,aAAa,MAAMnZ,qBAAqBiJ,GAAG,CAC/CkW,sBACAxV,sCACA,KAAC4E;gBACCC,mBAAmBkK,kBAAkB2D,iBAAiB;gBACtD5N,gBAAgBA;gBAChBhF,yBAAyBA;gBACzBiF,4BAA4BA;gBAC5BC,mBAAmB,CAAC,CAACkI;gBACrBlU,OAAOA;gBAET;gBACEmG,SAAS2P;gBACT9V;gBACA+W,kBAAkB;oBAACxB;iBAAgB;YACrC;YAGF,IAAIyF,+BAA+B1X,YAAY;gBAC7C,MAAMR,aAAa,MAAMtL,eAAeue,kBAAkB4F,QAAQ;gBAClEjM,SAAS5M,UAAU,GAAGA;gBACtB4M,SAASkM,WAAW,GAAG,MAAMC,mBAC3B/Y,YACA0Z,sBACAnV,cACA5D,YACA7C;YAEJ;YAEA,MAAM6V,wBAAwBrc,0BAA0B;gBACtD2a;gBACAL;gBACAgC,sBAAsBb;gBACtB5B;gBACAW,iBAAiBA;YACnB;YACA,OAAO;gBACL1D,iBAAiBsE;gBACjBjE,WAAWsE;gBACX5C,QAAQ,MAAM7b,mBAAmBof,YAAY;oBAC3CG,mBAAmBzb,gCACjB6a,kBAAkBqG,eAAe,IACjCpc,OACAwT;oBAEFlO,oBAAoB;oBACpB2R,yBACEpU,IAAIS,SAAS,CAAC2T,uBAAuB,KAAK;oBAC5C7R,SAASvC,IAAIS,SAAS,CAAC8B,OAAO;oBAC9BqR;oBACA9B;gBACF;gBACA,0CAA0C;gBAC1CjC,qBAAqB8J,qBAAqBpU,UAAU;gBACpDwK,iBAAiB4J,qBAAqBnU,MAAM;gBAC5CkK,gBAAgB6I,gBAAgBoB,qBAAqBlU,KAAK;gBAC1D6J,eAAeqK,qBAAqBjU,IAAI;YAC1C;QACF;IACF,EAAE,OAAOtC,KAAK;QACZ,IACE7K,wBAAwB6K,QACvB,OAAOA,QAAQ,YACdA,QAAQ,QACR,aAAaA,OACb,OAAOA,IAAIqF,OAAO,KAAK,YACvBrF,IAAIqF,OAAO,CAAC3B,QAAQ,CAClB,iEAEJ;YACA,sDAAsD;YACtD,MAAM1D;QACR;QAEA,uEAAuE;QACvE,mEAAmE;QACnE,IAAIjL,qBAAqBiL,MAAM;YAC7B,MAAMA;QACR;QAEA,wEAAwE;QACxE,uBAAuB;QACvB,MAAMkR,qBAAqBtd,oBAAoBoM;QAC/C,IAAIkR,oBAAoB;YACtB,MAAMzL,QAAQrQ,4BAA4B4K;YAC1ClM,MACE,GAAGkM,IAAImR,MAAM,CAAC,mDAAmD,EAAEzW,SAAS,kFAAkF,EAAE+K,OAAO;YAGzK,MAAMzF;QACR;QAEA,yEAAyE;QACzE,mDAAmD;QACnD,IAAIgV,+BAA+B,MAAM;YACvC,MAAMhV;QACR;QAEA,IAAIoD;QAEJ,IAAI7Q,0BAA0ByN,MAAM;YAClCzB,IAAInC,UAAU,GAAG9J,4BAA4B0N;YAC7CyJ,SAASrN,UAAU,GAAGmC,IAAInC,UAAU;YACpCgH,YAAY/Q,mCAAmCkM,IAAInC,UAAU;QAC/D,OAAO,IAAI1J,gBAAgBsN,MAAM;YAC/BoD,YAAY;YACZ7E,IAAInC,UAAU,GAAG3J,+BAA+BuN;YAChDyJ,SAASrN,UAAU,GAAGmC,IAAInC,UAAU;YAEpC,MAAMgV,cAAcld,cAAc1B,wBAAwBwN,MAAMgO;YAEhEzB,UAAU,YAAY6E;QACxB,OAAO,IAAI,CAACF,oBAAoB;YAC9B3S,IAAInC,UAAU,GAAG;YACjBqN,SAASrN,UAAU,GAAGmC,IAAInC,UAAU;QACtC;QAEA,MAAM,CAACmV,qBAAqBC,qBAAqB,GAAGvd,mBAClDia,eACA9J,aACA+J,aACAI,8BACAha,oBAAoBqI,KAAK,QACzB7C,OACA;QAGF,MAAMwc,uBAAwC1U,iBAAiB;YAC7DnG,MAAM;YACNoG,OAAO;YACPP;YACAF,cAAcA;YACdc,YACE,QAAON,kCAAAA,eAAgBM,UAAU,MAAK,cAClCN,eAAeM,UAAU,GACzB3K;YACN4K,QACE,QAAOP,kCAAAA,eAAgBO,MAAM,MAAK,cAC9BP,eAAeO,MAAM,GACrB5K;YACN6K,OACE,QAAOR,kCAAAA,eAAgBQ,KAAK,MAAK,cAC7BR,eAAeQ,KAAK,GACpB7K;YACN8K,MAAM;mBAAKT,CAAAA,kCAAAA,eAAgBS,IAAI,KAAIjB,aAAaiB,IAAI;aAAE;QACxD;QACA,MAAMmP,kBAAkB,MAAMra,qBAAqBiJ,GAAG,CACpDkW,sBACA3R,oBACA7H,MACAH,KACA2S,0BAA0BrU,GAAG,CAAC,AAAC8E,IAAYuF,MAAM,IAAIvM,YAAYgH,KACjEoD;QAGF,MAAMsO,oBAAoBta,qBAAqBiJ,GAAG,CAChDkW,sBACAnV,aAAaL,sBAAsB,EACnC0Q,iBACA5Q,wBAAwBG,aAAa,EACrC;YACEtI;YACAwH,SAASwP;QACX;QAGF,IAAI;YACF,6EAA6E;YAC7E,wFAAwF;YACxF,uCAAuC;YACvC,MAAMiC,aAAa,MAAMva,qBAAqBiJ,GAAG,CAC/CkW,sBACAtlB,2BACA;gBACE2gB,gBACE9Y,QAAQ;gBACV+Y,uBACE,KAAC9K;oBACCnB,mBAAmB8L;oBACnB5L,4BAA4BA;oBAC5BD,gBAAgB0L;oBAChB1Q,yBAAyBA;oBACzBkF,mBAAmB,CAAC,CAACkI;oBACrBlU,OAAOA;;gBAGX+X,eAAe;oBACb/X;oBACA,wCAAwC;oBACxC+W,kBAAkB;wBAACU;qBAAqB;oBACxCjE;gBACF;YACF;YAGF,IAAIwH,+BAA+B1X,YAAY;gBAC7C,MAAMR,aAAa,MAAMtL,eACvByjB,2BAA2BU,QAAQ;gBAErCjM,SAAS5M,UAAU,GAAGA;gBACtB4M,SAASkM,WAAW,GAAG,MAAMC,mBAC3B/Y,YACA0Z,sBACAnV,cACA5D,YACA7C;YAEJ;YAEA,oEAAoE;YACpE,gEAAgE;YAChE,MAAM6b,eACJxB,sCAAsCre,8BAClCqe,2BAA2BU,QAAQ,KACnCV,2BAA2BmB,eAAe;YAEhD,OAAO;gBACL,kEAAkE;gBAClE,8BAA8B;gBAC9BlL,iBAAiBsE;gBACjBjE,WAAWsE;gBACX5C,QAAQ,MAAM7b,mBAAmBwgB,YAAY;oBAC3CjB,mBAAmBzb,gCACjBuhB,cACAzc,OACAwT;oBAEFlO,oBAAoB;oBACpB2R,yBACEpU,IAAIS,SAAS,CAAC2T,uBAAuB,KAAK;oBAC5C7R,SAASvC,IAAIS,SAAS,CAAC8B,OAAO;oBAC9BqR,uBAAuBrc,0BAA0B;wBAC/C2a;wBACAL;wBACAgC,sBAAsB,EAAE;wBACxBzC;wBACAW,iBAAiBA;oBACnB;oBACAD;oBACAuC,oBAAoB9Q;gBACtB;gBACA0K,eAAe;gBACf4B,qBACE5K,mBAAmB,OAAOA,eAAeM,UAAU,GAAG3K;gBACxDmV,iBACE9K,mBAAmB,OAAOA,eAAeO,MAAM,GAAG5K;gBACpD8U,gBAAgB6I,gBACdtT,mBAAmB,OAAOA,eAAeQ,KAAK,GAAG7K;gBAEnD0U,eAAerK,mBAAmB,OAAOA,eAAeS,IAAI,GAAG;YACjE;QACF,EAAE,OAAOyP,UAAe;YACtB,IACEpZ,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBtG,0BAA0Bwf,WAC1B;gBACA,MAAM,EAAEC,kBAAkB,EAAE,GAC1BlZ,QAAQ;gBACVkZ;YACF;YACA,MAAMD;QACR;IACF;AACF;AAEA,MAAMhO,uBAAuB,OAC3BhH,MACAH;IAKA,MAAM,EACJ6Z,SAAS,EAAE,gBAAgBC,iBAAiB,EAAE,EAC/C,GAAGhf,gBAAgBqF;IAEpB,MAAM4Z,uBACJ/Z,IAAIE,YAAY,CAAC8G,WAAW;IAC9B,IAAIE;IACJ,IAAI4S,mBAAmB;QACrB,MAAM,GAAG7S,OAAO,GAAG,MAAMpM,gCAAgC;YACvDmF;YACAga,UAAUF,iBAAiB,CAAC,EAAE;YAC9BG,cAAcH,iBAAiB,CAAC,EAAE;YAClClY,aAAa,IAAIC;YACjBC,YAAY,IAAID;QAClB;QACAqF,oBAAoBD;IACtB;IACA,IAAIjH,IAAIY,UAAU,CAAC2C,GAAG,EAAE;QACtB,MAAM2W,MACJne,QAAQC,GAAG,CAAC+P,YAAY,KAAK,SACzBhQ,QAAQC,GAAG,CAACme,uBAAuB,GACnCna,IAAIY,UAAU,CAACsZ,GAAG,IAAI;QAE5B,MAAME,wBAAwB3e,4BAC5Bye,KACAJ,qCAAAA,iBAAmB,CAAC,EAAE;QAExB,IAAI9Z,IAAIY,UAAU,CAACyZ,sBAAsB,IAAID,uBAAuB;YAClE,MAAME,kBAAkBta,IAAIE,YAAY,CAACoa,eAAe;YACxDpT,oBACE,2EAA2E;YAC3E,iEAAiE;0BACjE,KAACoT;gBAECxb,MAAK;gBACLhB,UAAUsc;0BAETlT;eAJG;QAOV;IACF;IAEA,OAAO;QACLF,aAAa+S;QACb9S,QAAQC;IACV;AACF;AAEA,eAAe8R,mBACbuB,kBAA0B,EAC1BtV,cAA8B,EAC9BT,YAA2B,EAC3B5D,UAAsB,EACtB7C,mBAA+C;IAE/C,4BAA4B;IAC5B,EAAE;IACF,yEAAyE;IACzE,oEAAoE;IACpE,0EAA0E;IAC1E,2EAA2E;IAC3E,2EAA2E;IAC3E,wCAAwC;IACxC,EAAE;IACF,oEAAoE;IACpE,4EAA4E;IAC5E,iDAAiD;IAEjD,MAAMkG,0BAA0BrD,WAAWqD,uBAAuB;IAClE,IACE,CAACA,2BACD,yEAAyE;IACzE,mBAAmB;IACnB,EAAE;IACF,wEAAwE;IACxE,2EAA2E;IAC3E,2EAA2E;IAC3E,mCAAmC;IACnCrD,WAAW8C,YAAY,CAAC8W,kBAAkB,KAAK,MAC/C;QACA;IACF;IAEA,wEAAwE;IACxE,0DAA0D;IAC1D,MAAMC,gBAAgB1e,QAAQC,GAAG,CAAC+P,YAAY,KAAK;IACnD,MAAM2O,yBAAyB;QAC7B,2FAA2F;QAC3F,yFAAyF;QACzF,+CAA+C;QAC/CC,eAAe;QACfC,WAAWH,gBACPxW,wBAAwB4W,oBAAoB,GAC5C5W,wBAAwB6W,gBAAgB;QAC5C/N,iBAAiBnV;IACnB;IAEA,MAAMmjB,YAAY9V,eAAeQ,KAAK;IACtC,OAAO,MAAMjB,aAAawU,kBAAkB,CAC1CuB,oBACAQ,WACA9W,wBAAwBG,aAAa,EACrCsW,wBACA3c;AAEJ", "ignoreList": [0]}