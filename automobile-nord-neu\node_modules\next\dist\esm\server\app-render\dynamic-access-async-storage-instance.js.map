{"version": 3, "sources": ["../../../src/server/app-render/dynamic-access-async-storage-instance.ts"], "sourcesContent": ["import { createAsyncLocalStorage } from './async-local-storage'\nimport type { DynamicAccessStorage } from './dynamic-access-async-storage.external'\n\nexport const dynamicAccessAsyncStorageInstance: DynamicAccessStorage =\n  createAsyncLocalStorage()\n"], "names": ["createAsyncLocalStorage", "dynamicAccessAsyncStorageInstance"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,wBAAuB;AAG/D,OAAO,MAAMC,oCACXD,0BAAyB", "ignoreList": [0]}