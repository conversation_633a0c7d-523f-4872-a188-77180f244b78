{"version": 3, "sources": ["../../../src/server/app-render/dynamic-access-async-storage.external.ts"], "sourcesContent": ["import type { AsyncLocalStorage } from 'async_hooks'\n\n// Share the instance module in the next-shared layer\nimport { dynamicAccessAsyncStorageInstance } from './dynamic-access-async-storage-instance' with { 'turbopack-transition': 'next-shared' }\n\nexport interface DynamicAccessAsyncStore {\n  readonly abortController: AbortController\n}\n\nexport type DynamicAccessStorage = AsyncLocalStorage<DynamicAccessAsyncStore>\nexport { dynamicAccessAsyncStorageInstance as dynamicAccessAsyncStorage }\n"], "names": ["dynamicAccessAsyncStorageInstance", "dynamicAccessAsyncStorage"], "mappings": "AAEA,qDAAqD;AACrD,SAASA,iCAAiC,QAAQ,+CAA+C;IAAE,wBAAwB;AAAc,EAAC;AAO1I,SAASA,qCAAqCC,yBAAyB,GAAE", "ignoreList": [0]}