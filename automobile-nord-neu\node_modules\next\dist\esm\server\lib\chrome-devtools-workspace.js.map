{"version": 3, "sources": ["../../../src/server/lib/chrome-devtools-workspace.ts"], "sourcesContent": ["import type { ServerResponse } from 'http'\nimport type { NextConfigComplete } from '../config-shared'\nimport type { NextUrlWithParsedQuery } from '../request-meta'\n\nimport { randomUUID } from 'crypto'\nimport * as fs from 'fs'\nimport * as path from 'path'\nimport { getStorageDirectory } from '../cache-dir'\n\n// Keep the uuid in memory as it should never change during the lifetime of the server.\nlet workspaceUUID: string | null = null\n\nexport function isChromeDevtoolsWorkspaceUrl(\n  url: NextUrlWithParsedQuery\n): boolean {\n  return url.pathname === '/.well-known/appspecific/com.chrome.devtools.json'\n}\n\nexport async function handleChromeDevtoolsWorkspaceRequest(\n  response: ServerResponse,\n  opts: { dir: string },\n  config: NextConfigComplete\n): Promise<void> {\n  response.setHeader('Content-Type', 'application/json')\n  response.end(\n    JSON.stringify(\n      await getChromeDevtoolsWorkspace(opts.dir, config.distDir),\n      null,\n      2\n    )\n  )\n}\n\n/**\n * https://developer.chrome.com/docs/devtools/workspaces#generate-json\n */\ninterface ChromeDevtoolsWorkspace {\n  workspace: {\n    uuid: string\n    root: string\n  }\n}\n\n/**\n * For https://developer.chrome.com/docs/devtools/workspaces\n */\nasync function getChromeDevtoolsWorkspace(\n  root: string,\n  configDistDir: string\n): Promise<ChromeDevtoolsWorkspace> {\n  if (workspaceUUID === null) {\n    const distDir = path.join(root, configDistDir)\n    const cacheBaseDir = getStorageDirectory(distDir)\n\n    if (cacheBaseDir === undefined) {\n      workspaceUUID = randomUUID()\n    } else {\n      const cachedUUIDPath = path.join(\n        cacheBaseDir,\n        'chrome-devtools-workspace-uuid'\n      )\n      try {\n        workspaceUUID = await fs.promises.readFile(cachedUUIDPath, 'utf8')\n      } catch {\n        // TODO: Why does this need to be v4 and not v5?\n        // With v5 we could base it off of the `distDir` and `root` which would\n        // allow us to persist the workspace across .next wipes.\n        workspaceUUID = randomUUID()\n\n        try {\n          await fs.promises.writeFile(cachedUUIDPath, workspaceUUID, 'utf8')\n        } catch (cause) {\n          console.warn(\n            new Error(\n              'Failed to persist Chrome DevTools workspace UUID. The Chrome DevTools Workspace needs to be reconnected after the next page reload.',\n              { cause }\n            )\n          )\n        }\n      }\n    }\n  }\n\n  return {\n    workspace: {\n      uuid: workspaceUUID,\n      root,\n    },\n  }\n}\n"], "names": ["randomUUID", "fs", "path", "getStorageDirectory", "workspaceUUID", "isChromeDevtoolsWorkspaceUrl", "url", "pathname", "handleChromeDevtoolsWorkspaceRequest", "response", "opts", "config", "<PERSON><PERSON><PERSON><PERSON>", "end", "JSON", "stringify", "getChromeDevtoolsWorkspace", "dir", "distDir", "root", "configDistDir", "join", "cacheBaseDir", "undefined", "cachedUUIDPath", "promises", "readFile", "writeFile", "cause", "console", "warn", "Error", "workspace", "uuid"], "mappings": "AAIA,SAASA,UAAU,QAAQ,SAAQ;AACnC,YAAYC,QAAQ,KAAI;AACxB,YAAYC,UAAU,OAAM;AAC5B,SAASC,mBAAmB,QAAQ,eAAc;AAElD,uFAAuF;AACvF,IAAIC,gBAA+B;AAEnC,OAAO,SAASC,6BACdC,GAA2B;IAE3B,OAAOA,IAAIC,QAAQ,KAAK;AAC1B;AAEA,OAAO,eAAeC,qCACpBC,QAAwB,EACxBC,IAAqB,EACrBC,MAA0B;IAE1BF,SAASG,SAAS,CAAC,gBAAgB;IACnCH,SAASI,GAAG,CACVC,KAAKC,SAAS,CACZ,MAAMC,2BAA2BN,KAAKO,GAAG,EAAEN,OAAOO,OAAO,GACzD,MACA;AAGN;AAYA;;CAEC,GACD,eAAeF,2BACbG,IAAY,EACZC,aAAqB;IAErB,IAAIhB,kBAAkB,MAAM;QAC1B,MAAMc,UAAUhB,KAAKmB,IAAI,CAACF,MAAMC;QAChC,MAAME,eAAenB,oBAAoBe;QAEzC,IAAII,iBAAiBC,WAAW;YAC9BnB,gBAAgBJ;QAClB,OAAO;YACL,MAAMwB,iBAAiBtB,KAAKmB,IAAI,CAC9BC,cACA;YAEF,IAAI;gBACFlB,gBAAgB,MAAMH,GAAGwB,QAAQ,CAACC,QAAQ,CAACF,gBAAgB;YAC7D,EAAE,OAAM;gBACN,gDAAgD;gBAChD,uEAAuE;gBACvE,wDAAwD;gBACxDpB,gBAAgBJ;gBAEhB,IAAI;oBACF,MAAMC,GAAGwB,QAAQ,CAACE,SAAS,CAACH,gBAAgBpB,eAAe;gBAC7D,EAAE,OAAOwB,OAAO;oBACdC,QAAQC,IAAI,CACV,qBAGC,CAHD,IAAIC,MACF,uIACA;wBAAEH;oBAAM,IAFV,qBAAA;+BAAA;oCAAA;sCAAA;oBAGA;gBAEJ;YACF;QACF;IACF;IAEA,OAAO;QACLI,WAAW;YACTC,MAAM7B;YACNe;QACF;IACF;AACF", "ignoreList": [0]}