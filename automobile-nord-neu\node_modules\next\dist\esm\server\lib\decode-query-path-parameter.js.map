{"version": 3, "sources": ["../../../src/server/lib/decode-query-path-parameter.ts"], "sourcesContent": ["/**\n * Decodes a query path parameter.\n *\n * @param value - The value to decode.\n * @returns The decoded value.\n */\nexport function decodeQueryPathParameter(value: string) {\n  // When deployed to Vercel, the value may be encoded, so this attempts to\n  // decode it and returns the original value if it fails.\n  try {\n    return decodeURIComponent(value)\n  } catch {\n    return value\n  }\n}\n"], "names": ["decodeQueryPathParameter", "value", "decodeURIComponent"], "mappings": "AAAA;;;;;CAKC,GACD,OAAO,SAASA,yBAAyBC,KAAa;IACpD,yEAAyE;IACzE,wDAAwD;IACxD,IAAI;QACF,OAAOC,mBAAmBD;IAC5B,EAAE,OAAM;QACN,OAAOA;IACT;AACF", "ignoreList": [0]}