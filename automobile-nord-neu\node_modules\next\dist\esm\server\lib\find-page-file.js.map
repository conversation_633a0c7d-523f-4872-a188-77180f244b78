{"version": 3, "sources": ["../../../src/server/lib/find-page-file.ts"], "sourcesContent": ["import { fileExists } from '../../lib/file-exists'\nimport { getPagePaths } from '../../shared/lib/page-path/get-page-paths'\nimport { nonNullable } from '../../lib/non-nullable'\nimport { join, sep, normalize } from 'path'\nimport { promises as fsPromises } from 'fs'\nimport { warn } from '../../build/output/log'\nimport { cyan } from '../../lib/picocolors'\nimport { isMetadataRouteFile } from '../../lib/metadata/is-metadata-route'\nimport type { PageExtensions } from '../../build/page-extensions-type'\n\nasync function isTrueCasePagePath(pagePath: string, pagesDir: string) {\n  const pageSegments = normalize(pagePath).split(sep).filter(Boolean)\n  const segmentExistsPromises = pageSegments.map(async (segment, i) => {\n    const segmentParentDir = join(pagesDir, ...pageSegments.slice(0, i))\n    const parentDirEntries = await fsPromises.readdir(segmentParentDir)\n    return parentDirEntries.includes(segment)\n  })\n\n  return (await Promise.all(segmentExistsPromises)).every(<PERSON><PERSON>an)\n}\n\n/**\n * Finds a page file with the given parameters. If the page is duplicated with\n * multiple extensions it will throw, otherwise it will return the *relative*\n * path to the page file or null if it is not found.\n *\n * @param pagesDir Absolute path to the pages folder with trailing `/pages`.\n * @param normalizedPagePath The page normalized (it will be denormalized).\n * @param pageExtensions Array of page extensions.\n */\nexport async function findPageFile(\n  pagesDir: string,\n  normalizedPagePath: string,\n  pageExtensions: PageExtensions,\n  isAppDir: boolean\n): Promise<string | null> {\n  const pagePaths = getPagePaths(normalizedPagePath, pageExtensions, isAppDir)\n  const [existingPath, ...others] = (\n    await Promise.all(\n      pagePaths.map(async (path) => {\n        const filePath = join(pagesDir, path)\n        try {\n          return (await fileExists(filePath)) ? path : null\n        } catch (err: any) {\n          if (!err?.code?.includes('ENOTDIR')) throw err\n        }\n        return null\n      })\n    )\n  ).filter(nonNullable)\n\n  if (!existingPath) {\n    return null\n  }\n\n  if (!(await isTrueCasePagePath(existingPath, pagesDir))) {\n    return null\n  }\n\n  if (others.length > 0) {\n    warn(\n      `Duplicate page detected. ${cyan(join('pages', existingPath))} and ${cyan(\n        join('pages', others[0])\n      )} both resolve to ${cyan(normalizedPagePath)}.`\n    )\n  }\n\n  return existingPath\n}\n\n/**\n *\n * createValidFileMatcher receives configured page extensions and return helpers to determine:\n * `isLayoutsLeafPage`: if a file is a valid page file or routes file under app directory\n * `isTrackedFiles`: if it's a tracked file for webpack watcher\n *\n */\nexport function createValidFileMatcher(\n  pageExtensions: PageExtensions,\n  appDirPath: string | undefined\n) {\n  const getExtensionRegexString = (extensions: string[]) =>\n    `(?:${extensions.join('|')})`\n\n  const validExtensionFileRegex = new RegExp(\n    '\\\\.' + getExtensionRegexString(pageExtensions) + '$'\n  )\n  const leafOnlyPageFileRegex = new RegExp(\n    `(^(page|route)|[\\\\\\\\/](page|route))\\\\.${getExtensionRegexString(\n      pageExtensions\n    )}$`\n  )\n  const rootNotFoundFileRegex = new RegExp(\n    `^not-found\\\\.${getExtensionRegexString(pageExtensions)}$`\n  )\n  /** TODO-METADATA: support other metadata routes\n   *  regex for:\n   *\n   * /robots.txt|<ext>\n   * /sitemap.xml|<ext>\n   * /favicon.ico\n   * /manifest.json|<ext>\n   * <route>/icon.png|jpg|<ext>\n   * <route>/apple-touch-icon.png|jpg|<ext>\n   *\n   */\n\n  /**\n   * Match the file if it's a metadata route file, static: if the file is a static metadata file.\n   * It needs to be a file which doesn't match the custom metadata routes e.g. `app/robots.txt/route.js`\n   */\n  function isMetadataFile(filePath: string) {\n    const appDirRelativePath = appDirPath\n      ? filePath.replace(appDirPath, '')\n      : filePath\n\n    return isMetadataRouteFile(appDirRelativePath, pageExtensions, true)\n  }\n\n  // Determine if the file is leaf node page file or route file under layouts,\n  // 'page.<extension>' | 'route.<extension>'\n  function isAppRouterPage(filePath: string) {\n    return leafOnlyPageFileRegex.test(filePath) || isMetadataFile(filePath)\n  }\n\n  function isPageFile(filePath: string) {\n    return validExtensionFileRegex.test(filePath) || isMetadataFile(filePath)\n  }\n\n  function isRootNotFound(filePath: string) {\n    if (!appDirPath) {\n      return false\n    }\n    if (!filePath.startsWith(appDirPath + sep)) {\n      return false\n    }\n    const rest = filePath.slice(appDirPath.length + 1)\n    return rootNotFoundFileRegex.test(rest)\n  }\n\n  return {\n    isPageFile,\n    isAppRouterPage,\n    isMetadataFile,\n    isRootNotFound,\n  }\n}\n"], "names": ["fileExists", "getPagePaths", "nonNullable", "join", "sep", "normalize", "promises", "fsPromises", "warn", "cyan", "isMetadataRouteFile", "isTrueCasePagePath", "pagePath", "pagesDir", "pageSegments", "split", "filter", "Boolean", "segmentExistsPromises", "map", "segment", "i", "segmentParentDir", "slice", "parentDirEntries", "readdir", "includes", "Promise", "all", "every", "findPageFile", "normalizedPagePath", "pageExtensions", "isAppDir", "pagePaths", "existingPath", "others", "path", "filePath", "err", "code", "length", "createValidFileMatcher", "appDirPath", "getExtensionRegexString", "extensions", "validExtensionFileRegex", "RegExp", "leafOnlyPageFileRegex", "rootNotFoundFileRegex", "isMetadataFile", "appDirRelativePath", "replace", "isAppRouterPage", "test", "isPageFile", "isRootNotFound", "startsWith", "rest"], "mappings": "AAAA,SAASA,UAAU,QAAQ,wBAAuB;AAClD,SAASC,YAAY,QAAQ,4CAA2C;AACxE,SAASC,WAAW,QAAQ,yBAAwB;AACpD,SAASC,IAAI,EAAEC,GAAG,EAAEC,SAAS,QAAQ,OAAM;AAC3C,SAASC,YAAYC,UAAU,QAAQ,KAAI;AAC3C,SAASC,IAAI,QAAQ,yBAAwB;AAC7C,SAASC,IAAI,QAAQ,uBAAsB;AAC3C,SAASC,mBAAmB,QAAQ,uCAAsC;AAG1E,eAAeC,mBAAmBC,QAAgB,EAAEC,QAAgB;IAClE,MAAMC,eAAeT,UAAUO,UAAUG,KAAK,CAACX,KAAKY,MAAM,CAACC;IAC3D,MAAMC,wBAAwBJ,aAAaK,GAAG,CAAC,OAAOC,SAASC;QAC7D,MAAMC,mBAAmBnB,KAAKU,aAAaC,aAAaS,KAAK,CAAC,GAAGF;QACjE,MAAMG,mBAAmB,MAAMjB,WAAWkB,OAAO,CAACH;QAClD,OAAOE,iBAAiBE,QAAQ,CAACN;IACnC;IAEA,OAAO,AAAC,CAAA,MAAMO,QAAQC,GAAG,CAACV,sBAAqB,EAAGW,KAAK,CAACZ;AAC1D;AAEA;;;;;;;;CAQC,GACD,OAAO,eAAea,aACpBjB,QAAgB,EAChBkB,kBAA0B,EAC1BC,cAA8B,EAC9BC,QAAiB;IAEjB,MAAMC,YAAYjC,aAAa8B,oBAAoBC,gBAAgBC;IACnE,MAAM,CAACE,cAAc,GAAGC,OAAO,GAAG,AAChC,CAAA,MAAMT,QAAQC,GAAG,CACfM,UAAUf,GAAG,CAAC,OAAOkB;QACnB,MAAMC,WAAWnC,KAAKU,UAAUwB;QAChC,IAAI;YACF,OAAO,AAAC,MAAMrC,WAAWsC,YAAaD,OAAO;QAC/C,EAAE,OAAOE,KAAU;gBACZA;YAAL,IAAI,EAACA,wBAAAA,YAAAA,IAAKC,IAAI,qBAATD,UAAWb,QAAQ,CAAC,aAAY,MAAMa;QAC7C;QACA,OAAO;IACT,GACF,EACAvB,MAAM,CAACd;IAET,IAAI,CAACiC,cAAc;QACjB,OAAO;IACT;IAEA,IAAI,CAAE,MAAMxB,mBAAmBwB,cAActB,WAAY;QACvD,OAAO;IACT;IAEA,IAAIuB,OAAOK,MAAM,GAAG,GAAG;QACrBjC,KACE,CAAC,yBAAyB,EAAEC,KAAKN,KAAK,SAASgC,eAAe,KAAK,EAAE1B,KACnEN,KAAK,SAASiC,MAAM,CAAC,EAAE,GACvB,iBAAiB,EAAE3B,KAAKsB,oBAAoB,CAAC,CAAC;IAEpD;IAEA,OAAOI;AACT;AAEA;;;;;;CAMC,GACD,OAAO,SAASO,uBACdV,cAA8B,EAC9BW,UAA8B;IAE9B,MAAMC,0BAA0B,CAACC,aAC/B,CAAC,GAAG,EAAEA,WAAW1C,IAAI,CAAC,KAAK,CAAC,CAAC;IAE/B,MAAM2C,0BAA0B,IAAIC,OAClC,QAAQH,wBAAwBZ,kBAAkB;IAEpD,MAAMgB,wBAAwB,IAAID,OAChC,CAAC,sCAAsC,EAAEH,wBACvCZ,gBACA,CAAC,CAAC;IAEN,MAAMiB,wBAAwB,IAAIF,OAChC,CAAC,aAAa,EAAEH,wBAAwBZ,gBAAgB,CAAC,CAAC;IAE5D;;;;;;;;;;GAUC,GAED;;;GAGC,GACD,SAASkB,eAAeZ,QAAgB;QACtC,MAAMa,qBAAqBR,aACvBL,SAASc,OAAO,CAACT,YAAY,MAC7BL;QAEJ,OAAO5B,oBAAoByC,oBAAoBnB,gBAAgB;IACjE;IAEA,4EAA4E;IAC5E,2CAA2C;IAC3C,SAASqB,gBAAgBf,QAAgB;QACvC,OAAOU,sBAAsBM,IAAI,CAAChB,aAAaY,eAAeZ;IAChE;IAEA,SAASiB,WAAWjB,QAAgB;QAClC,OAAOQ,wBAAwBQ,IAAI,CAAChB,aAAaY,eAAeZ;IAClE;IAEA,SAASkB,eAAelB,QAAgB;QACtC,IAAI,CAACK,YAAY;YACf,OAAO;QACT;QACA,IAAI,CAACL,SAASmB,UAAU,CAACd,aAAavC,MAAM;YAC1C,OAAO;QACT;QACA,MAAMsD,OAAOpB,SAASf,KAAK,CAACoB,WAAWF,MAAM,GAAG;QAChD,OAAOQ,sBAAsBK,IAAI,CAACI;IACpC;IAEA,OAAO;QACLH;QACAF;QACAH;QACAM;IACF;AACF", "ignoreList": [0]}