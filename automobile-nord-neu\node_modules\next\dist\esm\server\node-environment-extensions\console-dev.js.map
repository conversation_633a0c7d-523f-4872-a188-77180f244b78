{"version": 3, "sources": ["../../../src/server/node-environment-extensions/console-dev.tsx"], "sourcesContent": ["import { dim } from '../../lib/picocolors'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\n\ntype InterceptableConsoleMethod =\n  | 'error'\n  | 'assert'\n  | 'debug'\n  | 'dir'\n  | 'dirxml'\n  | 'group'\n  | 'groupCollapsed'\n  | 'groupEnd'\n  | 'info'\n  | 'log'\n  | 'table'\n  | 'trace'\n  | 'warn'\n\n// This function could be used from multiple places, including hook.\n// Skips CSS and object arguments, inlines other in the first argument as a template string\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @source https://github.com/facebook/react/blob/b44a99bf58d69d52b5288d9eadcc6d226d705e11/packages/react-devtools-shared/src/backend/utils/formatConsoleArguments.js#L14\n */\nfunction formatConsoleArguments(maybeMessage: any, ...inputArgs: any[]): any[] {\n  if (inputArgs.length === 0 || typeof maybeMessage !== 'string') {\n    return [maybeMessage, ...inputArgs]\n  }\n\n  const args = inputArgs.slice()\n\n  let template = ''\n  let argumentsPointer = 0\n  for (let i = 0; i < maybeMessage.length; ++i) {\n    const currentChar = maybeMessage[i]\n    if (currentChar !== '%') {\n      template += currentChar\n      continue\n    }\n\n    const nextChar = maybeMessage[i + 1]\n    ++i\n\n    // Only keep CSS and objects, inline other arguments\n    switch (nextChar) {\n      case 'c':\n      case 'O':\n      case 'o': {\n        ++argumentsPointer\n        template += `%${nextChar}`\n\n        break\n      }\n      case 'd':\n      case 'i': {\n        const [arg] = args.splice(argumentsPointer, 1)\n        template += parseInt(arg, 10).toString()\n\n        break\n      }\n      case 'f': {\n        const [arg] = args.splice(argumentsPointer, 1)\n        template += parseFloat(arg).toString()\n\n        break\n      }\n      case 's': {\n        const [arg] = args.splice(argumentsPointer, 1)\n        template += String(arg)\n\n        break\n      }\n\n      default:\n        template += `%${nextChar}`\n    }\n  }\n\n  return [template, ...args]\n}\n\nconst isColorSupported = dim('test') !== 'test'\n// TODO: Breaks when complex objects are logged\n// dim(\"%s\") does not work in Chrome\nconst ANSI_STYLE_DIMMING_TEMPLATE = isColorSupported\n  ? '\\x1b[2;38;2;124;124;124m%s\\x1b[0m'\n  : '%s'\n\nfunction dimConsoleCall(\n  methodName: InterceptableConsoleMethod,\n  args: any[]\n): any[] {\n  switch (methodName) {\n    case 'dir':\n    case 'dirxml':\n    case 'groupEnd':\n    case 'table': {\n      // These methods cannot be colorized because they don't take a formatting string.\n      return args\n    }\n    case 'assert': {\n      // assert takes formatting options as the second argument.\n      return [args[0]].concat(\n        ANSI_STYLE_DIMMING_TEMPLATE,\n        ...formatConsoleArguments(args[1], ...args.slice(2))\n      )\n    }\n    default:\n      return [ANSI_STYLE_DIMMING_TEMPLATE].concat(\n        ...formatConsoleArguments(args[0], ...args.slice(1))\n      )\n  }\n}\n\n// Based on https://github.com/facebook/react/blob/28dc0776be2e1370fe217549d32aee2519f0cf05/packages/react-server/src/ReactFlightServer.js#L248\nfunction patchConsoleMethodDEV(methodName: InterceptableConsoleMethod): void {\n  const descriptor = Object.getOwnPropertyDescriptor(console, methodName)\n  if (\n    descriptor &&\n    (descriptor.configurable || descriptor.writable) &&\n    typeof descriptor.value === 'function'\n  ) {\n    const originalMethod = descriptor.value\n    const originalName = Object.getOwnPropertyDescriptor(originalMethod, 'name')\n    const wrapperMethod = function (this: typeof console, ...args: any[]) {\n      const workUnitStore = workUnitAsyncStorage.getStore()\n      const isPrerenderValidation =\n        workUnitStore !== undefined &&\n        (workUnitStore.type === 'prerender-client' ||\n          workUnitStore.type === 'prerender')\n\n      if (isPrerenderValidation) {\n        originalMethod.apply(this, dimConsoleCall(methodName, args))\n      } else {\n        originalMethod.apply(this, args)\n      }\n    }\n    if (originalName) {\n      Object.defineProperty(wrapperMethod, 'name', originalName)\n    }\n    Object.defineProperty(console, methodName, {\n      value: wrapperMethod,\n    })\n  }\n}\n\npatchConsoleMethodDEV('error')\npatchConsoleMethodDEV('assert')\npatchConsoleMethodDEV('debug')\npatchConsoleMethodDEV('dir')\npatchConsoleMethodDEV('dirxml')\npatchConsoleMethodDEV('group')\npatchConsoleMethodDEV('groupCollapsed')\npatchConsoleMethodDEV('groupEnd')\npatchConsoleMethodDEV('info')\npatchConsoleMethodDEV('log')\npatchConsoleMethodDEV('table')\npatchConsoleMethodDEV('trace')\npatchConsoleMethodDEV('warn')\n"], "names": ["dim", "workUnitAsyncStorage", "formatConsoleArguments", "maybeMessage", "inputArgs", "length", "args", "slice", "template", "argumentsPointer", "i", "currentChar", "nextChar", "arg", "splice", "parseInt", "toString", "parseFloat", "String", "isColorSupported", "ANSI_STYLE_DIMMING_TEMPLATE", "dimConsoleCall", "methodName", "concat", "patchConsoleMethodDEV", "descriptor", "Object", "getOwnPropertyDescriptor", "console", "configurable", "writable", "value", "originalMethod", "originalName", "wrapperMethod", "workUnitStore", "getStore", "isPrerenderValidation", "undefined", "type", "apply", "defineProperty"], "mappings": "AAAA,SAASA,GAAG,QAAQ,uBAAsB;AAC1C,SAASC,oBAAoB,QAAQ,iDAAgD;AAiBrF,oEAAoE;AACpE,2FAA2F;AAC3F;;;;;;;CAOC,GACD,SAASC,uBAAuBC,YAAiB,EAAE,GAAGC,SAAgB;IACpE,IAAIA,UAAUC,MAAM,KAAK,KAAK,OAAOF,iBAAiB,UAAU;QAC9D,OAAO;YAACA;eAAiBC;SAAU;IACrC;IAEA,MAAME,OAAOF,UAAUG,KAAK;IAE5B,IAAIC,WAAW;IACf,IAAIC,mBAAmB;IACvB,IAAK,IAAIC,IAAI,GAAGA,IAAIP,aAAaE,MAAM,EAAE,EAAEK,EAAG;QAC5C,MAAMC,cAAcR,YAAY,CAACO,EAAE;QACnC,IAAIC,gBAAgB,KAAK;YACvBH,YAAYG;YACZ;QACF;QAEA,MAAMC,WAAWT,YAAY,CAACO,IAAI,EAAE;QACpC,EAAEA;QAEF,oDAAoD;QACpD,OAAQE;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBAAK;oBACR,EAAEH;oBACFD,YAAY,CAAC,CAAC,EAAEI,UAAU;oBAE1B;gBACF;YACA,KAAK;YACL,KAAK;gBAAK;oBACR,MAAM,CAACC,IAAI,GAAGP,KAAKQ,MAAM,CAACL,kBAAkB;oBAC5CD,YAAYO,SAASF,KAAK,IAAIG,QAAQ;oBAEtC;gBACF;YACA,KAAK;gBAAK;oBACR,MAAM,CAACH,IAAI,GAAGP,KAAKQ,MAAM,CAACL,kBAAkB;oBAC5CD,YAAYS,WAAWJ,KAAKG,QAAQ;oBAEpC;gBACF;YACA,KAAK;gBAAK;oBACR,MAAM,CAACH,IAAI,GAAGP,KAAKQ,MAAM,CAACL,kBAAkB;oBAC5CD,YAAYU,OAAOL;oBAEnB;gBACF;YAEA;gBACEL,YAAY,CAAC,CAAC,EAAEI,UAAU;QAC9B;IACF;IAEA,OAAO;QAACJ;WAAaF;KAAK;AAC5B;AAEA,MAAMa,mBAAmBnB,IAAI,YAAY;AACzC,+CAA+C;AAC/C,oCAAoC;AACpC,MAAMoB,8BAA8BD,mBAChC,sCACA;AAEJ,SAASE,eACPC,UAAsC,EACtChB,IAAW;IAEX,OAAQgB;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAS;gBACZ,iFAAiF;gBACjF,OAAOhB;YACT;QACA,KAAK;YAAU;gBACb,0DAA0D;gBAC1D,OAAO;oBAACA,IAAI,CAAC,EAAE;iBAAC,CAACiB,MAAM,CACrBH,gCACGlB,uBAAuBI,IAAI,CAAC,EAAE,KAAKA,KAAKC,KAAK,CAAC;YAErD;QACA;YACE,OAAO;gBAACa;aAA4B,CAACG,MAAM,IACtCrB,uBAAuBI,IAAI,CAAC,EAAE,KAAKA,KAAKC,KAAK,CAAC;IAEvD;AACF;AAEA,+IAA+I;AAC/I,SAASiB,sBAAsBF,UAAsC;IACnE,MAAMG,aAAaC,OAAOC,wBAAwB,CAACC,SAASN;IAC5D,IACEG,cACCA,CAAAA,WAAWI,YAAY,IAAIJ,WAAWK,QAAQ,AAAD,KAC9C,OAAOL,WAAWM,KAAK,KAAK,YAC5B;QACA,MAAMC,iBAAiBP,WAAWM,KAAK;QACvC,MAAME,eAAeP,OAAOC,wBAAwB,CAACK,gBAAgB;QACrE,MAAME,gBAAgB,SAAgC,GAAG5B,IAAW;YAClE,MAAM6B,gBAAgBlC,qBAAqBmC,QAAQ;YACnD,MAAMC,wBACJF,kBAAkBG,aACjBH,CAAAA,cAAcI,IAAI,KAAK,sBACtBJ,cAAcI,IAAI,KAAK,WAAU;YAErC,IAAIF,uBAAuB;gBACzBL,eAAeQ,KAAK,CAAC,IAAI,EAAEnB,eAAeC,YAAYhB;YACxD,OAAO;gBACL0B,eAAeQ,KAAK,CAAC,IAAI,EAAElC;YAC7B;QACF;QACA,IAAI2B,cAAc;YAChBP,OAAOe,cAAc,CAACP,eAAe,QAAQD;QAC/C;QACAP,OAAOe,cAAc,CAACb,SAASN,YAAY;YACzCS,OAAOG;QACT;IACF;AACF;AAEAV,sBAAsB;AACtBA,sBAAsB;AACtBA,sBAAsB;AACtBA,sBAAsB;AACtBA,sBAAsB;AACtBA,sBAAsB;AACtBA,sBAAsB;AACtBA,sBAAsB;AACtBA,sBAAsB;AACtBA,sBAAsB;AACtBA,sBAAsB;AACtBA,sBAAsB;AACtBA,sBAAsB", "ignoreList": [0]}