{"version": 3, "sources": ["../../../src/server/node-environment-extensions/date.tsx"], "sourcesContent": ["/**\n * We extend `Date` during builds and revalidates to ensure that prerenders don't observe the clock as a source of IO\n * When dynamicIO is enabled. The current time is a form of IO even though it resolves synchronously. When dyanmicIO is\n * enabled we need to ensure that clock time is excluded from prerenders unless it is cached.\n *\n * There is tension here because time is used for both output and introspection. While arbitrary we intend to reserve\n * `Date` for output use cases and `performance` for introspection use cases. If you want to measure\n * how long something takes use `performance.timeOrigin` and `performance.now()` rather than `Date.now()` for instance.\n *\n * The extensions here never error nor alter the underlying Date objects, strings, and numbers created and thus should be transparent to callers.\n */\nimport { io } from './utils'\n\nfunction createNow(originalNow: typeof Date.now) {\n  return {\n    now: function now() {\n      io('`Date.now()`', 'time')\n      return originalNow()\n    },\n  }['now'.slice() as 'now'].bind(null)\n}\n\nfunction createDate(originalConstructor: typeof Date): typeof Date {\n  const properties = Object.getOwnPropertyDescriptors(originalConstructor)\n  properties.now.value = createNow(originalConstructor.now)\n\n  const apply = Reflect.apply\n  const construct = Reflect.construct\n\n  const newConstructor = Object.defineProperties(\n    // Ideally this should not minify the name.\n    function Date() {\n      if (new.target === undefined) {\n        io('`Date()`', 'time')\n        return apply(originalConstructor, undefined, arguments)\n      }\n      if (arguments.length === 0) {\n        io('`new Date()`', 'time')\n      }\n      return construct(originalConstructor, arguments, new.target)\n    },\n    properties\n  )\n  Object.defineProperty(originalConstructor.prototype, 'constructor', {\n    value: newConstructor,\n  })\n  return newConstructor as typeof Date\n}\n\ntry {\n  // eslint-disable-next-line no-native-reassign\n  Date = createDate(Date)\n} catch {\n  console.error(\n    'Failed to install `Date` class extension. When using `experimental.dynamicIO`, APIs that read the current time will not correctly trigger dynamic behavior.'\n  )\n}\n"], "names": ["io", "createNow", "originalNow", "now", "slice", "bind", "createDate", "originalConstructor", "properties", "Object", "getOwnPropertyDescriptors", "value", "apply", "Reflect", "construct", "newConstructor", "defineProperties", "Date", "undefined", "arguments", "length", "defineProperty", "prototype", "console", "error"], "mappings": "AAAA;;;;;;;;;;CAUC,GACD,SAASA,EAAE,QAAQ,UAAS;AAE5B,SAASC,UAAUC,WAA4B;IAC7C,OAAO,CAAA;QACLC,KAAK,SAASA;YACZH,GAAG,gBAAgB;YACnB,OAAOE;QACT;IACF,CAAA,CAAC,CAAC,MAAME,KAAK,GAAY,CAACC,IAAI,CAAC;AACjC;AAEA,SAASC,WAAWC,mBAAgC;IAClD,MAAMC,aAAaC,OAAOC,yBAAyB,CAACH;IACpDC,WAAWL,GAAG,CAACQ,KAAK,GAAGV,UAAUM,oBAAoBJ,GAAG;IAExD,MAAMS,QAAQC,QAAQD,KAAK;IAC3B,MAAME,YAAYD,QAAQC,SAAS;IAEnC,MAAMC,iBAAiBN,OAAOO,gBAAgB,CAC5C,2CAA2C;IAC3C,SAASC;QACP,IAAI,eAAeC,WAAW;YAC5BlB,GAAG,YAAY;YACf,OAAOY,MAAML,qBAAqBW,WAAWC;QAC/C;QACA,IAAIA,UAAUC,MAAM,KAAK,GAAG;YAC1BpB,GAAG,gBAAgB;QACrB;QACA,OAAOc,UAAUP,qBAAqBY,WAAW;IACnD,GACAX;IAEFC,OAAOY,cAAc,CAACd,oBAAoBe,SAAS,EAAE,eAAe;QAClEX,OAAOI;IACT;IACA,OAAOA;AACT;AAEA,IAAI;IACF,8CAA8C;IAC9CE,OAAOX,WAAWW;AACpB,EAAE,OAAM;IACNM,QAAQC,KAAK,CACX;AAEJ", "ignoreList": [0]}