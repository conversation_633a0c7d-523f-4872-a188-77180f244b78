{"version": 3, "sources": ["../../../src/server/node-environment-extensions/node-crypto.tsx"], "sourcesContent": ["/**\n * We extend node:crypto APIs during builds and revalidates to ensure that prerenders don't observe random bytes\n * When dynamicIO is enabled. Random bytes are a form of IO even if they resolve synchronously. When dyanmicIO is\n * enabled we need to ensure that random bytes are excluded from prerenders unless they are cached.\n *\n *\n * The extensions here never error nor alter the underlying return values and thus should be transparent to callers.\n */\n\nimport { io } from './utils'\n\nif (process.env.NEXT_RUNTIME === 'edge') {\n  // nothing to patch\n} else {\n  const nodeCrypto = require('node:crypto') as typeof import('node:crypto')\n\n  // require('node:crypto').getRandomValues is an alias for\n  // crypto.getRandomValues which is extended in web-crypto.tsx\n\n  // require('node:crypto').randomUUID is not an alias for crypto.randomUUID\n\n  const randomUUIDExpression = \"`require('node:crypto').randomUUID()`\"\n  try {\n    const _randomUUID = nodeCrypto.randomUUID\n    nodeCrypto.randomUUID = function randomUUID() {\n      io(randomUUIDExpression, 'random')\n      return _randomUUID.apply(this, arguments as any)\n    }\n  } catch {\n    console.error(\n      `Failed to install ${randomUUIDExpression} extension. When using \\`experimental.dynamicIO\\` calling this function will not correctly trigger dynamic behavior.`\n    )\n  }\n\n  const randomBytesExpression = \"`require('node:crypto').randomBytes(size)`\"\n  try {\n    const _randomBytes = nodeCrypto.randomBytes\n    // @ts-expect-error -- TODO: tell TS the overloads are preserved\n    nodeCrypto.randomBytes = function randomBytes() {\n      if (typeof arguments[1] !== 'function') {\n        // randomBytes is sync if the second arg is undefined\n        io(randomBytesExpression, 'random')\n      }\n      return _randomBytes.apply(this, arguments as any)\n    }\n  } catch {\n    console.error(\n      `Failed to install ${randomBytesExpression} extension. When using \\`experimental.dynamicIO\\` calling this function without a callback argument will not correctly trigger dynamic behavior.`\n    )\n  }\n\n  const randomFillSyncExpression =\n    \"`require('node:crypto').randomFillSync(...)`\"\n  try {\n    const _randomFillSync = nodeCrypto.randomFillSync\n    // @ts-expect-error -- TODO: tell TS the overloads are preserved\n    nodeCrypto.randomFillSync = function randomFillSync() {\n      io(randomFillSyncExpression, 'random')\n      return _randomFillSync.apply(this, arguments as any)\n    }\n  } catch {\n    console.error(\n      `Failed to install ${randomFillSyncExpression} extension. When using \\`experimental.dynamicIO\\` calling this function will not correctly trigger dynamic behavior.`\n    )\n  }\n\n  const randomIntExpression = \"`require('node:crypto').randomInt(min, max)`\"\n  try {\n    const _randomInt = nodeCrypto.randomInt\n    // @ts-expect-error -- TODO: tell TS the overloads are preserved\n    nodeCrypto.randomInt = function randomInt() {\n      if (typeof arguments[2] !== 'function') {\n        // randomInt is sync if the third arg is undefined\n        io(randomIntExpression, 'random')\n      }\n      return _randomInt.apply(this, arguments as any)\n    }\n  } catch {\n    console.error(\n      `Failed to install ${randomBytesExpression} extension. When using \\`experimental.dynamicIO\\` calling this function without a callback argument will not correctly trigger dynamic behavior.`\n    )\n  }\n\n  const generatePrimeSyncExpression =\n    \"`require('node:crypto').generatePrimeSync(...)`\"\n  try {\n    const _generatePrimeSync = nodeCrypto.generatePrimeSync\n    // @ts-expect-error -- TODO: tell TS the overloads are preserved\n    nodeCrypto.generatePrimeSync = function generatePrimeSync() {\n      io(generatePrimeSyncExpression, 'random')\n      return _generatePrimeSync.apply(this, arguments as any)\n    }\n  } catch {\n    console.error(\n      `Failed to install ${generatePrimeSyncExpression} extension. When using \\`experimental.dynamicIO\\` calling this function will not correctly trigger dynamic behavior.`\n    )\n  }\n\n  const generateKeyPairSyncExpression =\n    \"`require('node:crypto').generateKeyPairSync(...)`\"\n  try {\n    const _generateKeyPairSync = nodeCrypto.generateKeyPairSync\n    // @ts-expect-error -- TODO: tell TS the overloads are preserved\n    nodeCrypto.generateKeyPairSync = function generateKeyPairSync() {\n      io(generateKeyPairSyncExpression, 'random')\n      return _generateKeyPairSync.apply(this, arguments as any)\n    }\n  } catch {\n    console.error(\n      `Failed to install ${generateKeyPairSyncExpression} extension. When using \\`experimental.dynamicIO\\` calling this function will not correctly trigger dynamic behavior.`\n    )\n  }\n\n  const generateKeySyncExpression =\n    \"`require('node:crypto').generateKeySync(...)`\"\n  try {\n    const _generateKeySync = nodeCrypto.generateKeySync\n    nodeCrypto.generateKeySync = function generateKeySync() {\n      io(generateKeySyncExpression, 'random')\n      return _generateKeySync.apply(this, arguments as any)\n    }\n  } catch {\n    console.error(\n      `Failed to install ${generateKeySyncExpression} extension. When using \\`experimental.dynamicIO\\` calling this function will not correctly trigger dynamic behavior.`\n    )\n  }\n}\n"], "names": ["io", "process", "env", "NEXT_RUNTIME", "nodeCrypto", "require", "randomUUIDExpression", "_randomUUID", "randomUUID", "apply", "arguments", "console", "error", "randomBytesExpression", "_randomBytes", "randomBytes", "randomFillSyncExpression", "_randomFillSync", "randomFillSync", "randomIntExpression", "_randomInt", "randomInt", "generatePrimeSyncExpression", "_generatePrimeSync", "generatePrimeSync", "generateKeyPairSyncExpression", "_generateKeyPairSync", "generateKeyPairSync", "generateKeySyncExpression", "_generateKeySync", "generateKeySync"], "mappings": "AAAA;;;;;;;CAOC,GAED,SAASA,EAAE,QAAQ,UAAS;AAE5B,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;AACvC,mBAAmB;AACrB,OAAO;IACL,MAAMC,aAAaC,QAAQ;IAE3B,yDAAyD;IACzD,6DAA6D;IAE7D,0EAA0E;IAE1E,MAAMC,uBAAuB;IAC7B,IAAI;QACF,MAAMC,cAAcH,WAAWI,UAAU;QACzCJ,WAAWI,UAAU,GAAG,SAASA;YAC/BR,GAAGM,sBAAsB;YACzB,OAAOC,YAAYE,KAAK,CAAC,IAAI,EAAEC;QACjC;IACF,EAAE,OAAM;QACNC,QAAQC,KAAK,CACX,CAAC,kBAAkB,EAAEN,qBAAqB,oHAAoH,CAAC;IAEnK;IAEA,MAAMO,wBAAwB;IAC9B,IAAI;QACF,MAAMC,eAAeV,WAAWW,WAAW;QAC3C,gEAAgE;QAChEX,WAAWW,WAAW,GAAG,SAASA;YAChC,IAAI,OAAOL,SAAS,CAAC,EAAE,KAAK,YAAY;gBACtC,qDAAqD;gBACrDV,GAAGa,uBAAuB;YAC5B;YACA,OAAOC,aAAaL,KAAK,CAAC,IAAI,EAAEC;QAClC;IACF,EAAE,OAAM;QACNC,QAAQC,KAAK,CACX,CAAC,kBAAkB,EAAEC,sBAAsB,gJAAgJ,CAAC;IAEhM;IAEA,MAAMG,2BACJ;IACF,IAAI;QACF,MAAMC,kBAAkBb,WAAWc,cAAc;QACjD,gEAAgE;QAChEd,WAAWc,cAAc,GAAG,SAASA;YACnClB,GAAGgB,0BAA0B;YAC7B,OAAOC,gBAAgBR,KAAK,CAAC,IAAI,EAAEC;QACrC;IACF,EAAE,OAAM;QACNC,QAAQC,KAAK,CACX,CAAC,kBAAkB,EAAEI,yBAAyB,oHAAoH,CAAC;IAEvK;IAEA,MAAMG,sBAAsB;IAC5B,IAAI;QACF,MAAMC,aAAahB,WAAWiB,SAAS;QACvC,gEAAgE;QAChEjB,WAAWiB,SAAS,GAAG,SAASA;YAC9B,IAAI,OAAOX,SAAS,CAAC,EAAE,KAAK,YAAY;gBACtC,kDAAkD;gBAClDV,GAAGmB,qBAAqB;YAC1B;YACA,OAAOC,WAAWX,KAAK,CAAC,IAAI,EAAEC;QAChC;IACF,EAAE,OAAM;QACNC,QAAQC,KAAK,CACX,CAAC,kBAAkB,EAAEC,sBAAsB,gJAAgJ,CAAC;IAEhM;IAEA,MAAMS,8BACJ;IACF,IAAI;QACF,MAAMC,qBAAqBnB,WAAWoB,iBAAiB;QACvD,gEAAgE;QAChEpB,WAAWoB,iBAAiB,GAAG,SAASA;YACtCxB,GAAGsB,6BAA6B;YAChC,OAAOC,mBAAmBd,KAAK,CAAC,IAAI,EAAEC;QACxC;IACF,EAAE,OAAM;QACNC,QAAQC,KAAK,CACX,CAAC,kBAAkB,EAAEU,4BAA4B,oHAAoH,CAAC;IAE1K;IAEA,MAAMG,gCACJ;IACF,IAAI;QACF,MAAMC,uBAAuBtB,WAAWuB,mBAAmB;QAC3D,gEAAgE;QAChEvB,WAAWuB,mBAAmB,GAAG,SAASA;YACxC3B,GAAGyB,+BAA+B;YAClC,OAAOC,qBAAqBjB,KAAK,CAAC,IAAI,EAAEC;QAC1C;IACF,EAAE,OAAM;QACNC,QAAQC,KAAK,CACX,CAAC,kBAAkB,EAAEa,8BAA8B,oHAAoH,CAAC;IAE5K;IAEA,MAAMG,4BACJ;IACF,IAAI;QACF,MAAMC,mBAAmBzB,WAAW0B,eAAe;QACnD1B,WAAW0B,eAAe,GAAG,SAASA;YACpC9B,GAAG4B,2BAA2B;YAC9B,OAAOC,iBAAiBpB,KAAK,CAAC,IAAI,EAAEC;QACtC;IACF,EAAE,OAAM;QACNC,QAAQC,KAAK,CACX,CAAC,kBAAkB,EAAEgB,0BAA0B,oHAAoH,CAAC;IAExK;AACF", "ignoreList": [0]}