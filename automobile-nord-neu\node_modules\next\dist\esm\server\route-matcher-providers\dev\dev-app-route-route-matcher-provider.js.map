{"version": 3, "sources": ["../../../../src/server/route-matcher-providers/dev/dev-app-route-route-matcher-provider.ts"], "sourcesContent": ["import type { <PERSON><PERSON>eader } from './helpers/file-reader/file-reader'\nimport type { Normalizer } from '../../normalizers/normalizer'\nimport { AppRouteRouteMatcher } from '../../route-matchers/app-route-route-matcher'\nimport { RouteKind } from '../../route-kind'\nimport { FileCacheRouteMatcherProvider } from './file-cache-route-matcher-provider'\nimport { isAppRouteRoute } from '../../../lib/is-app-route-route'\nimport { DevAppNormalizers } from '../../normalizers/built/app'\nimport {\n  isMetadataRouteFile,\n  isStaticMetadataRoute,\n} from '../../../lib/metadata/is-metadata-route'\nimport { normalizeMetadataPageToRoute } from '../../../lib/metadata/get-metadata-route'\nimport path from '../../../shared/lib/isomorphic/path'\n\nexport class DevAppRouteRouteMatcherProvider extends FileCacheRouteMatcherProvider<AppRouteRouteMatcher> {\n  private readonly normalizers: {\n    page: Normalizer\n    pathname: Normalizer\n    bundlePath: Normalizer\n  }\n  private readonly appDir: string\n  private readonly isTurbopack: boolean\n\n  constructor(\n    appDir: string,\n    extensions: ReadonlyArray<string>,\n    reader: FileReader,\n    isTurbopack: boolean\n  ) {\n    super(appDir, reader)\n\n    this.appDir = appDir\n    this.isTurbopack = isTurbopack\n    this.normalizers = new DevAppNormalizers(appDir, extensions, isTurbopack)\n  }\n\n  protected async transform(\n    files: ReadonlyArray<string>\n  ): Promise<ReadonlyArray<AppRouteRouteMatcher>> {\n    const matchers: Array<AppRouteRouteMatcher> = []\n    for (const filename of files) {\n      let page = this.normalizers.page.normalize(filename)\n\n      // If the file isn't a match for this matcher, then skip it.\n      if (!isAppRouteRoute(page)) continue\n\n      // Validate that this is not an ignored page.\n      if (page.includes('/_')) continue\n\n      // Turbopack uses the correct page name with the underscore normalized.\n      // TODO: Move implementation to packages/next/src/server/normalizers/built/app/app-page-normalizer.ts.\n      // The `includes('/_')` check above needs to be moved for that to work as otherwise `%5Fsegmentname`\n      // will result in `_segmentname` which hits that includes check and be skipped.\n      if (this.isTurbopack) {\n        page = page.replace(/%5F/g, '_')\n      }\n\n      const pathname = this.normalizers.pathname.normalize(filename)\n      const bundlePath = this.normalizers.bundlePath.normalize(filename)\n      const ext = path.extname(filename).slice(1)\n      const isEntryMetadataRouteFile = isMetadataRouteFile(\n        filename.replace(this.appDir, ''),\n        [ext],\n        true\n      )\n\n      if (isEntryMetadataRouteFile && !isStaticMetadataRoute(page)) {\n        // Matching dynamic metadata routes.\n        // Add 2 possibilities for both single and multiple routes:\n        {\n          // single:\n          // /sitemap.ts -> /sitemap.xml/route\n          // /icon.ts -> /icon/route\n          // We'll map the filename before normalization:\n          // sitemap.ts -> sitemap.xml/route.ts\n          // icon.ts -> icon/route.ts\n          const metadataPage = normalizeMetadataPageToRoute(page, false)\n          const metadataPathname = normalizeMetadataPageToRoute(pathname, false)\n          const metadataBundlePath = normalizeMetadataPageToRoute(\n            bundlePath,\n            false\n          )\n\n          const matcher = new AppRouteRouteMatcher({\n            kind: RouteKind.APP_ROUTE,\n            page: metadataPage,\n            pathname: metadataPathname,\n            bundlePath: metadataBundlePath,\n            filename,\n          })\n          matchers.push(matcher)\n        }\n        {\n          // multiple:\n          // /sitemap.ts -> /sitemap/[__metadata_id__]/route\n          // /icon.ts -> /icon/[__metadata_id__]/route\n          // We'll map the filename before normalization:\n          // sitemap.ts -> sitemap.xml/[__metadata_id__].ts\n          // icon.ts -> icon/[__metadata_id__].ts\n          const metadataPage = normalizeMetadataPageToRoute(page, true)\n          const metadataPathname = normalizeMetadataPageToRoute(pathname, true)\n          const metadataBundlePath = normalizeMetadataPageToRoute(\n            bundlePath,\n            true\n          )\n\n          const matcher = new AppRouteRouteMatcher({\n            kind: RouteKind.APP_ROUTE,\n            page: metadataPage,\n            pathname: metadataPathname,\n            bundlePath: metadataBundlePath,\n            filename,\n          })\n          matchers.push(matcher)\n        }\n      } else {\n        // Normal app routes and static metadata routes.\n        matchers.push(\n          new AppRouteRouteMatcher({\n            kind: RouteKind.APP_ROUTE,\n            page,\n            pathname,\n            bundlePath,\n            filename,\n          })\n        )\n      }\n    }\n\n    return matchers\n  }\n}\n"], "names": ["AppRouteRouteMatcher", "RouteKind", "FileCacheRouteMatcherProvider", "isAppRouteRoute", "DevAppNormalizers", "isMetadataRouteFile", "isStaticMetadataRoute", "normalizeMetadataPageToRoute", "path", "DevAppRouteRouteMatcherProvider", "constructor", "appDir", "extensions", "reader", "isTurbopack", "normalizers", "transform", "files", "matchers", "filename", "page", "normalize", "includes", "replace", "pathname", "bundlePath", "ext", "extname", "slice", "isEntryMetadataRouteFile", "metadataPage", "metadataPathname", "metadataBundlePath", "matcher", "kind", "APP_ROUTE", "push"], "mappings": "AAEA,SAASA,oBAAoB,QAAQ,+CAA8C;AACnF,SAASC,SAAS,QAAQ,mBAAkB;AAC5C,SAASC,6BAA6B,QAAQ,sCAAqC;AACnF,SAASC,eAAe,QAAQ,kCAAiC;AACjE,SAASC,iBAAiB,QAAQ,8BAA6B;AAC/D,SACEC,mBAAmB,EACnBC,qBAAqB,QAChB,0CAAyC;AAChD,SAASC,4BAA4B,QAAQ,2CAA0C;AACvF,OAAOC,UAAU,sCAAqC;AAEtD,OAAO,MAAMC,wCAAwCP;IASnDQ,YACEC,MAAc,EACdC,UAAiC,EACjCC,MAAkB,EAClBC,WAAoB,CACpB;QACA,KAAK,CAACH,QAAQE;QAEd,IAAI,CAACF,MAAM,GAAGA;QACd,IAAI,CAACG,WAAW,GAAGA;QACnB,IAAI,CAACC,WAAW,GAAG,IAAIX,kBAAkBO,QAAQC,YAAYE;IAC/D;IAEA,MAAgBE,UACdC,KAA4B,EACkB;QAC9C,MAAMC,WAAwC,EAAE;QAChD,KAAK,MAAMC,YAAYF,MAAO;YAC5B,IAAIG,OAAO,IAAI,CAACL,WAAW,CAACK,IAAI,CAACC,SAAS,CAACF;YAE3C,4DAA4D;YAC5D,IAAI,CAAChB,gBAAgBiB,OAAO;YAE5B,6CAA6C;YAC7C,IAAIA,KAAKE,QAAQ,CAAC,OAAO;YAEzB,uEAAuE;YACvE,sGAAsG;YACtG,oGAAoG;YACpG,+EAA+E;YAC/E,IAAI,IAAI,CAACR,WAAW,EAAE;gBACpBM,OAAOA,KAAKG,OAAO,CAAC,QAAQ;YAC9B;YAEA,MAAMC,WAAW,IAAI,CAACT,WAAW,CAACS,QAAQ,CAACH,SAAS,CAACF;YACrD,MAAMM,aAAa,IAAI,CAACV,WAAW,CAACU,UAAU,CAACJ,SAAS,CAACF;YACzD,MAAMO,MAAMlB,KAAKmB,OAAO,CAACR,UAAUS,KAAK,CAAC;YACzC,MAAMC,2BAA2BxB,oBAC/Bc,SAASI,OAAO,CAAC,IAAI,CAACZ,MAAM,EAAE,KAC9B;gBAACe;aAAI,EACL;YAGF,IAAIG,4BAA4B,CAACvB,sBAAsBc,OAAO;gBAC5D,oCAAoC;gBACpC,2DAA2D;gBAC3D;oBACE,UAAU;oBACV,oCAAoC;oBACpC,0BAA0B;oBAC1B,+CAA+C;oBAC/C,qCAAqC;oBACrC,2BAA2B;oBAC3B,MAAMU,eAAevB,6BAA6Ba,MAAM;oBACxD,MAAMW,mBAAmBxB,6BAA6BiB,UAAU;oBAChE,MAAMQ,qBAAqBzB,6BACzBkB,YACA;oBAGF,MAAMQ,UAAU,IAAIjC,qBAAqB;wBACvCkC,MAAMjC,UAAUkC,SAAS;wBACzBf,MAAMU;wBACNN,UAAUO;wBACVN,YAAYO;wBACZb;oBACF;oBACAD,SAASkB,IAAI,CAACH;gBAChB;gBACA;oBACE,YAAY;oBACZ,kDAAkD;oBAClD,4CAA4C;oBAC5C,+CAA+C;oBAC/C,iDAAiD;oBACjD,uCAAuC;oBACvC,MAAMH,eAAevB,6BAA6Ba,MAAM;oBACxD,MAAMW,mBAAmBxB,6BAA6BiB,UAAU;oBAChE,MAAMQ,qBAAqBzB,6BACzBkB,YACA;oBAGF,MAAMQ,UAAU,IAAIjC,qBAAqB;wBACvCkC,MAAMjC,UAAUkC,SAAS;wBACzBf,MAAMU;wBACNN,UAAUO;wBACVN,YAAYO;wBACZb;oBACF;oBACAD,SAASkB,IAAI,CAACH;gBAChB;YACF,OAAO;gBACL,gDAAgD;gBAChDf,SAASkB,IAAI,CACX,IAAIpC,qBAAqB;oBACvBkC,MAAMjC,UAAUkC,SAAS;oBACzBf;oBACAI;oBACAC;oBACAN;gBACF;YAEJ;QACF;QAEA,OAAOD;IACT;AACF", "ignoreList": [0]}