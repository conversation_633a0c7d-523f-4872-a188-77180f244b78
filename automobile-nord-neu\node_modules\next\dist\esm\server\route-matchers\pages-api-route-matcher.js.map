{"version": 3, "sources": ["../../../src/server/route-matchers/pages-api-route-matcher.ts"], "sourcesContent": ["import type { PagesAPIRouteDefinition } from '../route-definitions/pages-api-route-definition'\nimport { LocaleRouteMatcher } from './locale-route-matcher'\nimport { RouteMatcher } from './route-matcher'\n\nexport class PagesAPIRouteMatcher extends RouteMatcher<PagesAPIRouteDefinition> {}\n\nexport class PagesAPILocaleRouteMatcher extends LocaleRouteMatcher<PagesAPIRouteDefinition> {}\n"], "names": ["LocaleRouteMatcher", "RouteMatcher", "PagesAPIRouteMatcher", "PagesAPILocaleRouteMatcher"], "mappings": "AACA,SAASA,kBAAkB,QAAQ,yBAAwB;AAC3D,SAASC,YAAY,QAAQ,kBAAiB;AAE9C,OAAO,MAAMC,6BAA6BD;AAAuC;AAEjF,OAAO,MAAME,mCAAmCH;AAA6C", "ignoreList": [0]}