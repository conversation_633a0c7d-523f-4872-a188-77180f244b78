{"version": 3, "sources": ["../../../../../../src/server/route-modules/app-page/vendored/rsc/entrypoints.ts"], "sourcesContent": ["import * as React from 'react'\nimport * as ReactD<PERSON> from 'react-dom'\nimport * as ReactJsxDevRuntime from 'react/jsx-dev-runtime'\nimport * as ReactJsxRuntime from 'react/jsx-runtime'\nimport * as ReactCompilerRuntime from 'react/compiler-runtime'\n\nfunction getAltProxyForBindingsDEV(\n  type: 'Turbopack' | 'Webpack',\n  pkg:\n    | 'react-server-dom-turbopack/server'\n    | 'react-server-dom-turbopack/static'\n    | 'react-server-dom-webpack/server'\n    | 'react-server-dom-webpack/static'\n) {\n  if (process.env.NODE_ENV === 'development') {\n    const altType = type === 'Turbopack' ? 'Webpack' : 'Turbopack'\n    const altPkg = pkg.replace(new RegExp(type, 'gi'), altType.toLowerCase())\n\n    return new Proxy(\n      {},\n      {\n        get(_, prop: string) {\n          throw new Error(\n            `Expected to use ${type} bindings (${pkg}) for React but the current process is referencing '${prop}' from the ${altType} bindings (${altPkg}). This is likely a bug in our integration of the Next.js server runtime.`\n          )\n        },\n      }\n    )\n  }\n}\n\nlet ReactServerDOMTurbopackServer, ReactServerDOMWebpackServer\nlet ReactServerDOMTurbopackStatic, ReactServerDOMWebpackStatic\n\nif (process.env.TURBOPACK) {\n  ReactServerDOMTurbopackServer =\n    // @ts-expect-error -- TODO: Add types\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    require('react-server-dom-turbopack/server') as typeof import('react-server-dom-turbopack/server')\n  if (process.env.NODE_ENV === 'development') {\n    ReactServerDOMWebpackServer = getAltProxyForBindingsDEV(\n      'Turbopack',\n      'react-server-dom-turbopack/server'\n    )\n  }\n  ReactServerDOMTurbopackStatic =\n    // @ts-expect-error -- TODO: Add types\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    require('react-server-dom-turbopack/static') as typeof import('react-server-dom-turbopack/static')\n  if (process.env.NODE_ENV === 'development') {\n    ReactServerDOMWebpackStatic = getAltProxyForBindingsDEV(\n      'Turbopack',\n      'react-server-dom-turbopack/static'\n    )\n  }\n} else {\n  ReactServerDOMWebpackServer =\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    require('react-server-dom-webpack/server') as typeof import('react-server-dom-webpack/server')\n  if (process.env.NODE_ENV === 'development') {\n    ReactServerDOMTurbopackServer = getAltProxyForBindingsDEV(\n      'Webpack',\n      'react-server-dom-webpack/server'\n    )\n  }\n  ReactServerDOMWebpackStatic =\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    require('react-server-dom-webpack/static') as typeof import('react-server-dom-webpack/static')\n  if (process.env.NODE_ENV === 'development') {\n    ReactServerDOMTurbopackStatic = getAltProxyForBindingsDEV(\n      'Webpack',\n      'react-server-dom-webpack/static'\n    )\n  }\n}\n\nexport {\n  React,\n  ReactJsxDevRuntime,\n  ReactJsxRuntime,\n  ReactCompilerRuntime,\n  ReactDOM,\n  ReactServerDOMTurbopackServer,\n  ReactServerDOMTurbopackStatic,\n  ReactServerDOMWebpackServer,\n  ReactServerDOMWebpackStatic,\n}\n"], "names": ["React", "ReactDOM", "ReactJsxDevRuntime", "ReactJsxRuntime", "ReactCompilerRuntime", "getAltProxyForBindingsDEV", "type", "pkg", "process", "env", "NODE_ENV", "altType", "altPkg", "replace", "RegExp", "toLowerCase", "Proxy", "get", "_", "prop", "Error", "ReactServerDOMTurbopackServer", "ReactServerDOMWebpackServer", "ReactServerDOMTurbopackStatic", "ReactServerDOMWebpackStatic", "TURBOPACK", "require"], "mappings": "AAAA,YAAYA,WAAW,QAAO;AAC9B,YAAYC,cAAc,YAAW;AACrC,YAAYC,wBAAwB,wBAAuB;AAC3D,YAAYC,qBAAqB,oBAAmB;AACpD,YAAYC,0BAA0B,yBAAwB;AAE9D,SAASC,0BACPC,IAA6B,EAC7BC,GAIqC;IAErC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAMC,UAAUL,SAAS,cAAc,YAAY;QACnD,MAAMM,SAASL,IAAIM,OAAO,CAAC,IAAIC,OAAOR,MAAM,OAAOK,QAAQI,WAAW;QAEtE,OAAO,IAAIC,MACT,CAAC,GACD;YACEC,KAAIC,CAAC,EAAEC,IAAY;gBACjB,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,gBAAgB,EAAEd,KAAK,WAAW,EAAEC,IAAI,oDAAoD,EAAEY,KAAK,WAAW,EAAER,QAAQ,WAAW,EAAEC,OAAO,yEAAyE,CAAC,GADnN,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IAEJ;AACF;AAEA,IAAIS,+BAA+BC;AACnC,IAAIC,+BAA+BC;AAEnC,IAAIhB,QAAQC,GAAG,CAACgB,SAAS,EAAE;IACzBJ,gCACE,sCAAsC;IACtC,6DAA6D;IAC7DK,QAAQ;IACV,IAAIlB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CY,8BAA8BjB,0BAC5B,aACA;IAEJ;IACAkB,gCACE,sCAAsC;IACtC,6DAA6D;IAC7DG,QAAQ;IACV,IAAIlB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1Cc,8BAA8BnB,0BAC5B,aACA;IAEJ;AACF,OAAO;IACLiB,8BACE,6DAA6D;IAC7DI,QAAQ;IACV,IAAIlB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CW,gCAAgChB,0BAC9B,WACA;IAEJ;IACAmB,8BACE,6DAA6D;IAC7DE,QAAQ;IACV,IAAIlB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1Ca,gCAAgClB,0BAC9B,WACA;IAEJ;AACF;AAEA,SACEL,KAAK,EACLE,kBAAkB,EAClBC,eAAe,EACfC,oBAAoB,EACpBH,QAAQ,EACRoB,6BAA6B,EAC7BE,6BAA6B,EAC7BD,2BAA2B,EAC3BE,2BAA2B,KAC5B", "ignoreList": [0]}