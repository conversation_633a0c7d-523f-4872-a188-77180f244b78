{"version": 3, "sources": ["../../../src/server/route-modules/checks.ts"], "sourcesContent": ["import type { AppRouteRouteModule } from './app-route/module'\nimport type { AppPageRouteModule } from './app-page/module'\nimport type { PagesRouteModule } from './pages/module'\nimport type { PagesAPIRouteModule } from './pages-api/module'\n\nimport type { RouteModule } from './route-module'\n\nimport { RouteKind } from '../route-kind'\n\nexport function isAppRouteRouteModule(\n  routeModule: RouteModule\n): routeModule is AppRouteRouteModule {\n  return routeModule.definition.kind === RouteKind.APP_ROUTE\n}\n\nexport function isAppPageRouteModule(\n  routeModule: RouteModule\n): routeModule is AppPageRouteModule {\n  return routeModule.definition.kind === RouteKind.APP_PAGE\n}\n\nexport function isPagesRouteModule(\n  routeModule: RouteModule\n): routeModule is PagesRouteModule {\n  return routeModule.definition.kind === RouteKind.PAGES\n}\n\nexport function isPagesAPIRouteModule(\n  routeModule: RouteModule\n): routeModule is PagesAPIRouteModule {\n  return routeModule.definition.kind === RouteKind.PAGES_API\n}\n"], "names": ["RouteKind", "isAppRouteRouteModule", "routeModule", "definition", "kind", "APP_ROUTE", "isAppPageRouteModule", "APP_PAGE", "isPagesRouteModule", "PAGES", "isPagesAPIRouteModule", "PAGES_API"], "mappings": "AAOA,SAASA,SAAS,QAAQ,gBAAe;AAEzC,OAAO,SAASC,sBACdC,WAAwB;IAExB,OAAOA,YAAYC,UAAU,CAACC,IAAI,KAAKJ,UAAUK,SAAS;AAC5D;AAEA,OAAO,SAASC,qBACdJ,WAAwB;IAExB,OAAOA,YAAYC,UAAU,CAACC,IAAI,KAAKJ,UAAUO,QAAQ;AAC3D;AAEA,OAAO,SAASC,mBACdN,WAAwB;IAExB,OAAOA,YAAYC,UAAU,CAACC,IAAI,KAAKJ,UAAUS,KAAK;AACxD;AAEA,OAAO,SAASC,sBACdR,WAAwB;IAExB,OAAOA,YAAYC,UAAU,CAACC,IAAI,KAAKJ,UAAUW,SAAS;AAC5D", "ignoreList": [0]}