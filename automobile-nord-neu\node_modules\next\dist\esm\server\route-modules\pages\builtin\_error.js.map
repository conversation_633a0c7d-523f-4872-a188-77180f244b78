{"version": 3, "sources": ["../../../../../src/server/route-modules/pages/builtin/_error.tsx"], "sourcesContent": ["import Document from '../../../../pages/_document'\nimport App from '../../../../pages/_app'\nimport { RouteKind } from '../../../route-kind'\n\nimport * as moduleError from '../../../../pages/_error'\n\nimport PagesRouteModule from '../module'\n\nexport const routeModule = new PagesRouteModule({\n  // TODO: add descriptor for internal error page\n  definition: {\n    kind: RouteKind.PAGES,\n    page: '/_error',\n    pathname: '/_error',\n    filename: '',\n    bundlePath: '',\n  },\n  distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n  projectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n  components: {\n    App,\n    Document,\n  },\n  userland: moduleError,\n})\n"], "names": ["Document", "App", "RouteKind", "moduleError", "PagesRouteModule", "routeModule", "definition", "kind", "PAGES", "page", "pathname", "filename", "bundlePath", "distDir", "process", "env", "__NEXT_RELATIVE_DIST_DIR", "projectDir", "__NEXT_RELATIVE_PROJECT_DIR", "components", "userland"], "mappings": "AAAA,OAAOA,cAAc,8BAA6B;AAClD,OAAOC,SAAS,yBAAwB;AACxC,SAASC,SAAS,QAAQ,sBAAqB;AAE/C,YAAYC,iBAAiB,2BAA0B;AAEvD,OAAOC,sBAAsB,YAAW;AAExC,OAAO,MAAMC,cAAc,IAAID,iBAAiB;IAC9C,+CAA+C;IAC/CE,YAAY;QACVC,MAAML,UAAUM,KAAK;QACrBC,MAAM;QACNC,UAAU;QACVC,UAAU;QACVC,YAAY;IACd;IACAC,SAASC,QAAQC,GAAG,CAACC,wBAAwB,IAAI;IACjDC,YAAYH,QAAQC,GAAG,CAACG,2BAA2B,IAAI;IACvDC,YAAY;QACVlB;QACAD;IACF;IACAoB,UAAUjB;AACZ,GAAE", "ignoreList": [0]}