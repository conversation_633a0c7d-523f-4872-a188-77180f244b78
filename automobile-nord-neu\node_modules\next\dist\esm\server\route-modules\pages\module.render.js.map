{"version": 3, "sources": ["../../../../src/server/route-modules/pages/module.render.ts"], "sourcesContent": ["import type { PagesRender } from '../../render'\n\nexport const lazyRenderPagesPage: PagesRender = (...args) => {\n  if (process.env.NEXT_MINIMAL) {\n    throw new Error(\"Can't use lazyRenderPagesPage in minimal mode\")\n  } else {\n    const render: PagesRender = (\n      require('./module.compiled') as typeof import('./module.compiled')\n    ).renderToHTML\n\n    return render(...args)\n  }\n}\n"], "names": ["lazyRenderPagesPage", "args", "process", "env", "NEXT_MINIMAL", "Error", "render", "require", "renderToHTML"], "mappings": "AAEA,OAAO,MAAMA,sBAAmC,CAAC,GAAGC;IAClD,IAAIC,QAAQC,GAAG,CAACC,YAAY,EAAE;QAC5B,MAAM,qBAA0D,CAA1D,IAAIC,MAAM,kDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAyD;IACjE,OAAO;QACL,MAAMC,SAAsB,AAC1BC,QAAQ,qBACRC,YAAY;QAEd,OAAOF,UAAUL;IACnB;AACF,EAAC", "ignoreList": [0]}