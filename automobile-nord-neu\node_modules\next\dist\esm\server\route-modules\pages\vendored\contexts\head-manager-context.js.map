{"version": 3, "sources": ["../../../../../../src/server/route-modules/pages/vendored/contexts/head-manager-context.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].HeadManagerContext\n"], "names": ["module", "exports", "require", "vendored", "HeadManagerContext"], "mappings": "AAAAA,OAAOC,OAAO,GAAG,AACfC,QAAQ,yBACRC,QAAQ,CAAC,WAAW,CAACC,kBAAkB", "ignoreList": [0]}