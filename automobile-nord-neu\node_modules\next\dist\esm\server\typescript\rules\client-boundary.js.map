{"version": 3, "sources": ["../../../../src/server/typescript/rules/client-boundary.ts"], "sourcesContent": ["// This module provides intellisense for all components that has the `\"use client\"` directive.\n\nimport { NEXT_TS_ERRORS } from '../constant'\nimport { getTs, getTypeChecker } from '../utils'\nimport type tsModule from 'typescript/lib/tsserverlibrary'\n\nconst clientBoundary = {\n  getSemanticDiagnosticsForExportVariableStatement(\n    source: tsModule.SourceFile,\n    node: tsModule.VariableStatement\n  ) {\n    const ts = getTs()\n\n    const diagnostics: tsModule.Diagnostic[] = []\n\n    if (ts.isVariableDeclarationList(node.declarationList)) {\n      for (const declaration of node.declarationList.declarations) {\n        const initializer = declaration.initializer\n        if (initializer && ts.isArrowFunction(initializer)) {\n          diagnostics.push(\n            ...clientBoundary.getSemanticDiagnosticsForFunctionExport(\n              source,\n              initializer\n            )\n          )\n        }\n      }\n    }\n\n    return diagnostics\n  },\n\n  getSemanticDiagnosticsForFunctionExport(\n    source: tsModule.SourceFile,\n    node: tsModule.FunctionDeclaration | tsModule.ArrowFunction\n  ) {\n    const ts = getTs()\n    const typeChecker = getTypeChecker()\n    if (!typeChecker) return []\n\n    const diagnostics: tsModule.Diagnostic[] = []\n\n    const isErrorFile = /[\\\\/]error\\.tsx?$/.test(source.fileName)\n    const isGlobalErrorFile = /[\\\\/]global-error\\.tsx?$/.test(source.fileName)\n\n    const props = node.parameters?.[0]\n    if (props) {\n      const propsType = typeChecker.getTypeAtLocation(props)\n      const typeNode = propsType.symbol?.getDeclarations()?.[0]\n\n      if (typeNode && ts.isTypeLiteralNode(typeNode)) {\n        for (const member of typeNode.members) {\n          if (ts.isPropertySignature(member)) {\n            const propName = member.name.getText()\n            const propType = member.type\n\n            if (propType) {\n              const propTypeInfo = typeChecker.getTypeAtLocation(propType)\n              const typeDeclarationNode =\n                propTypeInfo.symbol?.getDeclarations()?.[0]\n\n              if (typeDeclarationNode) {\n                if (\n                  ts.isFunctionTypeNode(typeDeclarationNode) ||\n                  // someFunc(): void\n                  ts.isMethodSignature(typeDeclarationNode)\n                ) {\n                  // By convention, props named \"action\" can accept functions since we\n                  // assume these are Server Actions. Structurally, there's no\n                  // difference between a Server Action and a normal function until\n                  // TypeScript exposes directives in the type of a function. This\n                  // will miss accidentally passing normal functions but a false\n                  // negative is better than a false positive given how frequent the\n                  // false-positive would be.\n                  const maybeServerAction =\n                    propName === 'action' || /.+Action$/.test(propName)\n\n                  // There's a special case for the error file that the `reset` prop\n                  // is allowed to be a function:\n                  // https://github.com/vercel/next.js/issues/46573\n                  const isErrorReset =\n                    (isErrorFile || isGlobalErrorFile) && propName === 'reset'\n\n                  if (!maybeServerAction && !isErrorReset) {\n                    diagnostics.push({\n                      file: source,\n                      category: ts.DiagnosticCategory.Warning,\n                      code: NEXT_TS_ERRORS.INVALID_CLIENT_ENTRY_PROP,\n                      messageText:\n                        `Props must be serializable for components in the \"use client\" entry file. ` +\n                        `\"${propName}\" is a function that's not a Server Action. ` +\n                        `Rename \"${propName}\" either to \"action\" or have its name end with \"Action\" e.g. \"${propName}Action\" to indicate it is a Server Action.`,\n                      start: propType.getStart(),\n                      length: propType.getWidth(),\n                    })\n                  }\n                } else if (\n                  // Show warning for not serializable props.\n                  ts.isConstructorTypeNode(typeDeclarationNode) ||\n                  ts.isClassDeclaration(typeDeclarationNode)\n                ) {\n                  diagnostics.push({\n                    file: source,\n                    category: ts.DiagnosticCategory.Warning,\n                    code: NEXT_TS_ERRORS.INVALID_CLIENT_ENTRY_PROP,\n                    messageText: `Props must be serializable for components in the \"use client\" entry file, \"${propName}\" is invalid.`,\n                    start: propType.getStart(),\n                    length: propType.getWidth(),\n                  })\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    return diagnostics\n  },\n}\n\nexport default clientBoundary\n"], "names": ["NEXT_TS_ERRORS", "getTs", "getType<PERSON><PERSON>cker", "clientBoundary", "getSemanticDiagnosticsForExportVariableStatement", "source", "node", "ts", "diagnostics", "isVariableDeclarationList", "declarationList", "declaration", "declarations", "initializer", "isArrowFunction", "push", "getSemanticDiagnosticsForFunctionExport", "typeC<PERSON>cker", "isErrorFile", "test", "fileName", "isGlobalErrorFile", "props", "parameters", "propsType", "getTypeAtLocation", "typeNode", "symbol", "getDeclarations", "isTypeLiteralNode", "member", "members", "isPropertySignature", "propName", "name", "getText", "propType", "type", "propTypeInfo", "typeDeclarationNode", "isFunctionTypeNode", "isMethodSignature", "maybeServerAction", "isErrorReset", "file", "category", "DiagnosticCategory", "Warning", "code", "INVALID_CLIENT_ENTRY_PROP", "messageText", "start", "getStart", "length", "getWidth", "isConstructorTypeNode", "isClassDeclaration"], "mappings": "AAAA,8FAA8F;AAE9F,SAASA,cAAc,QAAQ,cAAa;AAC5C,SAASC,KAAK,EAAEC,cAAc,QAAQ,WAAU;AAGhD,MAAMC,iBAAiB;IACrBC,kDACEC,MAA2B,EAC3BC,IAAgC;QAEhC,MAAMC,KAAKN;QAEX,MAAMO,cAAqC,EAAE;QAE7C,IAAID,GAAGE,yBAAyB,CAACH,KAAKI,eAAe,GAAG;YACtD,KAAK,MAAMC,eAAeL,KAAKI,eAAe,CAACE,YAAY,CAAE;gBAC3D,MAAMC,cAAcF,YAAYE,WAAW;gBAC3C,IAAIA,eAAeN,GAAGO,eAAe,CAACD,cAAc;oBAClDL,YAAYO,IAAI,IACXZ,eAAea,uCAAuC,CACvDX,QACAQ;gBAGN;YACF;QACF;QAEA,OAAOL;IACT;IAEAQ,yCACEX,MAA2B,EAC3BC,IAA2D;YAW7CA;QATd,MAAMC,KAAKN;QACX,MAAMgB,cAAcf;QACpB,IAAI,CAACe,aAAa,OAAO,EAAE;QAE3B,MAAMT,cAAqC,EAAE;QAE7C,MAAMU,cAAc,oBAAoBC,IAAI,CAACd,OAAOe,QAAQ;QAC5D,MAAMC,oBAAoB,2BAA2BF,IAAI,CAACd,OAAOe,QAAQ;QAEzE,MAAME,SAAQhB,mBAAAA,KAAKiB,UAAU,qBAAfjB,gBAAiB,CAAC,EAAE;QAClC,IAAIgB,OAAO;gBAEQE,mCAAAA;YADjB,MAAMA,YAAYP,YAAYQ,iBAAiB,CAACH;YAChD,MAAMI,YAAWF,oBAAAA,UAAUG,MAAM,sBAAhBH,oCAAAA,kBAAkBI,eAAe,uBAAjCJ,iCAAqC,CAAC,EAAE;YAEzD,IAAIE,YAAYnB,GAAGsB,iBAAiB,CAACH,WAAW;gBAC9C,KAAK,MAAMI,UAAUJ,SAASK,OAAO,CAAE;oBACrC,IAAIxB,GAAGyB,mBAAmB,CAACF,SAAS;wBAClC,MAAMG,WAAWH,OAAOI,IAAI,CAACC,OAAO;wBACpC,MAAMC,WAAWN,OAAOO,IAAI;wBAE5B,IAAID,UAAU;gCAGVE,sCAAAA;4BAFF,MAAMA,eAAerB,YAAYQ,iBAAiB,CAACW;4BACnD,MAAMG,uBACJD,uBAAAA,aAAaX,MAAM,sBAAnBW,uCAAAA,qBAAqBV,eAAe,uBAApCU,oCAAwC,CAAC,EAAE;4BAE7C,IAAIC,qBAAqB;gCACvB,IACEhC,GAAGiC,kBAAkB,CAACD,wBACtB,mBAAmB;gCACnBhC,GAAGkC,iBAAiB,CAACF,sBACrB;oCACA,oEAAoE;oCACpE,4DAA4D;oCAC5D,iEAAiE;oCACjE,gEAAgE;oCAChE,8DAA8D;oCAC9D,kEAAkE;oCAClE,2BAA2B;oCAC3B,MAAMG,oBACJT,aAAa,YAAY,YAAYd,IAAI,CAACc;oCAE5C,kEAAkE;oCAClE,+BAA+B;oCAC/B,iDAAiD;oCACjD,MAAMU,eACJ,AAACzB,CAAAA,eAAeG,iBAAgB,KAAMY,aAAa;oCAErD,IAAI,CAACS,qBAAqB,CAACC,cAAc;wCACvCnC,YAAYO,IAAI,CAAC;4CACf6B,MAAMvC;4CACNwC,UAAUtC,GAAGuC,kBAAkB,CAACC,OAAO;4CACvCC,MAAMhD,eAAeiD,yBAAyB;4CAC9CC,aACE,CAAC,0EAA0E,CAAC,GAC5E,CAAC,CAAC,EAAEjB,SAAS,4CAA4C,CAAC,GAC1D,CAAC,QAAQ,EAAEA,SAAS,8DAA8D,EAAEA,SAAS,0CAA0C,CAAC;4CAC1IkB,OAAOf,SAASgB,QAAQ;4CACxBC,QAAQjB,SAASkB,QAAQ;wCAC3B;oCACF;gCACF,OAAO,IACL,2CAA2C;gCAC3C/C,GAAGgD,qBAAqB,CAAChB,wBACzBhC,GAAGiD,kBAAkB,CAACjB,sBACtB;oCACA/B,YAAYO,IAAI,CAAC;wCACf6B,MAAMvC;wCACNwC,UAAUtC,GAAGuC,kBAAkB,CAACC,OAAO;wCACvCC,MAAMhD,eAAeiD,yBAAyB;wCAC9CC,aAAa,CAAC,2EAA2E,EAAEjB,SAAS,aAAa,CAAC;wCAClHkB,OAAOf,SAASgB,QAAQ;wCACxBC,QAAQjB,SAASkB,QAAQ;oCAC3B;gCACF;4BACF;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO9C;IACT;AACF;AAEA,eAAeL,eAAc", "ignoreList": [0]}