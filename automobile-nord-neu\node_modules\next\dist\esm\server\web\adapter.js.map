{"version": 3, "sources": ["../../../src/server/web/adapter.ts"], "sourcesContent": ["import type { RequestD<PERSON>, FetchEventResult } from './types'\nimport type { RequestInit } from './spec-extension/request'\nimport { PageSignatureError } from './error'\nimport { fromNodeOutgoingHttpHeaders, normalizeNextQueryParam } from './utils'\nimport {\n  NextFetchEvent,\n  getWaitUntilPromiseFromEvent,\n} from './spec-extension/fetch-event'\nimport { NextRequest } from './spec-extension/request'\nimport { NextResponse } from './spec-extension/response'\nimport {\n  parseRelativeURL,\n  getRelativeURL,\n} from '../../shared/lib/router/utils/relativize-url'\nimport { NextURL } from './next-url'\nimport { stripInternalSearchParams } from '../internal-utils'\nimport { normalizeRscURL } from '../../shared/lib/router/utils/app-paths'\nimport {\n  FLIGHT_HEADERS,\n  NEXT_REWRITTEN_PATH_HEADER,\n  NEXT_REWRITTEN_QUERY_HEADER,\n  RSC_HEADER,\n} from '../../client/components/app-router-headers'\nimport { ensureInstrumentationRegistered } from './globals'\nimport { createRequestStoreForAPI } from '../async-storage/request-store'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport { createWorkStore } from '../async-storage/work-store'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { NEXT_ROUTER_PREFETCH_HEADER } from '../../client/components/app-router-headers'\nimport { getTracer } from '../lib/trace/tracer'\nimport type { TextMapGetter } from 'next/dist/compiled/@opentelemetry/api'\nimport { MiddlewareSpan } from '../lib/trace/constants'\nimport { CloseController } from './web-on-close'\nimport { getEdgePreviewProps } from './get-edge-preview-props'\nimport { getBuiltinRequestContext } from '../after/builtin-request-context'\nimport { getImplicitTags } from '../lib/implicit-tags'\n\nexport class NextRequestHint extends NextRequest {\n  sourcePage: string\n  fetchMetrics: FetchEventResult['fetchMetrics'] | undefined\n\n  constructor(params: {\n    init: RequestInit\n    input: Request | string\n    page: string\n  }) {\n    super(params.input, params.init)\n    this.sourcePage = params.page\n  }\n\n  get request() {\n    throw new PageSignatureError({ page: this.sourcePage })\n  }\n\n  respondWith() {\n    throw new PageSignatureError({ page: this.sourcePage })\n  }\n\n  waitUntil() {\n    throw new PageSignatureError({ page: this.sourcePage })\n  }\n}\n\nconst headersGetter: TextMapGetter<Headers> = {\n  keys: (headers) => Array.from(headers.keys()),\n  get: (headers, key) => headers.get(key) ?? undefined,\n}\n\nexport type AdapterOptions = {\n  handler: (req: NextRequestHint, event: NextFetchEvent) => Promise<Response>\n  page: string\n  request: RequestData\n  IncrementalCache?: typeof import('../lib/incremental-cache').IncrementalCache\n  incrementalCacheHandler?: typeof import('../lib/incremental-cache').CacheHandler\n  bypassNextUrl?: boolean\n}\n\nlet propagator: <T>(request: NextRequestHint, fn: () => T) => T = (\n  request,\n  fn\n) => {\n  const tracer = getTracer()\n  return tracer.withPropagatedContext(request.headers, fn, headersGetter)\n}\n\nlet testApisIntercepted = false\n\nfunction ensureTestApisIntercepted() {\n  if (!testApisIntercepted) {\n    testApisIntercepted = true\n    if (process.env.NEXT_PRIVATE_TEST_PROXY === 'true') {\n      const { interceptTestApis, wrapRequestHandler } =\n        // eslint-disable-next-line @next/internal/typechecked-require -- experimental/testmode is not built ins next/dist/esm\n        require('next/dist/experimental/testmode/server-edge') as typeof import('../../experimental/testmode/server-edge')\n      interceptTestApis()\n      propagator = wrapRequestHandler(propagator)\n    }\n  }\n}\n\nexport async function adapter(\n  params: AdapterOptions\n): Promise<FetchEventResult> {\n  ensureTestApisIntercepted()\n  await ensureInstrumentationRegistered()\n\n  // TODO-APP: use explicit marker for this\n  const isEdgeRendering =\n    typeof (globalThis as any).__BUILD_MANIFEST !== 'undefined'\n\n  params.request.url = normalizeRscURL(params.request.url)\n\n  const requestURL = params.bypassNextUrl\n    ? new URL(params.request.url)\n    : new NextURL(params.request.url, {\n        headers: params.request.headers,\n        nextConfig: params.request.nextConfig,\n      })\n\n  // Iterator uses an index to keep track of the current iteration. Because of deleting and appending below we can't just use the iterator.\n  // Instead we use the keys before iteration.\n  const keys = [...requestURL.searchParams.keys()]\n  for (const key of keys) {\n    const value = requestURL.searchParams.getAll(key)\n\n    const normalizedKey = normalizeNextQueryParam(key)\n    if (normalizedKey) {\n      requestURL.searchParams.delete(normalizedKey)\n      for (const val of value) {\n        requestURL.searchParams.append(normalizedKey, val)\n      }\n      requestURL.searchParams.delete(key)\n    }\n  }\n\n  // Ensure users only see page requests, never data requests.\n  let buildId = process.env.__NEXT_BUILD_ID || ''\n  if ('buildId' in requestURL) {\n    buildId = (requestURL as NextURL).buildId || ''\n    requestURL.buildId = ''\n  }\n\n  const requestHeaders = fromNodeOutgoingHttpHeaders(params.request.headers)\n  const isNextDataRequest = requestHeaders.has('x-nextjs-data')\n  const isRSCRequest = requestHeaders.get(RSC_HEADER) === '1'\n\n  if (isNextDataRequest && requestURL.pathname === '/index') {\n    requestURL.pathname = '/'\n  }\n\n  const flightHeaders = new Map()\n\n  // Headers should only be stripped for middleware\n  if (!isEdgeRendering) {\n    for (const header of FLIGHT_HEADERS) {\n      const key = header.toLowerCase()\n      const value = requestHeaders.get(key)\n      if (value !== null) {\n        flightHeaders.set(key, value)\n        requestHeaders.delete(key)\n      }\n    }\n  }\n\n  const normalizeURL = process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE\n    ? new URL(params.request.url)\n    : requestURL\n\n  const request = new NextRequestHint({\n    page: params.page,\n    // Strip internal query parameters off the request.\n    input: stripInternalSearchParams(normalizeURL).toString(),\n    init: {\n      body: params.request.body,\n      headers: requestHeaders,\n      method: params.request.method,\n      nextConfig: params.request.nextConfig,\n      signal: params.request.signal,\n    },\n  })\n\n  /**\n   * This allows to identify the request as a data request. The user doesn't\n   * need to know about this property neither use it. We add it for testing\n   * purposes.\n   */\n  if (isNextDataRequest) {\n    Object.defineProperty(request, '__isData', {\n      enumerable: false,\n      value: true,\n    })\n  }\n\n  if (\n    // If we are inside of the next start sandbox\n    // leverage the shared instance if not we need\n    // to create a fresh cache instance each time\n    !(globalThis as any).__incrementalCacheShared &&\n    (params as any).IncrementalCache\n  ) {\n    ;(globalThis as any).__incrementalCache = new (\n      params as {\n        IncrementalCache: typeof import('../lib/incremental-cache').IncrementalCache\n      }\n    ).IncrementalCache({\n      CurCacheHandler: params.incrementalCacheHandler,\n      minimalMode: process.env.NODE_ENV !== 'development',\n      fetchCacheKeyPrefix: process.env.__NEXT_FETCH_CACHE_KEY_PREFIX,\n      dev: process.env.NODE_ENV === 'development',\n      requestHeaders: params.request.headers as any,\n\n      getPrerenderManifest: () => {\n        return {\n          version: -1 as any, // letting us know this doesn't conform to spec\n          routes: {},\n          dynamicRoutes: {},\n          notFoundRoutes: [],\n          preview: getEdgePreviewProps(),\n        }\n      },\n    })\n  }\n\n  // if we're in an edge runtime sandbox, we should use the waitUntil\n  // that we receive from the enclosing NextServer\n  const outerWaitUntil =\n    params.request.waitUntil ?? getBuiltinRequestContext()?.waitUntil\n\n  const event = new NextFetchEvent({\n    request,\n    page: params.page,\n    context: outerWaitUntil ? { waitUntil: outerWaitUntil } : undefined,\n  })\n  let response\n  let cookiesFromResponse\n\n  response = await propagator(request, () => {\n    // we only care to make async storage available for middleware\n    const isMiddleware =\n      params.page === '/middleware' || params.page === '/src/middleware'\n\n    if (isMiddleware) {\n      // if we're in an edge function, we only get a subset of `nextConfig` (no `experimental`),\n      // so we have to inject it via DefinePlugin.\n      // in `next start` this will be passed normally (see `NextNodeServer.runMiddleware`).\n\n      const waitUntil = event.waitUntil.bind(event)\n      const closeController = new CloseController()\n\n      return getTracer().trace(\n        MiddlewareSpan.execute,\n        {\n          spanName: `middleware ${request.method} ${request.nextUrl.pathname}`,\n          attributes: {\n            'http.target': request.nextUrl.pathname,\n            'http.method': request.method,\n          },\n        },\n        async () => {\n          try {\n            const onUpdateCookies = (cookies: Array<string>) => {\n              cookiesFromResponse = cookies\n            }\n            const previewProps = getEdgePreviewProps()\n            const page = '/' // Fake Work\n            const fallbackRouteParams = null\n\n            const implicitTags = await getImplicitTags(\n              page,\n              request.nextUrl,\n              fallbackRouteParams\n            )\n\n            const requestStore = createRequestStoreForAPI(\n              request,\n              request.nextUrl,\n              implicitTags,\n              onUpdateCookies,\n              previewProps\n            )\n\n            const workStore = createWorkStore({\n              page,\n              fallbackRouteParams,\n              renderOpts: {\n                cacheLifeProfiles:\n                  params.request.nextConfig?.experimental?.cacheLife,\n                experimental: {\n                  isRoutePPREnabled: false,\n                  dynamicIO: false,\n                  authInterrupts:\n                    !!params.request.nextConfig?.experimental?.authInterrupts,\n                },\n                supportsDynamicResponse: true,\n                waitUntil,\n                onClose: closeController.onClose.bind(closeController),\n                onAfterTaskError: undefined,\n              },\n              requestEndedState: { ended: false },\n              isPrefetchRequest: request.headers.has(\n                NEXT_ROUTER_PREFETCH_HEADER\n              ),\n              buildId: buildId ?? '',\n              previouslyRevalidatedTags: [],\n            })\n\n            return await workAsyncStorage.run(workStore, () =>\n              workUnitAsyncStorage.run(\n                requestStore,\n                params.handler,\n                request,\n                event\n              )\n            )\n          } finally {\n            // middleware cannot stream, so we can consider the response closed\n            // as soon as the handler returns.\n            // we can delay running it until a bit later --\n            // if it's needed, we'll have a `waitUntil` lock anyway.\n            setTimeout(() => {\n              closeController.dispatchClose()\n            }, 0)\n          }\n        }\n      )\n    }\n    return params.handler(request, event)\n  })\n\n  // check if response is a Response object\n  if (response && !(response instanceof Response)) {\n    throw new TypeError('Expected an instance of Response to be returned')\n  }\n\n  if (response && cookiesFromResponse) {\n    response.headers.set('set-cookie', cookiesFromResponse)\n  }\n\n  /**\n   * For rewrites we must always include the locale in the final pathname\n   * so we re-create the NextURL forcing it to include it when the it is\n   * an internal rewrite. Also we make sure the outgoing rewrite URL is\n   * a data URL if the request was a data request.\n   */\n  const rewrite = response?.headers.get('x-middleware-rewrite')\n  if (response && rewrite && (isRSCRequest || !isEdgeRendering)) {\n    const destination = new NextURL(rewrite, {\n      forceLocale: true,\n      headers: params.request.headers,\n      nextConfig: params.request.nextConfig,\n    })\n\n    if (!process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE && !isEdgeRendering) {\n      if (destination.host === request.nextUrl.host) {\n        destination.buildId = buildId || destination.buildId\n        response.headers.set('x-middleware-rewrite', String(destination))\n      }\n    }\n\n    /**\n     * When the request is a data request we must show if there was a rewrite\n     * with an internal header so the client knows which component to load\n     * from the data request.\n     */\n    const { url: relativeDestination, isRelative } = parseRelativeURL(\n      destination.toString(),\n      requestURL.toString()\n    )\n\n    if (\n      !isEdgeRendering &&\n      isNextDataRequest &&\n      // if the rewrite is external and external rewrite\n      // resolving config is enabled don't add this header\n      // so the upstream app can set it instead\n      !(\n        process.env.__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE &&\n        relativeDestination.match(/http(s)?:\\/\\//)\n      )\n    ) {\n      response.headers.set('x-nextjs-rewrite', relativeDestination)\n    }\n\n    // If this is an RSC request, and the pathname or search has changed, and\n    // this isn't an external rewrite, we need to set the rewritten pathname and\n    // query headers.\n    if (isRSCRequest && isRelative) {\n      if (requestURL.pathname !== destination.pathname) {\n        response.headers.set(NEXT_REWRITTEN_PATH_HEADER, destination.pathname)\n      }\n      if (requestURL.search !== destination.search) {\n        response.headers.set(\n          NEXT_REWRITTEN_QUERY_HEADER,\n          // remove the leading ? from the search string\n          destination.search.slice(1)\n        )\n      }\n    }\n  }\n\n  /**\n   * For redirects we will not include the locale in case when it is the\n   * default and we must also make sure the outgoing URL is a data one if\n   * the incoming request was a data request.\n   */\n  const redirect = response?.headers.get('Location')\n  if (response && redirect && !isEdgeRendering) {\n    const redirectURL = new NextURL(redirect, {\n      forceLocale: false,\n      headers: params.request.headers,\n      nextConfig: params.request.nextConfig,\n    })\n\n    /**\n     * Responses created from redirects have immutable headers so we have\n     * to clone the response to be able to modify it.\n     */\n    response = new Response(response.body, response)\n\n    if (!process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE) {\n      if (redirectURL.host === requestURL.host) {\n        redirectURL.buildId = buildId || redirectURL.buildId\n        response.headers.set('Location', redirectURL.toString())\n      }\n    }\n\n    /**\n     * When the request is a data request we can't use the location header as\n     * it may end up with CORS error. Instead we map to an internal header so\n     * the client knows the destination.\n     */\n    if (isNextDataRequest) {\n      response.headers.delete('Location')\n      response.headers.set(\n        'x-nextjs-redirect',\n        getRelativeURL(redirectURL.toString(), requestURL.toString())\n      )\n    }\n  }\n\n  const finalResponse = response ? response : NextResponse.next()\n\n  // Flight headers are not overridable / removable so they are applied at the end.\n  const middlewareOverrideHeaders = finalResponse.headers.get(\n    'x-middleware-override-headers'\n  )\n  const overwrittenHeaders: string[] = []\n  if (middlewareOverrideHeaders) {\n    for (const [key, value] of flightHeaders) {\n      finalResponse.headers.set(`x-middleware-request-${key}`, value)\n      overwrittenHeaders.push(key)\n    }\n\n    if (overwrittenHeaders.length > 0) {\n      finalResponse.headers.set(\n        'x-middleware-override-headers',\n        middlewareOverrideHeaders + ',' + overwrittenHeaders.join(',')\n      )\n    }\n  }\n\n  return {\n    response: finalResponse,\n    waitUntil: getWaitUntilPromiseFromEvent(event) ?? Promise.resolve(),\n    fetchMetrics: request.fetchMetrics,\n  }\n}\n"], "names": ["PageSignatureError", "fromNodeOutgoingHttpHeaders", "normalizeNextQueryParam", "NextFetchEvent", "getWaitUntilPromiseFromEvent", "NextRequest", "NextResponse", "parseRelativeURL", "getRelativeURL", "NextURL", "stripInternalSearchParams", "normalizeRscURL", "FLIGHT_HEADERS", "NEXT_REWRITTEN_PATH_HEADER", "NEXT_REWRITTEN_QUERY_HEADER", "RSC_HEADER", "ensureInstrumentationRegistered", "createRequestStoreForAPI", "workUnitAsyncStorage", "createWorkStore", "workAsyncStorage", "NEXT_ROUTER_PREFETCH_HEADER", "getTracer", "MiddlewareSpan", "CloseController", "getEdgePreviewProps", "getBuiltinRequestContext", "getImplicitTags", "NextRequestHint", "constructor", "params", "input", "init", "sourcePage", "page", "request", "respondWith", "waitUntil", "headersGetter", "keys", "headers", "Array", "from", "get", "key", "undefined", "propagator", "fn", "tracer", "withPropagatedContext", "testApisIntercepted", "ensureTestApisIntercepted", "process", "env", "NEXT_PRIVATE_TEST_PROXY", "interceptTestApis", "wrapRequestHandler", "require", "adapter", "isEdgeRendering", "globalThis", "__BUILD_MANIFEST", "url", "requestURL", "bypassNextUrl", "URL", "nextConfig", "searchParams", "value", "getAll", "normalizedKey", "delete", "val", "append", "buildId", "__NEXT_BUILD_ID", "requestHeaders", "isNextDataRequest", "has", "isRSCRequest", "pathname", "flightHeaders", "Map", "header", "toLowerCase", "set", "normalizeURL", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "toString", "body", "method", "signal", "Object", "defineProperty", "enumerable", "__incrementalCacheShared", "IncrementalCache", "__incrementalCache", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "minimalMode", "NODE_ENV", "fetchCacheKeyPrefix", "__NEXT_FETCH_CACHE_KEY_PREFIX", "dev", "getPrerenderManifest", "version", "routes", "dynamicRoutes", "notFoundRoutes", "preview", "outerWaitUntil", "event", "context", "response", "cookiesFromResponse", "isMiddleware", "bind", "closeController", "trace", "execute", "spanName", "nextUrl", "attributes", "onUpdateCookies", "cookies", "previewProps", "fallbackRouteParams", "implicitTags", "requestStore", "workStore", "renderOpts", "cacheLifeProfiles", "experimental", "cacheLife", "isRoutePPREnabled", "dynamicIO", "authInterrupts", "supportsDynamicResponse", "onClose", "onAfterTaskError", "requestEndedState", "ended", "isPrefetchRequest", "previouslyRevalidatedTags", "run", "handler", "setTimeout", "dispatchClose", "Response", "TypeError", "rewrite", "destination", "forceLocale", "host", "String", "relativeDestination", "isRelative", "__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE", "match", "search", "slice", "redirect", "redirectURL", "finalResponse", "next", "middlewareOverrideHeaders", "overwrittenHeaders", "push", "length", "join", "Promise", "resolve", "fetchMetrics"], "mappings": "AAEA,SAASA,kBAAkB,QAAQ,UAAS;AAC5C,SAASC,2BAA2B,EAAEC,uBAAuB,QAAQ,UAAS;AAC9E,SACEC,cAAc,EACdC,4BAA4B,QACvB,+BAA8B;AACrC,SAASC,WAAW,QAAQ,2BAA0B;AACtD,SAASC,YAAY,QAAQ,4BAA2B;AACxD,SACEC,gBAAgB,EAChBC,cAAc,QACT,+CAA8C;AACrD,SAASC,OAAO,QAAQ,aAAY;AACpC,SAASC,yBAAyB,QAAQ,oBAAmB;AAC7D,SAASC,eAAe,QAAQ,0CAAyC;AACzE,SACEC,cAAc,EACdC,0BAA0B,EAC1BC,2BAA2B,EAC3BC,UAAU,QACL,6CAA4C;AACnD,SAASC,+BAA+B,QAAQ,YAAW;AAC3D,SAASC,wBAAwB,QAAQ,iCAAgC;AACzE,SAASC,oBAAoB,QAAQ,iDAAgD;AACrF,SAASC,eAAe,QAAQ,8BAA6B;AAC7D,SAASC,gBAAgB,QAAQ,4CAA2C;AAC5E,SAASC,2BAA2B,QAAQ,6CAA4C;AACxF,SAASC,SAAS,QAAQ,sBAAqB;AAE/C,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SAASC,eAAe,QAAQ,iBAAgB;AAChD,SAASC,mBAAmB,QAAQ,2BAA0B;AAC9D,SAASC,wBAAwB,QAAQ,mCAAkC;AAC3E,SAASC,eAAe,QAAQ,uBAAsB;AAEtD,OAAO,MAAMC,wBAAwBvB;IAInCwB,YAAYC,MAIX,CAAE;QACD,KAAK,CAACA,OAAOC,KAAK,EAAED,OAAOE,IAAI;QAC/B,IAAI,CAACC,UAAU,GAAGH,OAAOI,IAAI;IAC/B;IAEA,IAAIC,UAAU;QACZ,MAAM,qBAAiD,CAAjD,IAAInC,mBAAmB;YAAEkC,MAAM,IAAI,CAACD,UAAU;QAAC,IAA/C,qBAAA;mBAAA;wBAAA;0BAAA;QAAgD;IACxD;IAEAG,cAAc;QACZ,MAAM,qBAAiD,CAAjD,IAAIpC,mBAAmB;YAAEkC,MAAM,IAAI,CAACD,UAAU;QAAC,IAA/C,qBAAA;mBAAA;wBAAA;0BAAA;QAAgD;IACxD;IAEAI,YAAY;QACV,MAAM,qBAAiD,CAAjD,IAAIrC,mBAAmB;YAAEkC,MAAM,IAAI,CAACD,UAAU;QAAC,IAA/C,qBAAA;mBAAA;wBAAA;0BAAA;QAAgD;IACxD;AACF;AAEA,MAAMK,gBAAwC;IAC5CC,MAAM,CAACC,UAAYC,MAAMC,IAAI,CAACF,QAAQD,IAAI;IAC1CI,KAAK,CAACH,SAASI,MAAQJ,QAAQG,GAAG,CAACC,QAAQC;AAC7C;AAWA,IAAIC,aAA8D,CAChEX,SACAY;IAEA,MAAMC,SAAS1B;IACf,OAAO0B,OAAOC,qBAAqB,CAACd,QAAQK,OAAO,EAAEO,IAAIT;AAC3D;AAEA,IAAIY,sBAAsB;AAE1B,SAASC;IACP,IAAI,CAACD,qBAAqB;QACxBA,sBAAsB;QACtB,IAAIE,QAAQC,GAAG,CAACC,uBAAuB,KAAK,QAAQ;YAClD,MAAM,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAE,GAC7C,sHAAsH;YACtHC,QAAQ;YACVF;YACAT,aAAaU,mBAAmBV;QAClC;IACF;AACF;AAEA,OAAO,eAAeY,QACpB5B,MAAsB;QA6HQJ;IA3H9ByB;IACA,MAAMnC;IAEN,yCAAyC;IACzC,MAAM2C,kBACJ,OAAO,AAACC,WAAmBC,gBAAgB,KAAK;IAElD/B,OAAOK,OAAO,CAAC2B,GAAG,GAAGnD,gBAAgBmB,OAAOK,OAAO,CAAC2B,GAAG;IAEvD,MAAMC,aAAajC,OAAOkC,aAAa,GACnC,IAAIC,IAAInC,OAAOK,OAAO,CAAC2B,GAAG,IAC1B,IAAIrD,QAAQqB,OAAOK,OAAO,CAAC2B,GAAG,EAAE;QAC9BtB,SAASV,OAAOK,OAAO,CAACK,OAAO;QAC/B0B,YAAYpC,OAAOK,OAAO,CAAC+B,UAAU;IACvC;IAEJ,yIAAyI;IACzI,4CAA4C;IAC5C,MAAM3B,OAAO;WAAIwB,WAAWI,YAAY,CAAC5B,IAAI;KAAG;IAChD,KAAK,MAAMK,OAAOL,KAAM;QACtB,MAAM6B,QAAQL,WAAWI,YAAY,CAACE,MAAM,CAACzB;QAE7C,MAAM0B,gBAAgBpE,wBAAwB0C;QAC9C,IAAI0B,eAAe;YACjBP,WAAWI,YAAY,CAACI,MAAM,CAACD;YAC/B,KAAK,MAAME,OAAOJ,MAAO;gBACvBL,WAAWI,YAAY,CAACM,MAAM,CAACH,eAAeE;YAChD;YACAT,WAAWI,YAAY,CAACI,MAAM,CAAC3B;QACjC;IACF;IAEA,4DAA4D;IAC5D,IAAI8B,UAAUtB,QAAQC,GAAG,CAACsB,eAAe,IAAI;IAC7C,IAAI,aAAaZ,YAAY;QAC3BW,UAAU,AAACX,WAAuBW,OAAO,IAAI;QAC7CX,WAAWW,OAAO,GAAG;IACvB;IAEA,MAAME,iBAAiB3E,4BAA4B6B,OAAOK,OAAO,CAACK,OAAO;IACzE,MAAMqC,oBAAoBD,eAAeE,GAAG,CAAC;IAC7C,MAAMC,eAAeH,eAAejC,GAAG,CAAC5B,gBAAgB;IAExD,IAAI8D,qBAAqBd,WAAWiB,QAAQ,KAAK,UAAU;QACzDjB,WAAWiB,QAAQ,GAAG;IACxB;IAEA,MAAMC,gBAAgB,IAAIC;IAE1B,iDAAiD;IACjD,IAAI,CAACvB,iBAAiB;QACpB,KAAK,MAAMwB,UAAUvE,eAAgB;YACnC,MAAMgC,MAAMuC,OAAOC,WAAW;YAC9B,MAAMhB,QAAQQ,eAAejC,GAAG,CAACC;YACjC,IAAIwB,UAAU,MAAM;gBAClBa,cAAcI,GAAG,CAACzC,KAAKwB;gBACvBQ,eAAeL,MAAM,CAAC3B;YACxB;QACF;IACF;IAEA,MAAM0C,eAAelC,QAAQC,GAAG,CAACkC,kCAAkC,GAC/D,IAAItB,IAAInC,OAAOK,OAAO,CAAC2B,GAAG,IAC1BC;IAEJ,MAAM5B,UAAU,IAAIP,gBAAgB;QAClCM,MAAMJ,OAAOI,IAAI;QACjB,mDAAmD;QACnDH,OAAOrB,0BAA0B4E,cAAcE,QAAQ;QACvDxD,MAAM;YACJyD,MAAM3D,OAAOK,OAAO,CAACsD,IAAI;YACzBjD,SAASoC;YACTc,QAAQ5D,OAAOK,OAAO,CAACuD,MAAM;YAC7BxB,YAAYpC,OAAOK,OAAO,CAAC+B,UAAU;YACrCyB,QAAQ7D,OAAOK,OAAO,CAACwD,MAAM;QAC/B;IACF;IAEA;;;;GAIC,GACD,IAAId,mBAAmB;QACrBe,OAAOC,cAAc,CAAC1D,SAAS,YAAY;YACzC2D,YAAY;YACZ1B,OAAO;QACT;IACF;IAEA,IACE,6CAA6C;IAC7C,8CAA8C;IAC9C,6CAA6C;IAC7C,CAAC,AAACR,WAAmBmC,wBAAwB,IAC7C,AAACjE,OAAekE,gBAAgB,EAChC;;QACEpC,WAAmBqC,kBAAkB,GAAG,IAAI,AAC5CnE,OAGAkE,gBAAgB,CAAC;YACjBE,iBAAiBpE,OAAOqE,uBAAuB;YAC/CC,aAAahD,QAAQC,GAAG,CAACgD,QAAQ,KAAK;YACtCC,qBAAqBlD,QAAQC,GAAG,CAACkD,6BAA6B;YAC9DC,KAAKpD,QAAQC,GAAG,CAACgD,QAAQ,KAAK;YAC9BzB,gBAAgB9C,OAAOK,OAAO,CAACK,OAAO;YAEtCiE,sBAAsB;gBACpB,OAAO;oBACLC,SAAS,CAAC;oBACVC,QAAQ,CAAC;oBACTC,eAAe,CAAC;oBAChBC,gBAAgB,EAAE;oBAClBC,SAASrF;gBACX;YACF;QACF;IACF;IAEA,mEAAmE;IACnE,gDAAgD;IAChD,MAAMsF,iBACJjF,OAAOK,OAAO,CAACE,SAAS,MAAIX,4BAAAA,+CAAAA,0BAA4BW,SAAS;IAEnE,MAAM2E,QAAQ,IAAI7G,eAAe;QAC/BgC;QACAD,MAAMJ,OAAOI,IAAI;QACjB+E,SAASF,iBAAiB;YAAE1E,WAAW0E;QAAe,IAAIlE;IAC5D;IACA,IAAIqE;IACJ,IAAIC;IAEJD,WAAW,MAAMpE,WAAWX,SAAS;QACnC,8DAA8D;QAC9D,MAAMiF,eACJtF,OAAOI,IAAI,KAAK,iBAAiBJ,OAAOI,IAAI,KAAK;QAEnD,IAAIkF,cAAc;YAChB,0FAA0F;YAC1F,4CAA4C;YAC5C,qFAAqF;YAErF,MAAM/E,YAAY2E,MAAM3E,SAAS,CAACgF,IAAI,CAACL;YACvC,MAAMM,kBAAkB,IAAI9F;YAE5B,OAAOF,YAAYiG,KAAK,CACtBhG,eAAeiG,OAAO,EACtB;gBACEC,UAAU,CAAC,WAAW,EAAEtF,QAAQuD,MAAM,CAAC,CAAC,EAAEvD,QAAQuF,OAAO,CAAC1C,QAAQ,EAAE;gBACpE2C,YAAY;oBACV,eAAexF,QAAQuF,OAAO,CAAC1C,QAAQ;oBACvC,eAAe7C,QAAQuD,MAAM;gBAC/B;YACF,GACA;gBACE,IAAI;wBA2BI5D,yCAAAA,4BAKIA,0CAAAA;oBA/BV,MAAM8F,kBAAkB,CAACC;wBACvBV,sBAAsBU;oBACxB;oBACA,MAAMC,eAAerG;oBACrB,MAAMS,OAAO,IAAI,YAAY;;oBAC7B,MAAM6F,sBAAsB;oBAE5B,MAAMC,eAAe,MAAMrG,gBACzBO,MACAC,QAAQuF,OAAO,EACfK;oBAGF,MAAME,eAAehH,yBACnBkB,SACAA,QAAQuF,OAAO,EACfM,cACAJ,iBACAE;oBAGF,MAAMI,YAAY/G,gBAAgB;wBAChCe;wBACA6F;wBACAI,YAAY;4BACVC,iBAAiB,GACftG,6BAAAA,OAAOK,OAAO,CAAC+B,UAAU,sBAAzBpC,0CAAAA,2BAA2BuG,YAAY,qBAAvCvG,wCAAyCwG,SAAS;4BACpDD,cAAc;gCACZE,mBAAmB;gCACnBC,WAAW;gCACXC,gBACE,CAAC,GAAC3G,8BAAAA,OAAOK,OAAO,CAAC+B,UAAU,sBAAzBpC,2CAAAA,4BAA2BuG,YAAY,qBAAvCvG,yCAAyC2G,cAAc;4BAC7D;4BACAC,yBAAyB;4BACzBrG;4BACAsG,SAASrB,gBAAgBqB,OAAO,CAACtB,IAAI,CAACC;4BACtCsB,kBAAkB/F;wBACpB;wBACAgG,mBAAmB;4BAAEC,OAAO;wBAAM;wBAClCC,mBAAmB5G,QAAQK,OAAO,CAACsC,GAAG,CACpCzD;wBAEFqD,SAASA,WAAW;wBACpBsE,2BAA2B,EAAE;oBAC/B;oBAEA,OAAO,MAAM5H,iBAAiB6H,GAAG,CAACf,WAAW,IAC3ChH,qBAAqB+H,GAAG,CACtBhB,cACAnG,OAAOoH,OAAO,EACd/G,SACA6E;gBAGN,SAAU;oBACR,mEAAmE;oBACnE,kCAAkC;oBAClC,+CAA+C;oBAC/C,wDAAwD;oBACxDmC,WAAW;wBACT7B,gBAAgB8B,aAAa;oBAC/B,GAAG;gBACL;YACF;QAEJ;QACA,OAAOtH,OAAOoH,OAAO,CAAC/G,SAAS6E;IACjC;IAEA,yCAAyC;IACzC,IAAIE,YAAY,CAAEA,CAAAA,oBAAoBmC,QAAO,GAAI;QAC/C,MAAM,qBAAgE,CAAhE,IAAIC,UAAU,oDAAd,qBAAA;mBAAA;wBAAA;0BAAA;QAA+D;IACvE;IAEA,IAAIpC,YAAYC,qBAAqB;QACnCD,SAAS1E,OAAO,CAAC6C,GAAG,CAAC,cAAc8B;IACrC;IAEA;;;;;GAKC,GACD,MAAMoC,UAAUrC,4BAAAA,SAAU1E,OAAO,CAACG,GAAG,CAAC;IACtC,IAAIuE,YAAYqC,WAAYxE,CAAAA,gBAAgB,CAACpB,eAAc,GAAI;QAC7D,MAAM6F,cAAc,IAAI/I,QAAQ8I,SAAS;YACvCE,aAAa;YACbjH,SAASV,OAAOK,OAAO,CAACK,OAAO;YAC/B0B,YAAYpC,OAAOK,OAAO,CAAC+B,UAAU;QACvC;QAEA,IAAI,CAACd,QAAQC,GAAG,CAACkC,kCAAkC,IAAI,CAAC5B,iBAAiB;YACvE,IAAI6F,YAAYE,IAAI,KAAKvH,QAAQuF,OAAO,CAACgC,IAAI,EAAE;gBAC7CF,YAAY9E,OAAO,GAAGA,WAAW8E,YAAY9E,OAAO;gBACpDwC,SAAS1E,OAAO,CAAC6C,GAAG,CAAC,wBAAwBsE,OAAOH;YACtD;QACF;QAEA;;;;KAIC,GACD,MAAM,EAAE1F,KAAK8F,mBAAmB,EAAEC,UAAU,EAAE,GAAGtJ,iBAC/CiJ,YAAYhE,QAAQ,IACpBzB,WAAWyB,QAAQ;QAGrB,IACE,CAAC7B,mBACDkB,qBACA,kDAAkD;QAClD,oDAAoD;QACpD,yCAAyC;QACzC,CACEzB,CAAAA,QAAQC,GAAG,CAACyG,0CAA0C,IACtDF,oBAAoBG,KAAK,CAAC,gBAAe,GAE3C;YACA7C,SAAS1E,OAAO,CAAC6C,GAAG,CAAC,oBAAoBuE;QAC3C;QAEA,yEAAyE;QACzE,4EAA4E;QAC5E,iBAAiB;QACjB,IAAI7E,gBAAgB8E,YAAY;YAC9B,IAAI9F,WAAWiB,QAAQ,KAAKwE,YAAYxE,QAAQ,EAAE;gBAChDkC,SAAS1E,OAAO,CAAC6C,GAAG,CAACxE,4BAA4B2I,YAAYxE,QAAQ;YACvE;YACA,IAAIjB,WAAWiG,MAAM,KAAKR,YAAYQ,MAAM,EAAE;gBAC5C9C,SAAS1E,OAAO,CAAC6C,GAAG,CAClBvE,6BACA,8CAA8C;gBAC9C0I,YAAYQ,MAAM,CAACC,KAAK,CAAC;YAE7B;QACF;IACF;IAEA;;;;GAIC,GACD,MAAMC,WAAWhD,4BAAAA,SAAU1E,OAAO,CAACG,GAAG,CAAC;IACvC,IAAIuE,YAAYgD,YAAY,CAACvG,iBAAiB;QAC5C,MAAMwG,cAAc,IAAI1J,QAAQyJ,UAAU;YACxCT,aAAa;YACbjH,SAASV,OAAOK,OAAO,CAACK,OAAO;YAC/B0B,YAAYpC,OAAOK,OAAO,CAAC+B,UAAU;QACvC;QAEA;;;KAGC,GACDgD,WAAW,IAAImC,SAASnC,SAASzB,IAAI,EAAEyB;QAEvC,IAAI,CAAC9D,QAAQC,GAAG,CAACkC,kCAAkC,EAAE;YACnD,IAAI4E,YAAYT,IAAI,KAAK3F,WAAW2F,IAAI,EAAE;gBACxCS,YAAYzF,OAAO,GAAGA,WAAWyF,YAAYzF,OAAO;gBACpDwC,SAAS1E,OAAO,CAAC6C,GAAG,CAAC,YAAY8E,YAAY3E,QAAQ;YACvD;QACF;QAEA;;;;KAIC,GACD,IAAIX,mBAAmB;YACrBqC,SAAS1E,OAAO,CAAC+B,MAAM,CAAC;YACxB2C,SAAS1E,OAAO,CAAC6C,GAAG,CAClB,qBACA7E,eAAe2J,YAAY3E,QAAQ,IAAIzB,WAAWyB,QAAQ;QAE9D;IACF;IAEA,MAAM4E,gBAAgBlD,WAAWA,WAAW5G,aAAa+J,IAAI;IAE7D,iFAAiF;IACjF,MAAMC,4BAA4BF,cAAc5H,OAAO,CAACG,GAAG,CACzD;IAEF,MAAM4H,qBAA+B,EAAE;IACvC,IAAID,2BAA2B;QAC7B,KAAK,MAAM,CAAC1H,KAAKwB,MAAM,IAAIa,cAAe;YACxCmF,cAAc5H,OAAO,CAAC6C,GAAG,CAAC,CAAC,qBAAqB,EAAEzC,KAAK,EAAEwB;YACzDmG,mBAAmBC,IAAI,CAAC5H;QAC1B;QAEA,IAAI2H,mBAAmBE,MAAM,GAAG,GAAG;YACjCL,cAAc5H,OAAO,CAAC6C,GAAG,CACvB,iCACAiF,4BAA4B,MAAMC,mBAAmBG,IAAI,CAAC;QAE9D;IACF;IAEA,OAAO;QACLxD,UAAUkD;QACV/H,WAAWjC,6BAA6B4G,UAAU2D,QAAQC,OAAO;QACjEC,cAAc1I,QAAQ0I,YAAY;IACpC;AACF", "ignoreList": [0]}