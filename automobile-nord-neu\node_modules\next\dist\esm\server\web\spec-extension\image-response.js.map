{"version": 3, "sources": ["../../../../src/server/web/spec-extension/image-response.ts"], "sourcesContent": ["/**\n * @deprecated ImageResponse moved from \"next/server\" to \"next/og\" since Next.js 14, please import from \"next/og\" instead.\n * Migration with codemods: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#next-og-import\n */\nexport function ImageResponse(): never {\n  throw new Error(\n    'ImageResponse moved from \"next/server\" to \"next/og\" since Next.js 14, please import from \"next/og\" instead'\n  )\n}\n"], "names": ["ImageResponse", "Error"], "mappings": "AAAA;;;CAGC,GACD,OAAO,SAASA;IACd,MAAM,qBAEL,CAFK,IAAIC,MACR,+GADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF", "ignoreList": [0]}