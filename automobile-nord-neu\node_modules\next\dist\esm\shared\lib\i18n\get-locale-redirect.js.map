{"version": 3, "sources": ["../../../../src/shared/lib/i18n/get-locale-redirect.ts"], "sourcesContent": ["import type { DomainLocale } from '../../../server/config'\nimport type { I18NConfig } from '../../../server/config-shared'\nimport { acceptLanguage } from '../../../server/accept-header'\nimport { denormalizePagePath } from '../page-path/denormalize-page-path'\nimport { detectDomainLocale } from './detect-domain-locale'\nimport { formatUrl } from '../router/utils/format-url'\nimport { getCookieParser } from '../../../server/api-utils/get-cookie-parser'\n\ninterface Options {\n  defaultLocale: string\n  domainLocale?: DomainLocale\n  headers?: { [key: string]: string | string[] | undefined }\n  nextConfig: {\n    basePath?: string\n    i18n?: I18NConfig | null\n    trailingSlash?: boolean\n  }\n  pathLocale?: string\n  urlParsed: { hostname?: string | null; pathname: string }\n}\n\nfunction getLocaleFromCookie(\n  i18n: I18NConfig,\n  headers: { [key: string]: string | string[] | undefined } = {}\n) {\n  const nextLocale = getCookieParser(\n    headers || {}\n  )()?.NEXT_LOCALE?.toLowerCase()\n  return nextLocale\n    ? i18n.locales.find((locale) => nextLocale === locale.toLowerCase())\n    : undefined\n}\n\nfunction detectLocale({\n  i18n,\n  headers,\n  domainLocale,\n  preferredLocale,\n  pathLocale,\n}: {\n  i18n: I18NConfig\n  preferredLocale?: string\n  headers?: { [key: string]: string | string[] | undefined }\n  domainLocale?: DomainLocale\n  pathLocale?: string\n}) {\n  return (\n    pathLocale ||\n    domainLocale?.defaultLocale ||\n    getLocaleFromCookie(i18n, headers) ||\n    preferredLocale ||\n    i18n.defaultLocale\n  )\n}\n\nfunction getAcceptPreferredLocale(\n  i18n: I18NConfig,\n  headers?: { [key: string]: string | string[] | undefined }\n) {\n  if (\n    headers?.['accept-language'] &&\n    !Array.isArray(headers['accept-language'])\n  ) {\n    try {\n      return acceptLanguage(headers['accept-language'], i18n.locales)\n    } catch (err) {}\n  }\n}\n\nexport function getLocaleRedirect({\n  defaultLocale,\n  domainLocale,\n  pathLocale,\n  headers,\n  nextConfig,\n  urlParsed,\n}: Options) {\n  if (\n    nextConfig.i18n &&\n    nextConfig.i18n.localeDetection !== false &&\n    denormalizePagePath(urlParsed.pathname) === '/'\n  ) {\n    const preferredLocale = getAcceptPreferredLocale(nextConfig.i18n, headers)\n    const detectedLocale = detectLocale({\n      i18n: nextConfig.i18n,\n      preferredLocale,\n      headers,\n      pathLocale,\n      domainLocale,\n    })\n\n    const preferredDomain = detectDomainLocale(\n      nextConfig.i18n.domains,\n      undefined,\n      preferredLocale\n    )\n\n    if (domainLocale && preferredDomain) {\n      const isPDomain = preferredDomain.domain === domainLocale.domain\n      const isPLocale = preferredDomain.defaultLocale === preferredLocale\n      if (!isPDomain || !isPLocale) {\n        const scheme = `http${preferredDomain.http ? '' : 's'}`\n        const rlocale = isPLocale ? '' : preferredLocale\n        return `${scheme}://${preferredDomain.domain}/${rlocale}`\n      }\n    }\n\n    if (detectedLocale.toLowerCase() !== defaultLocale.toLowerCase()) {\n      return formatUrl({\n        ...urlParsed,\n        pathname: `${nextConfig.basePath || ''}/${detectedLocale}${\n          nextConfig.trailingSlash ? '/' : ''\n        }`,\n      })\n    }\n  }\n}\n"], "names": ["acceptLanguage", "denormalizePagePath", "detectDomainLocale", "formatUrl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getLocaleFromCookie", "i18n", "headers", "nextLocale", "NEXT_LOCALE", "toLowerCase", "locales", "find", "locale", "undefined", "detectLocale", "domainLocale", "preferredLocale", "pathLocale", "defaultLocale", "getAcceptPreferredLocale", "Array", "isArray", "err", "getLocaleRedirect", "nextConfig", "urlParsed", "localeDetection", "pathname", "detectedLocale", "preferredDomain", "domains", "isPDomain", "domain", "isPLocale", "scheme", "http", "rlocale", "basePath", "trailingSlash"], "mappings": "AAEA,SAASA,cAAc,QAAQ,gCAA+B;AAC9D,SAASC,mBAAmB,QAAQ,qCAAoC;AACxE,SAASC,kBAAkB,QAAQ,yBAAwB;AAC3D,SAASC,SAAS,QAAQ,6BAA4B;AACtD,SAASC,eAAe,QAAQ,8CAA6C;AAe7E,SAASC,oBACPC,IAAgB,EAChBC,OAA8D;IAA9DA,IAAAA,oBAAAA,UAA4D,CAAC;QAE1CH,8BAAAA;IAAnB,MAAMI,cAAaJ,mBAAAA,gBACjBG,WAAW,CAAC,0BADKH,+BAAAA,iBAEdK,WAAW,qBAFGL,6BAEDM,WAAW;IAC7B,OAAOF,aACHF,KAAKK,OAAO,CAACC,IAAI,CAAC,CAACC,SAAWL,eAAeK,OAAOH,WAAW,MAC/DI;AACN;AAEA,SAASC,aAAa,KAYrB;IAZqB,IAAA,EACpBT,IAAI,EACJC,OAAO,EACPS,YAAY,EACZC,eAAe,EACfC,UAAU,EAOX,GAZqB;IAapB,OACEA,eACAF,gCAAAA,aAAcG,aAAa,KAC3Bd,oBAAoBC,MAAMC,YAC1BU,mBACAX,KAAKa,aAAa;AAEtB;AAEA,SAASC,yBACPd,IAAgB,EAChBC,OAA0D;IAE1D,IACEA,CAAAA,2BAAAA,OAAS,CAAC,kBAAkB,KAC5B,CAACc,MAAMC,OAAO,CAACf,OAAO,CAAC,kBAAkB,GACzC;QACA,IAAI;YACF,OAAOP,eAAeO,OAAO,CAAC,kBAAkB,EAAED,KAAKK,OAAO;QAChE,EAAE,OAAOY,KAAK,CAAC;IACjB;AACF;AAEA,OAAO,SAASC,kBAAkB,KAOxB;IAPwB,IAAA,EAChCL,aAAa,EACbH,YAAY,EACZE,UAAU,EACVX,OAAO,EACPkB,UAAU,EACVC,SAAS,EACD,GAPwB;IAQhC,IACED,WAAWnB,IAAI,IACfmB,WAAWnB,IAAI,CAACqB,eAAe,KAAK,SACpC1B,oBAAoByB,UAAUE,QAAQ,MAAM,KAC5C;QACA,MAAMX,kBAAkBG,yBAAyBK,WAAWnB,IAAI,EAAEC;QAClE,MAAMsB,iBAAiBd,aAAa;YAClCT,MAAMmB,WAAWnB,IAAI;YACrBW;YACAV;YACAW;YACAF;QACF;QAEA,MAAMc,kBAAkB5B,mBACtBuB,WAAWnB,IAAI,CAACyB,OAAO,EACvBjB,WACAG;QAGF,IAAID,gBAAgBc,iBAAiB;YACnC,MAAME,YAAYF,gBAAgBG,MAAM,KAAKjB,aAAaiB,MAAM;YAChE,MAAMC,YAAYJ,gBAAgBX,aAAa,KAAKF;YACpD,IAAI,CAACe,aAAa,CAACE,WAAW;gBAC5B,MAAMC,SAAS,AAAC,SAAML,CAAAA,gBAAgBM,IAAI,GAAG,KAAK,GAAE;gBACpD,MAAMC,UAAUH,YAAY,KAAKjB;gBACjC,OAAO,AAAGkB,SAAO,QAAKL,gBAAgBG,MAAM,GAAC,MAAGI;YAClD;QACF;QAEA,IAAIR,eAAenB,WAAW,OAAOS,cAAcT,WAAW,IAAI;YAChE,OAAOP,UAAU;gBACf,GAAGuB,SAAS;gBACZE,UAAU,AAAGH,CAAAA,WAAWa,QAAQ,IAAI,EAAC,IAAE,MAAGT,iBACxCJ,CAAAA,WAAWc,aAAa,GAAG,MAAM,EAAC;YAEtC;QACF;IACF;AACF", "ignoreList": [0]}