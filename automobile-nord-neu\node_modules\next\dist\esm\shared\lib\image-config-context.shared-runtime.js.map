{"version": 3, "sources": ["../../../src/shared/lib/image-config-context.shared-runtime.ts"], "sourcesContent": ["import React from 'react'\nimport type { ImageConfigComplete } from './image-config'\nimport { imageConfigDefault } from './image-config'\n\nexport const ImageConfigContext =\n  React.createContext<ImageConfigComplete>(imageConfigDefault)\n\nif (process.env.NODE_ENV !== 'production') {\n  ImageConfigContext.displayName = 'ImageConfigContext'\n}\n"], "names": ["React", "imageConfigDefault", "ImageConfigContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AAEzB,SAASC,kBAAkB,QAAQ,iBAAgB;AAEnD,OAAO,MAAMC,qBACXF,MAAMG,aAAa,CAAsBF,oBAAmB;AAE9D,IAAIG,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;IACzCJ,mBAAmBK,WAAW,GAAG;AACnC", "ignoreList": [0]}