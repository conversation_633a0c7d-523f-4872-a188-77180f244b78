{"version": 3, "sources": ["../../../src/shared/lib/image-external.tsx"], "sourcesContent": ["import type { ImageConfigComplete, ImageLoaderProps } from './image-config'\nimport type { ImageProps, ImageLoader, StaticImageData } from './get-img-props'\n\nimport { getImgProps } from './get-img-props'\nimport { Image } from '../../client/image-component'\n\n// This is replaced by webpack alias\nimport defaultLoader from 'next/dist/shared/lib/image-loader'\n\n/**\n * For more advanced use cases, you can call `getImageProps()`\n * to get the props that would be passed to the underlying `<img>` element,\n * and instead pass to them to another component, style, canvas, etc.\n *\n * Read more: [Next.js docs: `getImageProps`](https://nextjs.org/docs/app/api-reference/components/image#getimageprops)\n */\nexport function getImageProps(imgProps: ImageProps) {\n  const { props } = getImgProps(imgProps, {\n    defaultLoader,\n    // This is replaced by webpack define plugin\n    imgConf: process.env.__NEXT_IMAGE_OPTS as any as ImageConfigComplete,\n  })\n  // Normally we don't care about undefined props because we pass to JSX,\n  // but this exported function could be used by the end user for anything\n  // so we delete undefined props to clean it up a little.\n  for (const [key, value] of Object.entries(props)) {\n    if (value === undefined) {\n      delete props[key as keyof typeof props]\n    }\n  }\n  return { props }\n}\n\nexport default Image\n\nexport type { ImageProps, ImageLoaderProps, ImageLoader, StaticImageData }\n"], "names": ["getImgProps", "Image", "defaultLoader", "getImageProps", "imgProps", "props", "imgConf", "process", "env", "__NEXT_IMAGE_OPTS", "key", "value", "Object", "entries", "undefined"], "mappings": "AAGA,SAASA,WAAW,QAAQ,kBAAiB;AAC7C,SAASC,KAAK,QAAQ,+BAA8B;AAEpD,oCAAoC;AACpC,OAAOC,mBAAmB,oCAAmC;AAE7D;;;;;;CAMC,GACD,OAAO,SAASC,cAAcC,QAAoB;IAChD,MAAM,EAAEC,KAAK,EAAE,GAAGL,YAAYI,UAAU;QACtCF;QACA,4CAA4C;QAC5CI,SAASC,QAAQC,GAAG,CAACC,iBAAiB;IACxC;IACA,uEAAuE;IACvE,wEAAwE;IACxE,wDAAwD;IACxD,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACR,OAAQ;QAChD,IAAIM,UAAUG,WAAW;YACvB,OAAOT,KAAK,CAACK,IAA0B;QACzC;IACF;IACA,OAAO;QAAEL;IAAM;AACjB;AAEA,eAAeJ,MAAK", "ignoreList": [0]}