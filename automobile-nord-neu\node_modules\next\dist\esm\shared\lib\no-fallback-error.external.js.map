{"version": 3, "sources": ["../../../src/shared/lib/no-fallback-error.external.ts"], "sourcesContent": ["export class NoFallbackError extends Error {\n  constructor() {\n    super()\n    this.message = 'Internal: NoFallbackError'\n  }\n}\n"], "names": ["NoFallbackError", "Error", "constructor", "message"], "mappings": "AAAA,OAAO,MAAMA,wBAAwBC;IACnCC,aAAc;QACZ,KAAK;QACL,IAAI,CAACC,OAAO,GAAG;IACjB;AACF", "ignoreList": [0]}