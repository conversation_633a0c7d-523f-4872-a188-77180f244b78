'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { RefreshCw, Database, CheckCircle, AlertCircle, Clock, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface SyncResult {
  success: boolean;
  message: string;
  count?: number;
  timestamp: string;
  errors?: string[];
}

const AdminPage = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [lastSync, setLastSync] = useState<SyncResult | null>(null);
  const [syncHistory, setSyncHistory] = useState<SyncResult[]>([]);

  const handleSync = async () => {
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/sync-vehicles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const result: SyncResult = await response.json();
      
      setLastSync(result);
      setSyncHistory(prev => [result, ...prev.slice(0, 9)]); // Keep last 10 syncs
      
    } catch (error) {
      const errorResult: SyncResult = {
        success: false,
        message: 'Failed to connect to sync API',
        timestamp: new Date().toISOString(),
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
      
      setLastSync(errorResult);
      setSyncHistory(prev => [errorResult, ...prev.slice(0, 9)]);
    } finally {
      setIsLoading(false);
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('de-DE', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  return (
    <div className="min-h-screen bg-secondary-50">
      {/* Header */}
      <section className="bg-white border-b border-secondary-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="flex items-center justify-between"
          >
            <div>
              <h1 className="text-3xl lg:text-4xl font-bold text-secondary-900">
                Admin Dashboard
              </h1>
              <p className="text-xl text-secondary-600 mt-2">
                Fahrzeug-Synchronisation mit mobile.de
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Settings className="h-8 w-8 text-secondary-400" />
            </div>
          </motion.div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 space-y-8">
          
          {/* Sync Control */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Database className="h-6 w-6 text-primary-600" />
                  <span>Mobile.de Synchronisation</span>
                </CardTitle>
                <CardDescription>
                  Synchronisieren Sie Fahrzeugdaten von mobile.de mit Ihrer Webseite
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium text-secondary-900">Fahrzeugdaten synchronisieren</h3>
                    <p className="text-sm text-secondary-600">
                      Lädt die neuesten Fahrzeugdaten von mobile.de herunter
                    </p>
                  </div>
                  <Button 
                    onClick={handleSync} 
                    disabled={isLoading}
                    className="flex items-center space-x-2"
                  >
                    <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                    <span>{isLoading ? 'Synchronisiere...' : 'Jetzt synchronisieren'}</span>
                  </Button>
                </div>

                {/* Last Sync Result */}
                {lastSync && (
                  <div className="border-t border-secondary-200 pt-6">
                    <div className={`p-4 rounded-lg border ${
                      lastSync.success 
                        ? 'bg-green-50 border-green-200' 
                        : 'bg-red-50 border-red-200'
                    }`}>
                      <div className="flex items-start space-x-3">
                        {lastSync.success ? (
                          <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                        ) : (
                          <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
                        )}
                        <div className="flex-1">
                          <h4 className={`font-medium ${
                            lastSync.success ? 'text-green-900' : 'text-red-900'
                          }`}>
                            {lastSync.success ? 'Synchronisation erfolgreich' : 'Synchronisation fehlgeschlagen'}
                          </h4>
                          <p className={`text-sm mt-1 ${
                            lastSync.success ? 'text-green-700' : 'text-red-700'
                          }`}>
                            {lastSync.message}
                          </p>
                          {lastSync.count && (
                            <p className="text-sm text-secondary-600 mt-1">
                              {lastSync.count} Fahrzeuge synchronisiert
                            </p>
                          )}
                          <p className="text-xs text-secondary-500 mt-2">
                            {formatTimestamp(lastSync.timestamp)}
                          </p>
                          {lastSync.errors && lastSync.errors.length > 0 && (
                            <div className="mt-2">
                              <p className="text-sm font-medium text-red-700">Fehler:</p>
                              <ul className="text-sm text-red-600 mt-1 space-y-1">
                                {lastSync.errors.map((error, index) => (
                                  <li key={index}>• {error}</li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>

          {/* Sync History */}
          {syncHistory.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Clock className="h-6 w-6 text-primary-600" />
                    <span>Synchronisation Verlauf</span>
                  </CardTitle>
                  <CardDescription>
                    Die letzten Synchronisationen mit mobile.de
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {syncHistory.map((sync, index) => (
                      <div 
                        key={index}
                        className="flex items-center justify-between p-4 border border-secondary-200 rounded-lg"
                      >
                        <div className="flex items-center space-x-3">
                          {sync.success ? (
                            <CheckCircle className="h-5 w-5 text-green-600" />
                          ) : (
                            <AlertCircle className="h-5 w-5 text-red-600" />
                          )}
                          <div>
                            <p className="font-medium text-secondary-900">
                              {sync.success ? 'Erfolgreich' : 'Fehlgeschlagen'}
                            </p>
                            <p className="text-sm text-secondary-600">
                              {sync.message}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          {sync.count && (
                            <p className="text-sm font-medium text-secondary-900">
                              {sync.count} Fahrzeuge
                            </p>
                          )}
                          <p className="text-xs text-secondary-500">
                            {formatTimestamp(sync.timestamp)}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* API Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Card>
              <CardHeader>
                <CardTitle>API Information</CardTitle>
                <CardDescription>
                  Technische Details zur mobile.de Integration
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium text-secondary-900 mb-2">Sync Endpoint</h4>
                    <code className="text-sm bg-secondary-100 px-2 py-1 rounded">
                      POST /api/sync-vehicles
                    </code>
                  </div>
                  <div>
                    <h4 className="font-medium text-secondary-900 mb-2">Status</h4>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-secondary-600">API bereit</span>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium text-secondary-900 mb-2">Automatische Synchronisation</h4>
                    <p className="text-sm text-secondary-600">
                      Kann über Cron-Jobs oder Webhooks eingerichtet werden
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium text-secondary-900 mb-2">Datenformat</h4>
                    <p className="text-sm text-secondary-600">
                      JSON-basierte API mit Fahrzeugdaten-Mapping
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default AdminPage;
