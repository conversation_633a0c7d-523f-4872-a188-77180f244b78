import { NextRequest, NextResponse } from 'next/server';
import { mobileDeApi } from '@/lib/mobile-api';

export async function POST(request: NextRequest) {
  try {
    // In production, you might want to add authentication here
    // const authHeader = request.headers.get('authorization');
    // if (!authHeader || !isValid<PERSON><PERSON><PERSON>(authHeader)) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }

    console.log('Starting vehicle sync from mobile.de...');
    
    const result = await mobileDeApi.syncVehicles();
    
    if (result.success) {
      return NextResponse.json({
        success: true,
        message: `Successfully synced ${result.count} vehicles from mobile.de`,
        count: result.count,
        timestamp: new Date().toISOString()
      });
    } else {
      return NextResponse.json({
        success: false,
        message: 'Failed to sync vehicles',
        errors: result.errors,
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error in sync-vehicles API:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Internal server error during vehicle sync',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get sync status or last sync information
    return NextResponse.json({
      status: 'ready',
      message: 'Vehicle sync API is ready',
      lastSync: null, // In production, this would come from database
      endpoint: '/api/sync-vehicles',
      method: 'POST',
      description: 'Synchronizes vehicles from mobile.de API'
    });
  } catch (error) {
    return NextResponse.json({
      error: 'Failed to get sync status',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
