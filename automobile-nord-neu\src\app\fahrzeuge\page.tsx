'use client';

import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Search, Filter, Grid, List, SlidersHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import VehicleCard from '@/components/ui/vehicle-card';
import { mockVehicles } from '@/lib/mock-data';
import { Vehicle, VehicleFilter } from '@/types/vehicle';

const FahrzeugePage = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<VehicleFilter>({});

  // Filter vehicles based on search and filters
  const filteredVehicles = useMemo(() => {
    let filtered = mockVehicles;

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(vehicle =>
        `${vehicle.make} ${vehicle.model} ${vehicle.variant || ''}`
          .toLowerCase()
          .includes(searchQuery.toLowerCase())
      );
    }

    // Make filter
    if (filters.make && filters.make.length > 0) {
      filtered = filtered.filter(vehicle => filters.make!.includes(vehicle.make));
    }

    // Price filter
    if (filters.priceMin) {
      filtered = filtered.filter(vehicle => vehicle.price >= filters.priceMin!);
    }
    if (filters.priceMax) {
      filtered = filtered.filter(vehicle => vehicle.price <= filters.priceMax!);
    }

    // Year filter
    if (filters.yearMin) {
      filtered = filtered.filter(vehicle => vehicle.year >= filters.yearMin!);
    }
    if (filters.yearMax) {
      filtered = filtered.filter(vehicle => vehicle.year <= filters.yearMax!);
    }

    // Fuel filter
    if (filters.fuel && filters.fuel.length > 0) {
      filtered = filtered.filter(vehicle => filters.fuel!.includes(vehicle.fuel));
    }

    return filtered;
  }, [searchQuery, filters]);

  // Get unique makes for filter
  const uniqueMakes = Array.from(new Set(mockVehicles.map(v => v.make))).sort();
  const uniqueFuelTypes = Array.from(new Set(mockVehicles.map(v => v.fuel))).sort();

  const handleMakeFilter = (make: string) => {
    setFilters(prev => ({
      ...prev,
      make: prev.make?.includes(make)
        ? prev.make.filter(m => m !== make)
        : [...(prev.make || []), make]
    }));
  };

  const handleFuelFilter = (fuel: string) => {
    setFilters(prev => ({
      ...prev,
      fuel: prev.fuel?.includes(fuel as any)
        ? prev.fuel.filter(f => f !== fuel)
        : [...(prev.fuel || []), fuel as any]
    }));
  };

  const clearFilters = () => {
    setFilters({});
    setSearchQuery('');
  };

  return (
    <div className="min-h-screen bg-secondary-50">
      {/* Header */}
      <section className="bg-white border-b border-secondary-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center space-y-4"
          >
            <h1 className="text-3xl lg:text-4xl font-bold text-secondary-900">
              Unsere Fahrzeuge
            </h1>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              Entdecken Sie unsere große Auswahl an hochwertigen Fahrzeugen. 
              Aktuell haben wir {mockVehicles.length} Fahrzeuge für Sie im Angebot.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Search and Filter Bar */}
      <section className="bg-white border-b border-secondary-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-secondary-400" />
              <input
                type="text"
                placeholder="Marke, Modell oder Variante suchen..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-secondary-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>

            {/* Controls */}
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center space-x-2"
              >
                <SlidersHorizontal className="h-4 w-4" />
                <span>Filter</span>
              </Button>

              <div className="flex items-center space-x-2 border border-secondary-300 rounded-lg p-1">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>

              <div className="text-sm text-secondary-600">
                {filteredVehicles.length} von {mockVehicles.length} Fahrzeugen
              </div>
            </div>
          </div>

          {/* Filter Panel */}
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="mt-6 p-6 bg-secondary-50 rounded-xl"
            >
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Make Filter */}
                <div>
                  <h3 className="font-medium text-secondary-900 mb-3">Marke</h3>
                  <div className="space-y-2">
                    {uniqueMakes.map(make => (
                      <label key={make} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={filters.make?.includes(make) || false}
                          onChange={() => handleMakeFilter(make)}
                          className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
                        />
                        <span className="text-sm text-secondary-700">{make}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Fuel Filter */}
                <div>
                  <h3 className="font-medium text-secondary-900 mb-3">Kraftstoff</h3>
                  <div className="space-y-2">
                    {uniqueFuelTypes.map(fuel => (
                      <label key={fuel} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={filters.fuel?.includes(fuel as any) || false}
                          onChange={() => handleFuelFilter(fuel)}
                          className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
                        />
                        <span className="text-sm text-secondary-700">{fuel}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Price Filter */}
                <div>
                  <h3 className="font-medium text-secondary-900 mb-3">Preis</h3>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm text-secondary-700 mb-1">Von (€)</label>
                      <input
                        type="number"
                        placeholder="Min. Preis"
                        value={filters.priceMin || ''}
                        onChange={(e) => setFilters(prev => ({ ...prev, priceMin: Number(e.target.value) || undefined }))}
                        className="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm text-secondary-700 mb-1">Bis (€)</label>
                      <input
                        type="number"
                        placeholder="Max. Preis"
                        value={filters.priceMax || ''}
                        onChange={(e) => setFilters(prev => ({ ...prev, priceMax: Number(e.target.value) || undefined }))}
                        className="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6 flex justify-end">
                <Button variant="outline" onClick={clearFilters}>
                  Filter zurücksetzen
                </Button>
              </div>
            </motion.div>
          )}
        </div>
      </section>

      {/* Vehicle Grid */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {filteredVehicles.length > 0 ? (
            <div className={`grid gap-8 ${
              viewMode === 'grid' 
                ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
                : 'grid-cols-1'
            }`}>
              {filteredVehicles.map((vehicle, index) => (
                <motion.div
                  key={vehicle.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <VehicleCard vehicle={vehicle} />
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="w-24 h-24 bg-secondary-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Search className="h-12 w-12 text-secondary-400" />
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-2">
                Keine Fahrzeuge gefunden
              </h3>
              <p className="text-secondary-600 mb-6">
                Versuchen Sie es mit anderen Suchbegriffen oder passen Sie Ihre Filter an.
              </p>
              <Button onClick={clearFilters}>
                Filter zurücksetzen
              </Button>
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default FahrzeugePage;
