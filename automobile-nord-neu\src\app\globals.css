@import "tailwindcss";

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
}

body {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  background-attachment: fixed;
  color: #f8fafc;
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
}

/* Glass morphism utility classes */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-light {
  background: rgba(248, 250, 252, 0.9);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Modern gradient backgrounds */
.gradient-modern {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #06b6d4 100%);
  background-size: 200% 200%;
  animation: gradient 15s ease infinite;
}

.gradient-hero {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #6366f1 50%, #8b5cf6 75%, #06b6d4 100%);
  background-size: 400% 400%;
  animation: gradient 20s ease infinite;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.3);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
}

/* Selection styles */
::selection {
  background: rgba(99, 102, 241, 0.3);
  color: #f8fafc;
}

/* Focus styles */
*:focus {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
}

/* Text glow effect */
.text-glow {
  text-shadow: 0 0 20px rgba(99, 102, 241, 0.5);
}

/* Hover glow effect */
.hover-glow {
  transition: all 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 30px rgba(99, 102, 241, 0.4);
  transform: translateY(-2px);
}
