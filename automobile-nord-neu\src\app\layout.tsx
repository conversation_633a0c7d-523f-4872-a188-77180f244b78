import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Header from "@/components/layout/header";
import Footer from "@/components/layout/footer";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

export const metadata: Metadata = {
  title: "Automobile Nord GmbH - Ihr Autohändler in Flensburg",
  description: "Automobile Nord GmbH - Exklusive Fahrzeuge in Flensburg und Umgebung. Gebrauchtwagen An- und Verkauf, Finanzierung und Service. Wir finden Ihr Traumauto!",
  keywords: "Automobile Nord, Autohändler, Flensburg, Gebrauchtwagen, Neuwagen, Finanzierung, Service, Audi, BMW, Mercedes, Porsche",
  authors: [{ name: "Automobile Nord GmbH" }],
  creator: "Automobile Nord GmbH",
  publisher: "Automobile Nord GmbH",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "de_DE",
    url: "https://www.automobile-nord.com",
    title: "Automobile Nord GmbH - Ihr Autohändler in Flensburg",
    description: "Exklusive Fahrzeuge in Flensburg und Umgebung. Gebrauchtwagen An- und Verkauf, Finanzierung und Service.",
    siteName: "Automobile Nord GmbH",
  },
  twitter: {
    card: "summary_large_image",
    title: "Automobile Nord GmbH - Ihr Autohändler in Flensburg",
    description: "Exklusive Fahrzeuge in Flensburg und Umgebung. Gebrauchtwagen An- und Verkauf, Finanzierung und Service.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="de" className={inter.variable}>
      <body className="font-sans antialiased">
        <div className="flex min-h-screen flex-col">
          <Header />
          <main className="flex-1">
            {children}
          </main>
          <Footer />
        </div>
      </body>
    </html>
  );
}
