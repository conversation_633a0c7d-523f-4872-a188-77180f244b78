'use client';

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { ArrowRight, Car, Shield, Star, Users, CheckCircle, Phone, Sparkles, Zap, Award, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function Home() {
  const stats = [
    { label: 'Premium Fahrzeuge', value: '61+', icon: Car, color: 'from-violet-500 to-purple-600' },
    { label: 'Zufriedene Kunden', value: '500+', icon: Users, color: 'from-blue-500 to-cyan-600' },
    { label: 'Jahre Erfahrung', value: '15+', icon: Star, color: 'from-amber-500 to-orange-600' },
    { label: 'Garantie', value: '100%', icon: Shield, color: 'from-emerald-500 to-teal-600' },
  ];

  const services = [
    {
      title: 'Premium Gebrauchtwagen',
      description: 'Exklusive Auswahl an hochwertigen Fahrzeugen der Luxusklasse',
      features: ['Marktgerechte Preise', 'Qualitätsprüfung', 'Garantieleistungen'],
      icon: Car,
      gradient: 'from-violet-500 to-purple-600'
    },
    {
      title: 'Finanzierung & Leasing',
      description: 'Maßgeschneiderte Finanzierungslösungen für Ihr Traumfahrzeug',
      features: ['Günstige Konditionen', 'Schnelle Abwicklung', 'Persönliche Beratung'],
      icon: Zap,
      gradient: 'from-blue-500 to-cyan-600'
    },
    {
      title: 'Inzahlungnahme',
      description: 'Wir kaufen Ihr Fahrzeug zu fairen Marktkonditionen',
      features: ['Sofortbewertung', 'Bargeldauszahlung', 'Unkomplizierte Abwicklung'],
      icon: Award,
      gradient: 'from-emerald-500 to-teal-600'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="clean-hero py-16 sm:py-24 lg:py-32">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="space-y-6"
            >
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                Wir finden dein{' '}
                <span className="primary-blue">Traumauto!</span>
              </h1>

              <p className="text-xl sm:text-2xl text-gray-600 max-w-3xl mx-auto">
                Automobile Nord GmbH - Ihr zuverlässiger Partner für hochwertige Fahrzeuge in Flensburg und Umgebung.
              </p>
            </motion.div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button size="lg" className="bg-primary-blue hover:bg-primary-blue text-white px-8 py-3" asChild>
                  <Link href="/fahrzeuge">
                    <Car className="mr-2 h-5 w-5" />
                    Fahrzeuge ansehen
                  </Link>
                </Button>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button size="lg" variant="outline" className="border-primary-blue text-primary-blue hover:bg-primary-blue hover:text-white px-8 py-3" asChild>
                  <Link href="/kontakt">
                    <Phone className="mr-2 h-5 w-5" />
                    0461-66353453
                  </Link>
                </Button>
              </motion.div>
            </div>

            <div className="flex flex-wrap justify-center gap-8 pt-8">
              {[
                { icon: CheckCircle, text: 'Kundenzufriedenheit hat höchste Priorität' },
                { icon: Shield, text: 'Zuverlässiger Partner' },
                { icon: Star, text: 'Präzise Antworten' }
              ].map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                  className="flex items-center space-x-3"
                >
                  <div className="w-8 h-8 rounded-full bg-primary-blue flex items-center justify-center">
                    <item.icon className="h-4 w-4 text-white" />
                  </div>
                  <span className="text-gray-700 font-medium">{item.text}</span>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Willkommen Section */}
      <section className="clean-section py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Herzlich Willkommen
              </h2>
              <div className="max-w-4xl mx-auto space-y-4 text-lg text-gray-600">
                <p className="font-semibold">Kundenzufriedenheit hat für uns höchste Priorität.</p>
                <p className="font-semibold">Nicht nur dies, sondern WIR wollen DICH begeistern!</p>
                <div className="grid md:grid-cols-2 gap-4 mt-8">
                  <div className="space-y-2">
                    <p><strong>Bei uns bekommst du präzise Antworten.</strong></p>
                    <p><strong>Bei uns fühlst du dich wie zu Hause.</strong></p>
                  </div>
                  <div className="space-y-2">
                    <p><strong>Bei uns genießt du alle Freiheiten.</strong></p>
                    <p><strong>Bei uns bleibst du immer mobil.</strong></p>
                  </div>
                </div>
                <p className="text-xl font-bold primary-blue mt-8">Bei uns erwirbst du „das Auto".</p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-gray-50 rounded-lg p-8"
            >
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Hast Du Fragen? Ruf an!</h3>
              <a href="tel:+4946166353453" className="text-3xl font-bold primary-blue hover:underline">
                0461-66353453
              </a>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="bg-gray-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="clean-card p-6 text-center"
            >
              <Car className="h-12 w-12 primary-blue mx-auto mb-4" />
              <h3 className="text-xl font-bold text-gray-900 mb-4">Wir kaufen Jedes Auto</h3>
              <p className="text-gray-600 mb-4">
                Sie wollen Ihr Auto verkaufen? Das trifft sich gut. In uns haben Sie einen zuverlässigen Partner.
              </p>
              <ul className="text-sm text-gray-600 space-y-2">
                <li>• Eine marktgerechte Schätzung Ihres Fahrzeugs</li>
                <li>• Finanzkräftige Vormerkkunden</li>
                <li>• Individuelle und persönliche Betreuung</li>
                <li>• Kraftfahrzeuge jeglicher Art</li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="clean-card p-6 text-center"
            >
              <Settings className="h-12 w-12 primary-blue mx-auto mb-4" />
              <h3 className="text-xl font-bold text-gray-900 mb-4">Dienstleistungen</h3>
              <p className="text-gray-600 mb-4">
                Gebrauchtwagen An- und Verkauf mit umfassendem Service.
              </p>
              <ul className="text-sm text-gray-600 space-y-2">
                <li>• Große Auswahl an Fahrzeugen</li>
                <li>• Wir besorgen Ihr Wunschfahrzeug</li>
                <li>• Inzahlungnahme</li>
                <li>• Sofortankauf gegen Bargeld</li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="clean-card p-6 text-center"
            >
              <Users className="h-12 w-12 primary-blue mx-auto mb-4" />
              <h3 className="text-xl font-bold text-gray-900 mb-4">Unser Team</h3>
              <p className="text-gray-600 mb-4">
                Unser Team steht Ihnen während der Öffnungszeiten zur Verfügung.
              </p>
              <div className="text-sm text-gray-600 space-y-2">
                <p><strong>Öffnungszeiten:</strong></p>
                <p>Mo-Fr: 10:00 - 18:00</p>
                <p>Sa: 10:00 - 13:00</p>
                <p>So: Geschlossen</p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="clean-section py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900">
              Bereit für Ihr neues Auto?
            </h2>
            <p className="text-xl text-gray-600">
              Kontaktieren Sie uns noch heute und finden Sie Ihr Traumfahrzeug.
              Unser Team berät Sie gerne persönlich.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-primary-blue hover:bg-primary-blue text-white px-8 py-3" asChild>
                <Link href="/kontakt">
                  <Phone className="mr-2 h-5 w-5" />
                  Jetzt Kontakt aufnehmen
                </Link>
              </Button>

              <Button size="lg" variant="outline" className="border-primary-blue text-primary-blue hover:bg-primary-blue hover:text-white px-8 py-3" asChild>
                <Link href="/fahrzeuge">
                  <Car className="mr-2 h-5 w-5" />
                  Fahrzeuge ansehen
                </Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>


    </div>
  );
}
