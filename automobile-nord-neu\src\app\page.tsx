'use client';

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { ArrowRight, Car, Shield, Star, Users, CheckCircle, Phone, Sparkles, Zap, Award } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function Home() {
  const stats = [
    { label: 'Premium Fahrzeuge', value: '61+', icon: Car, color: 'from-violet-500 to-purple-600' },
    { label: 'Zufriedene Kunden', value: '500+', icon: Users, color: 'from-blue-500 to-cyan-600' },
    { label: 'Jahre Erfahrung', value: '15+', icon: Star, color: 'from-amber-500 to-orange-600' },
    { label: 'Garantie', value: '100%', icon: Shield, color: 'from-emerald-500 to-teal-600' },
  ];

  const services = [
    {
      title: 'Premium Gebrauchtwagen',
      description: 'Exklusive Auswahl an hochwertigen Fahrzeugen der Luxusklasse',
      features: ['Marktgerechte Preise', 'Qualitätsprüfung', 'Garantieleistungen'],
      icon: Car,
      gradient: 'from-violet-500 to-purple-600'
    },
    {
      title: 'Finanzierung & Leasing',
      description: 'Maßgeschneiderte Finanzierungslösungen für Ihr Traumfahrzeug',
      features: ['Günstige Konditionen', 'Schnelle Abwicklung', 'Persönliche Beratung'],
      icon: Zap,
      gradient: 'from-blue-500 to-cyan-600'
    },
    {
      title: 'Inzahlungnahme',
      description: 'Wir kaufen Ihr Fahrzeug zu fairen Marktkonditionen',
      features: ['Sofortbewertung', 'Bargeldauszahlung', 'Unkomplizierte Abwicklung'],
      icon: Award,
      gradient: 'from-emerald-500 to-teal-600'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative gradient-hero py-32 lg:py-40 overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-72 h-72 bg-violet-500/20 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-cyan-500/20 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-purple-500/20 rounded-full blur-3xl animate-float" style={{ animationDelay: '4s' }}></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="space-y-10"
            >
              <div className="space-y-6">
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="inline-flex items-center px-4 py-2 rounded-full glass text-sm font-medium text-white/90"
                >
                  <Sparkles className="w-4 h-4 mr-2 text-violet-400" />
                  Premium Automobile in Flensburg
                </motion.div>

                <h1 className="text-5xl lg:text-7xl font-black text-white leading-tight">
                  Wir finden Ihr{' '}
                  <span className="bg-gradient-to-r from-violet-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent text-glow">
                    Traumauto
                  </span>
                </h1>

                <p className="text-xl lg:text-2xl text-white/80 leading-relaxed font-light">
                  Automobile Nord GmbH - Ihr exklusiver Partner für Premium-Fahrzeuge
                  in Flensburg und Umgebung. Erleben Sie Luxus neu definiert.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-6">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button size="xl" className="bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white border-0 shadow-glow hover-glow" asChild>
                    <Link href="/fahrzeuge">
                      <Sparkles className="mr-3 h-5 w-5" />
                      Fahrzeuge entdecken
                      <ArrowRight className="ml-3 h-5 w-5" />
                    </Link>
                  </Button>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button size="xl" variant="outline" className="glass border-white/20 text-white hover:bg-white/10" asChild>
                    <Link href="/kontakt">
                      <Phone className="mr-3 h-5 w-5" />
                      Beratung anfragen
                    </Link>
                  </Button>
                </motion.div>
              </div>

              <div className="flex flex-wrap gap-6 pt-6">
                {[
                  { icon: CheckCircle, text: 'Geprüfte Premium-Qualität' },
                  { icon: Shield, text: 'Umfassende Garantie' },
                  { icon: Star, text: 'Exklusiver Service' }
                ].map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                    className="flex items-center space-x-3"
                  >
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-emerald-400 to-teal-400 flex items-center justify-center">
                      <item.icon className="h-4 w-4 text-white" />
                    </div>
                    <span className="text-white/90 font-medium">{item.text}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.8, rotateY: 20 }}
              animate={{ opacity: 1, scale: 1, rotateY: 0 }}
              transition={{ duration: 1, delay: 0.3 }}
              className="relative"
            >
              <div className="relative">
                {/* Main glass card */}
                <div className="glass-light rounded-4xl p-8 shadow-glow-lg">
                  <div className="text-center space-y-6">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                      className="w-32 h-32 mx-auto bg-gradient-to-r from-violet-500 to-purple-600 rounded-3xl flex items-center justify-center shadow-glow"
                    >
                      <Car className="h-16 w-16 text-white" />
                    </motion.div>

                    <div>
                      <h3 className="text-2xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent">
                        Premium Collection
                      </h3>
                      <p className="text-slate-600 mt-2">Exklusive Fahrzeuge für anspruchsvolle Kunden</p>
                    </div>

                    <div className="grid grid-cols-2 gap-4 pt-4">
                      {['Audi', 'Mercedes', 'Porsche', 'BMW'].map((brand, index) => (
                        <motion.div
                          key={brand}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
                          className="glass rounded-2xl p-3 text-center"
                        >
                          <span className="text-slate-700 font-semibold">{brand}</span>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Floating elements */}
                <motion.div
                  animate={{ y: [-10, 10, -10] }}
                  transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
                  className="absolute -top-6 -right-6 w-24 h-24 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-2xl flex items-center justify-center shadow-glow"
                >
                  <Star className="h-8 w-8 text-white" />
                </motion.div>

                <motion.div
                  animate={{ y: [10, -10, 10] }}
                  transition={{ duration: 5, repeat: Infinity, ease: "easeInOut" }}
                  className="absolute -bottom-6 -left-6 w-20 h-20 bg-gradient-to-r from-emerald-400 to-teal-500 rounded-2xl flex items-center justify-center shadow-glow"
                >
                  <Shield className="h-6 w-6 text-white" />
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-24 relative">
        <div className="absolute inset-0 bg-slate-900/50"></div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-4">
              Vertrauen durch{' '}
              <span className="bg-gradient-to-r from-violet-400 to-cyan-400 bg-clip-text text-transparent">
                Exzellenz
              </span>
            </h2>
            <p className="text-xl text-white/70 max-w-2xl mx-auto">
              Zahlen, die für unsere Qualität und Ihren Erfolg sprechen
            </p>
          </motion.div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 30, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.05, y: -5 }}
                className="text-center space-y-4 group"
              >
                <div className={`w-20 h-20 bg-gradient-to-r ${stat.color} rounded-3xl flex items-center justify-center mx-auto shadow-glow group-hover:shadow-glow-lg transition-all duration-300`}>
                  <stat.icon className="h-10 w-10 text-white" />
                </div>
                <div>
                  <motion.div
                    className="text-4xl lg:text-5xl font-black text-white mb-2"
                    initial={{ scale: 0 }}
                    whileInView={{ scale: 1 }}
                    transition={{ duration: 0.6, delay: index * 0.1 + 0.3, type: "spring" }}
                    viewport={{ once: true }}
                  >
                    {stat.value}
                  </motion.div>
                  <div className="text-white/70 font-medium">{stat.label}</div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900"></div>
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-violet-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl"></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center space-y-6 mb-20"
          >
            <h2 className="text-4xl lg:text-6xl font-black text-white">
              Premium{' '}
              <span className="bg-gradient-to-r from-violet-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent">
                Services
              </span>
            </h2>
            <p className="text-xl lg:text-2xl text-white/70 max-w-4xl mx-auto font-light">
              Erleben Sie exklusiven Service auf höchstem Niveau -
              von der ersten Beratung bis zur Schlüsselübergabe.
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 40, rotateX: 10 }}
                whileInView={{ opacity: 1, y: 0, rotateX: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                whileHover={{ y: -10, scale: 1.02 }}
                className="group"
              >
                <div className="glass-dark rounded-4xl p-8 h-full hover-glow transition-all duration-500 border border-white/10">
                  <div className="space-y-6">
                    {/* Icon */}
                    <div className={`w-16 h-16 bg-gradient-to-r ${service.gradient} rounded-3xl flex items-center justify-center shadow-glow group-hover:shadow-glow-lg transition-all duration-300`}>
                      <service.icon className="h-8 w-8 text-white" />
                    </div>

                    {/* Content */}
                    <div className="space-y-4">
                      <h3 className="text-2xl font-bold text-white group-hover:text-glow transition-all duration-300">
                        {service.title}
                      </h3>
                      <p className="text-white/70 leading-relaxed">
                        {service.description}
                      </p>
                    </div>

                    {/* Features */}
                    <div className="space-y-3 pt-4 border-t border-white/10">
                      {service.features.map((feature, featureIndex) => (
                        <motion.div
                          key={featureIndex}
                          initial={{ opacity: 0, x: -20 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.5, delay: index * 0.2 + featureIndex * 0.1 }}
                          viewport={{ once: true }}
                          className="flex items-center space-x-3"
                        >
                          <div className="w-6 h-6 rounded-full bg-gradient-to-r from-emerald-400 to-teal-400 flex items-center justify-center flex-shrink-0">
                            <CheckCircle className="h-3 w-3 text-white" />
                          </div>
                          <span className="text-white/80 font-medium">{feature}</span>
                        </motion.div>
                      ))}
                    </div>

                    {/* CTA */}
                    <div className="pt-6">
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className={`w-full py-3 px-6 rounded-2xl bg-gradient-to-r ${service.gradient} text-white font-semibold shadow-glow hover:shadow-glow-lg transition-all duration-300`}
                      >
                        Mehr erfahren
                      </motion.button>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 gradient-modern"></div>
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-full h-full bg-black/20"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white/10 rounded-full blur-3xl animate-pulse"></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-12"
          >
            <div className="space-y-6">
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
                className="inline-flex items-center px-6 py-3 rounded-full glass text-white/90 font-medium"
              >
                <Sparkles className="w-5 h-5 mr-2 text-violet-400" />
                Ihr Traumauto wartet auf Sie
              </motion.div>

              <h2 className="text-5xl lg:text-7xl font-black text-white leading-tight">
                Bereit für{' '}
                <span className="bg-gradient-to-r from-violet-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent text-glow">
                  Luxus?
                </span>
              </h2>

              <p className="text-xl lg:text-2xl text-white/80 max-w-4xl mx-auto font-light leading-relaxed">
                Kontaktieren Sie uns noch heute und erleben Sie exklusiven Service
                auf höchstem Niveau. Ihr Traumfahrzeug ist nur einen Anruf entfernt.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button size="xl" className="bg-white text-slate-900 hover:bg-white/90 shadow-glow hover-glow font-bold" asChild>
                  <Link href="/kontakt">
                    <Sparkles className="mr-3 h-5 w-5" />
                    Jetzt Kontakt aufnehmen
                    <ArrowRight className="ml-3 h-5 w-5" />
                  </Link>
                </Button>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button size="xl" variant="outline" className="glass border-white/30 text-white hover:bg-white/10 font-bold" asChild>
                  <Link href="tel:+4946166353453">
                    <Phone className="mr-3 h-5 w-5" />
                    0461-66353453
                  </Link>
                </Button>
              </motion.div>
            </div>

            {/* Trust indicators */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="flex flex-wrap justify-center gap-8 pt-12"
            >
              {[
                { icon: Shield, text: 'Geprüfte Qualität' },
                { icon: Star, text: '15+ Jahre Erfahrung' },
                { icon: Users, text: '500+ Zufriedene Kunden' },
                { icon: Award, text: 'Premium Service' }
              ].map((item, index) => (
                <div key={index} className="flex items-center space-x-3 glass rounded-2xl px-4 py-3">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-emerald-400 to-teal-400 flex items-center justify-center">
                    <item.icon className="h-4 w-4 text-white" />
                  </div>
                  <span className="text-white/90 font-medium">{item.text}</span>
                </div>
              ))}
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
