'use client';

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { ArrowRight, Car, Shield, Star, Users, CheckCircle, Phone } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function Home() {
  const stats = [
    { label: 'Autos im Bestand', value: '61+', icon: Car },
    { label: 'Zufriedene Kunden', value: '500+', icon: Users },
    { label: 'Jahre Erfahrung', value: '15+', icon: Star },
    { label: 'Garan<PERSON>', value: '100%', icon: Shield },
  ];

  const services = [
    {
      title: 'Gebrauchtwagen An- und Verkauf',
      description: 'Große Auswahl an hochwertigen Gebrauchtwagen aller Marken',
      features: ['Marktgerechte Preise', 'Qualitätsprüfung', 'Garantieleistungen']
    },
    {
      title: 'Finanzierung & Leasing',
      description: 'Individuelle Finanzierungslösungen für Ihr Traumfahrzeug',
      features: ['Günstige Konditionen', 'Schnelle Abwicklung', 'Persönliche Beratung']
    },
    {
      title: 'Inzahlungnahme',
      description: 'Wir kaufen Ihr Fahrzeug zu fairen Konditionen',
      features: ['Sofortbewertung', 'Bargeldauszahlung', 'Unkomplizierte Abwicklung']
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-secondary-50 to-primary-50 py-20 lg:py-32 overflow-hidden">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="space-y-8"
            >
              <div className="space-y-4">
                <h1 className="text-4xl lg:text-6xl font-bold text-secondary-900 leading-tight">
                  Wir finden Ihr{' '}
                  <span className="text-primary-600">Traumauto</span>
                </h1>
                <p className="text-xl text-secondary-600 leading-relaxed">
                  Automobile Nord GmbH - Ihr vertrauensvoller Partner für exklusive Fahrzeuge
                  in Flensburg und Umgebung. Kundenzufriedenheit hat für uns höchste Priorität.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button size="lg" asChild>
                  <Link href="/fahrzeuge">
                    Fahrzeuge entdecken
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/kontakt">
                    <Phone className="mr-2 h-5 w-5" />
                    Beratung anfragen
                  </Link>
                </Button>
              </div>

              <div className="flex items-center space-x-6 pt-4">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <span className="text-sm text-secondary-600">Geprüfte Qualität</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <span className="text-sm text-secondary-600">Faire Preise</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <span className="text-sm text-secondary-600">Persönlicher Service</span>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="relative"
            >
              <div className="aspect-[4/3] bg-gradient-to-br from-primary-100 to-secondary-100 rounded-3xl flex items-center justify-center">
                <div className="text-center space-y-4">
                  <div className="w-24 h-24 bg-primary-600 rounded-2xl flex items-center justify-center mx-auto">
                    <Car className="h-12 w-12 text-white" />
                  </div>
                  <p className="text-secondary-600 font-medium">Premium Fahrzeuge</p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center space-y-3"
              >
                <div className="w-16 h-16 bg-primary-100 rounded-2xl flex items-center justify-center mx-auto">
                  <stat.icon className="h-8 w-8 text-primary-600" />
                </div>
                <div>
                  <div className="text-3xl font-bold text-secondary-900">{stat.value}</div>
                  <div className="text-sm text-secondary-600">{stat.label}</div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-secondary-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center space-y-4 mb-16"
          >
            <h2 className="text-3xl lg:text-4xl font-bold text-secondary-900">
              Unsere Services
            </h2>
            <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
              Wir bieten Ihnen einen umfassenden Service rund um Ihr Fahrzeug -
              von der Beratung bis zur Finanzierung.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full hover:shadow-large transition-all duration-300">
                  <CardHeader>
                    <CardTitle className="text-xl">{service.title}</CardTitle>
                    <CardDescription className="text-base">
                      {service.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {service.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center space-x-2">
                          <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                          <span className="text-sm text-secondary-600">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <div className="space-y-4">
              <h2 className="text-3xl lg:text-4xl font-bold text-white">
                Bereit für Ihr neues Fahrzeug?
              </h2>
              <p className="text-xl text-primary-100 max-w-2xl mx-auto">
                Kontaktieren Sie uns noch heute und lassen Sie sich von unserem
                erfahrenen Team beraten. Wir freuen uns auf Sie!
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/kontakt">
                  Jetzt Kontakt aufnehmen
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-primary-600" asChild>
                <Link href="tel:+4946166353453">
                  <Phone className="mr-2 h-5 w-5" />
                  0461-66353453
                </Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
