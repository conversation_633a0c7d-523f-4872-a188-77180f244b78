import React from 'react';
import Link from 'next/link';
import { MapPin, Phone, Mail, Clock, Facebook, Instagram, Youtube } from 'lucide-react';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-secondary-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary-600 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-lg">AN</span>
              </div>
              <div>
                <h3 className="text-xl font-bold">Automobile Nord</h3>
                <p className="text-sm text-secondary-400">GmbH</p>
              </div>
            </div>
            <p className="text-secondary-300 text-sm leading-relaxed">
              Wir bieten alles, was <PERSON>e brauchen, um Ihr Traum-Fahrzeug zu finden. 
              Unser Team konzentriert sich auf Ihre Wünsche.
            </p>
            <div className="flex space-x-4">
              <a 
                href="https://www.facebook.com/automobilenord" 
                target="_blank" 
                rel="noopener noreferrer"
                className="w-10 h-10 bg-secondary-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors"
              >
                <Facebook className="h-5 w-5" />
              </a>
              <a 
                href="https://www.instagram.com/automobilenord" 
                target="_blank" 
                rel="noopener noreferrer"
                className="w-10 h-10 bg-secondary-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors"
              >
                <Instagram className="h-5 w-5" />
              </a>
              <a 
                href="https://www.youtube.com/channel/UCVIvQHOig3Vw9sxNj_tUBcQ" 
                target="_blank" 
                rel="noopener noreferrer"
                className="w-10 h-10 bg-secondary-800 rounded-lg flex items-center justify-center hover:bg-primary-600 transition-colors"
              >
                <Youtube className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold">Schnellzugriff</h4>
            <ul className="space-y-2">
              <li>
                <Link href="/fahrzeuge" className="text-secondary-300 hover:text-primary-400 transition-colors">
                  Fahrzeuge
                </Link>
              </li>
              <li>
                <Link href="/finanzierung" className="text-secondary-300 hover:text-primary-400 transition-colors">
                  Finanzierung
                </Link>
              </li>
              <li>
                <Link href="/verkaufen" className="text-secondary-300 hover:text-primary-400 transition-colors">
                  Fahrzeug verkaufen
                </Link>
              </li>
              <li>
                <Link href="/service" className="text-secondary-300 hover:text-primary-400 transition-colors">
                  Service
                </Link>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold">Unsere Services</h4>
            <ul className="space-y-2 text-secondary-300 text-sm">
              <li>• Gebrauchtwagen An- und Verkauf</li>
              <li>• Finanzierungsberatung</li>
              <li>• Inzahlungnahme</li>
              <li>• Sofortankauf gegen Bargeld</li>
              <li>• Fahrzeugbewertung</li>
              <li>• Garantieleistungen</li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold">Kontakt</h4>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-primary-400 mt-0.5 flex-shrink-0" />
                <div className="text-secondary-300 text-sm">
                  <p>Grönfahrtweg 22</p>
                  <p>24955 Harrislee</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-primary-400 flex-shrink-0" />
                <a 
                  href="tel:+4946166353453" 
                  className="text-secondary-300 hover:text-primary-400 transition-colors text-sm"
                >
                  0461-66353453
                </a>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-primary-400 flex-shrink-0" />
                <a 
                  href="mailto:<EMAIL>" 
                  className="text-secondary-300 hover:text-primary-400 transition-colors text-sm"
                >
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center space-x-3">
                <Clock className="h-5 w-5 text-primary-400 flex-shrink-0" />
                <span className="text-secondary-300 text-sm">10:00 - 18:00</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-secondary-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-secondary-400 text-sm">
              © {currentYear} Automobile Nord GmbH. Alle Rechte vorbehalten.
            </p>
            <div className="flex space-x-6 text-sm">
              <Link href="/datenschutz" className="text-secondary-400 hover:text-primary-400 transition-colors">
                Datenschutz
              </Link>
              <Link href="/impressum" className="text-secondary-400 hover:text-primary-400 transition-colors">
                Impressum
              </Link>
              <Link href="/agb" className="text-secondary-400 hover:text-primary-400 transition-colors">
                AGB
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
