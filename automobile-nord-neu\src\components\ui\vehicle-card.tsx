'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Calendar, Gauge, Fuel, Settings, MapPin, Star } from 'lucide-react';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Vehicle } from '@/types/vehicle';
import { formatPrice, formatKilometers, formatPower, formatYear } from '@/lib/utils';
import { cn } from '@/lib/utils';

interface VehicleCardProps {
  vehicle: Vehicle;
  className?: string;
}

const VehicleCard: React.FC<VehicleCardProps> = ({ vehicle, className }) => {
  const primaryImage = vehicle.images.find(img => img.isPrimary) || vehicle.images[0];
  
  const getPriceRatingColor = (rating?: string) => {
    switch (rating) {
      case 'sehr guter Preis':
        return 'bg-green-100 text-green-800';
      case 'guter Preis':
        return 'bg-blue-100 text-blue-800';
      case 'fairer Preis':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return '';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
      className={cn("group", className)}
    >
      <Card className="h-full overflow-hidden hover:shadow-large transition-all duration-300 group-hover:-translate-y-1">
        {/* Image Section */}
        <div className="relative aspect-[4/3] overflow-hidden">
          {primaryImage ? (
            <Image
              src={primaryImage.url}
              alt={primaryImage.alt}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          ) : (
            <div className="w-full h-full bg-secondary-100 flex items-center justify-center">
              <div className="text-center space-y-2">
                <div className="w-16 h-16 bg-secondary-200 rounded-xl flex items-center justify-center mx-auto">
                  <Car className="h-8 w-8 text-secondary-400" />
                </div>
                <p className="text-secondary-500 text-sm">Kein Bild verfügbar</p>
              </div>
            </div>
          )}
          
          {/* Price Rating Badge */}
          {vehicle.priceRating && (
            <div className="absolute top-3 left-3">
              <span className={cn(
                "px-2 py-1 rounded-lg text-xs font-medium",
                getPriceRatingColor(vehicle.priceRating)
              )}>
                {vehicle.priceRating}
              </span>
            </div>
          )}

          {/* Featured Badge */}
          {vehicle.isFeatured && (
            <div className="absolute top-3 right-3">
              <div className="bg-primary-600 text-white px-2 py-1 rounded-lg flex items-center space-x-1">
                <Star className="h-3 w-3" />
                <span className="text-xs font-medium">Featured</span>
              </div>
            </div>
          )}

          {/* Condition Badge */}
          <div className="absolute bottom-3 left-3">
            <span className="bg-white/90 backdrop-blur-sm text-secondary-900 px-2 py-1 rounded-lg text-xs font-medium">
              {vehicle.condition}
            </span>
          </div>
        </div>

        <CardContent className="p-6 space-y-4">
          {/* Vehicle Title */}
          <div className="space-y-1">
            <h3 className="text-lg font-semibold text-secondary-900 line-clamp-1">
              {vehicle.make} {vehicle.model}
            </h3>
            {vehicle.variant && (
              <p className="text-sm text-secondary-600 line-clamp-1">{vehicle.variant}</p>
            )}
          </div>

          {/* Vehicle Details */}
          <div className="grid grid-cols-2 gap-3 text-sm">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-secondary-400" />
              <span className="text-secondary-600">{formatYear(vehicle.firstRegistration)}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Gauge className="h-4 w-4 text-secondary-400" />
              <span className="text-secondary-600">{formatKilometers(vehicle.mileage)}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Settings className="h-4 w-4 text-secondary-400" />
              <span className="text-secondary-600">{formatPower(vehicle.power.kw)}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Fuel className="h-4 w-4 text-secondary-400" />
              <span className="text-secondary-600">{vehicle.fuel}</span>
            </div>
          </div>

          {/* Location */}
          {vehicle.location && (
            <div className="flex items-center space-x-2 text-sm">
              <MapPin className="h-4 w-4 text-secondary-400" />
              <span className="text-secondary-600">
                {vehicle.location.city}, {vehicle.location.postalCode}
              </span>
            </div>
          )}

          {/* Price */}
          <div className="space-y-1">
            <div className="text-2xl font-bold text-secondary-900">
              {formatPrice(vehicle.price)}
            </div>
            {vehicle.netPrice && vehicle.vatIncluded && (
              <div className="text-sm text-secondary-600">
                {formatPrice(vehicle.netPrice)} (Netto) • 19% MwSt.
              </div>
            )}
          </div>
        </CardContent>

        <CardFooter className="p-6 pt-0 space-y-3">
          <div className="flex space-x-2 w-full">
            <Button asChild className="flex-1">
              <Link href={`/fahrzeuge/${vehicle.id}`}>
                Details ansehen
              </Link>
            </Button>
            <Button variant="outline" size="icon">
              <Star className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="flex space-x-2 w-full">
            <Button variant="outline" size="sm" className="flex-1">
              Vergleichen
            </Button>
            <Button variant="outline" size="sm" className="flex-1">
              Kontakt
            </Button>
          </div>
        </CardFooter>
      </Card>
    </motion.div>
  );
};

export default VehicleCard;
