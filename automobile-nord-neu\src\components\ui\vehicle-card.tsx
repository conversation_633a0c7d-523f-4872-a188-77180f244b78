'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Calendar, Gauge, Fuel, Settings, MapPin, Star } from 'lucide-react';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Vehicle } from '@/types/vehicle';
import { formatPrice, formatKilometers, formatPower, formatYear } from '@/lib/utils';
import { cn } from '@/lib/utils';

interface VehicleCardProps {
  vehicle: Vehicle;
  className?: string;
}

const VehicleCard: React.FC<VehicleCardProps> = ({ vehicle, className }) => {
  const primaryImage = vehicle.images.find(img => img.isPrimary) || vehicle.images[0];
  
  const getPriceRatingColor = (rating?: string) => {
    switch (rating) {
      case 'sehr guter Preis':
        return 'bg-green-100 text-green-800';
      case 'guter Preis':
        return 'bg-blue-100 text-blue-800';
      case 'fairer Preis':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return '';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 30, scale: 0.9 }}
      whileInView={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      whileHover={{ y: -10, scale: 1.02 }}
      className={cn("group", className)}
    >
      <div className="h-full overflow-hidden glass-dark rounded-4xl border border-white/10 hover-glow transition-all duration-500">
        {/* Image Section */}
        <div className="relative aspect-[4/3] overflow-hidden">
          {primaryImage ? (
            <Image
              src={primaryImage.url}
              alt={primaryImage.alt}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          ) : (
            <div className="w-full h-full bg-secondary-100 flex items-center justify-center">
              <div className="text-center space-y-2">
                <div className="w-16 h-16 bg-secondary-200 rounded-xl flex items-center justify-center mx-auto">
                  <Car className="h-8 w-8 text-secondary-400" />
                </div>
                <p className="text-secondary-500 text-sm">Kein Bild verfügbar</p>
              </div>
            </div>
          )}
          
          {/* Price Rating Badge */}
          {vehicle.priceRating && (
            <div className="absolute top-3 left-3">
              <span className={cn(
                "px-2 py-1 rounded-lg text-xs font-medium",
                getPriceRatingColor(vehicle.priceRating)
              )}>
                {vehicle.priceRating}
              </span>
            </div>
          )}

          {/* Featured Badge */}
          {vehicle.isFeatured && (
            <div className="absolute top-3 right-3">
              <div className="bg-primary-600 text-white px-2 py-1 rounded-lg flex items-center space-x-1">
                <Star className="h-3 w-3" />
                <span className="text-xs font-medium">Featured</span>
              </div>
            </div>
          )}

          {/* Condition Badge */}
          <div className="absolute bottom-3 left-3">
            <span className="bg-white/90 backdrop-blur-sm text-secondary-900 px-2 py-1 rounded-lg text-xs font-medium">
              {vehicle.condition}
            </span>
          </div>
        </div>

        <div className="p-8 space-y-6">
          {/* Vehicle Title */}
          <div className="space-y-2">
            <h3 className="text-2xl font-bold text-white group-hover:text-glow transition-all duration-300">
              {vehicle.make} {vehicle.model}
            </h3>
            {vehicle.variant && (
              <p className="text-white/70 line-clamp-1 font-medium">{vehicle.variant}</p>
            )}
          </div>

          {/* Vehicle Details */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-emerald-400 to-teal-400 flex items-center justify-center">
                <Calendar className="h-4 w-4 text-white" />
              </div>
              <span className="text-white/80 font-medium">{formatYear(vehicle.firstRegistration)}</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-violet-400 to-purple-400 flex items-center justify-center">
                <Gauge className="h-4 w-4 text-white" />
              </div>
              <span className="text-white/80 font-medium">{formatKilometers(vehicle.mileage)}</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-cyan-400 to-blue-400 flex items-center justify-center">
                <Settings className="h-4 w-4 text-white" />
              </div>
              <span className="text-white/80 font-medium">{formatPower(vehicle.power.kw)}</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-amber-400 to-orange-400 flex items-center justify-center">
                <Fuel className="h-4 w-4 text-white" />
              </div>
              <span className="text-white/80 font-medium">{vehicle.fuel}</span>
            </div>
          </div>

          {/* Location */}
          {vehicle.location && (
            <div className="flex items-center space-x-3 text-sm pt-2 border-t border-white/10">
              <div className="w-6 h-6 rounded-full bg-gradient-to-r from-pink-400 to-rose-400 flex items-center justify-center">
                <MapPin className="h-3 w-3 text-white" />
              </div>
              <span className="text-white/70 font-medium">
                {vehicle.location.city}, {vehicle.location.postalCode}
              </span>
            </div>
          )}

          {/* Price */}
          <div className="space-y-2 pt-4">
            <div className="text-3xl font-black text-white text-glow">
              {formatPrice(vehicle.price)}
            </div>
            {vehicle.netPrice && vehicle.vatIncluded && (
              <div className="text-sm text-white/60">
                {formatPrice(vehicle.netPrice)} (Netto) • 19% MwSt.
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="space-y-4 pt-6 border-t border-white/10">
            <div className="flex space-x-3 w-full">
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }} className="flex-1">
                <Button className="w-full bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white border-0 shadow-glow font-bold" asChild>
                  <Link href={`/fahrzeuge/${vehicle.id}`}>
                    Details ansehen
                  </Link>
                </Button>
              </motion.div>
              <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                <Button variant="outline" size="icon" className="glass border-white/20 text-white hover:bg-white/10">
                  <Star className="h-4 w-4" />
                </Button>
              </motion.div>
            </div>

            <div className="flex space-x-3 w-full">
              <Button variant="outline" size="sm" className="flex-1 glass border-white/20 text-white hover:bg-white/10 font-medium">
                Vergleichen
              </Button>
              <Button variant="outline" size="sm" className="flex-1 glass border-white/20 text-white hover:bg-white/10 font-medium">
                Kontakt
              </Button>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default VehicleCard;
