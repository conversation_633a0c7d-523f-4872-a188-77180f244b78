import { Vehicle } from '@/types/vehicle';

// Mobile.de API Configuration
const MOBILE_API_BASE_URL = 'https://services.mobile.de/search-api/search';
const MOBILE_SELLER_API_URL = 'https://services.mobile.de/seller-api';

// Types for mobile.de API responses
interface MobileDeVehicle {
  id: string;
  make: string;
  model: string;
  variant?: string;
  firstRegistration: string;
  mileage: number;
  price: {
    consumerPriceAmount: number;
    dealerPriceAmount?: number;
    currency: string;
    vatRate?: number;
  };
  power: {
    kw: number;
    hp: number;
  };
  fuel: string;
  transmission: string;
  category: string;
  condition: string;
  images: Array<{
    uri: string;
    description?: string;
  }>;
  features: string[];
  description?: string;
  exteriorColor?: string;
  interiorColor?: string;
  doors?: number;
  seats?: number;
  previousOwners?: number;
  co2Emission?: number;
  fuelConsumption?: {
    combinedConsumption?: number;
    cityConsumption?: number;
    highwayConsumption?: number;
  };
  energyEfficiencyClass?: string;
  location?: {
    city: string;
    zipCode: string;
    countryCode: string;
  };
  seller?: {
    name: string;
    address: {
      street: string;
      city: string;
      zipCode: string;
      countryCode: string;
    };
    phone: string;
    email: string;
  };
  creationDate: string;
  modificationDate: string;
}

interface MobileDeSearchResponse {
  searchResults: MobileDeVehicle[];
  totalResultCount: number;
  pageNumber: number;
  pageSize: number;
}

// Convert mobile.de vehicle to our Vehicle type
function convertMobileDeVehicle(mobileVehicle: MobileDeVehicle): Vehicle {
  const primaryImage = mobileVehicle.images[0];
  
  return {
    id: mobileVehicle.id,
    make: mobileVehicle.make,
    model: mobileVehicle.model,
    variant: mobileVehicle.variant,
    year: new Date(mobileVehicle.firstRegistration).getFullYear(),
    firstRegistration: mobileVehicle.firstRegistration,
    mileage: mobileVehicle.mileage,
    price: mobileVehicle.price.consumerPriceAmount,
    netPrice: mobileVehicle.price.dealerPriceAmount,
    vatIncluded: !!mobileVehicle.price.vatRate,
    power: {
      kw: mobileVehicle.power.kw,
      ps: mobileVehicle.power.hp
    },
    fuel: mapFuelType(mobileVehicle.fuel),
    transmission: mapTransmissionType(mobileVehicle.transmission),
    bodyType: mapBodyType(mobileVehicle.category),
    condition: mapConditionType(mobileVehicle.condition),
    images: mobileVehicle.images.map((img, index) => ({
      id: `${mobileVehicle.id}-${index}`,
      url: img.uri,
      alt: img.description || `${mobileVehicle.make} ${mobileVehicle.model}`,
      isPrimary: index === 0,
      order: index + 1
    })),
    features: mobileVehicle.features || [],
    description: mobileVehicle.description,
    exteriorColor: mobileVehicle.exteriorColor,
    interiorColor: mobileVehicle.interiorColor,
    doors: mobileVehicle.doors,
    seats: mobileVehicle.seats,
    previousOwners: mobileVehicle.previousOwners,
    co2Emissions: mobileVehicle.co2Emission,
    fuelConsumption: mobileVehicle.fuelConsumption ? {
      combined: mobileVehicle.fuelConsumption.combinedConsumption,
      city: mobileVehicle.fuelConsumption.cityConsumption,
      highway: mobileVehicle.fuelConsumption.highwayConsumption
    } : undefined,
    energyEfficiencyClass: mobileVehicle.energyEfficiencyClass,
    location: mobileVehicle.location ? {
      city: mobileVehicle.location.city,
      postalCode: mobileVehicle.location.zipCode,
      country: mobileVehicle.location.countryCode === 'DE' ? 'Deutschland' : mobileVehicle.location.countryCode
    } : undefined,
    dealer: mobileVehicle.seller ? {
      name: mobileVehicle.seller.name,
      address: {
        street: mobileVehicle.seller.address.street,
        city: mobileVehicle.seller.address.city,
        postalCode: mobileVehicle.seller.address.zipCode,
        country: mobileVehicle.seller.address.countryCode === 'DE' ? 'Deutschland' : mobileVehicle.seller.address.countryCode
      },
      contact: {
        phone: mobileVehicle.seller.phone,
        email: mobileVehicle.seller.email
      }
    } : undefined,
    createdAt: mobileVehicle.creationDate,
    updatedAt: mobileVehicle.modificationDate,
    isAvailable: true,
    isFeatured: false
  };
}

// Mapping functions for mobile.de data
function mapFuelType(mobileFuel: string): any {
  const fuelMap: Record<string, any> = {
    'PETROL': 'Benzin',
    'DIESEL': 'Diesel',
    'ELECTRIC': 'Elektro',
    'HYBRID_PETROL': 'Hybrid',
    'HYBRID_DIESEL': 'Hybrid',
    'PLUGIN_HYBRID': 'Plug-in-Hybrid',
    'CNG': 'Erdgas',
    'LPG': 'Autogas',
    'HYDROGEN': 'Wasserstoff'
  };
  return fuelMap[mobileFuel] || 'Andere';
}

function mapTransmissionType(mobileTransmission: string): any {
  const transmissionMap: Record<string, any> = {
    'MANUAL_GEAR': 'Schaltgetriebe',
    'AUTOMATIC_GEAR': 'Automatik',
    'SEMI_AUTOMATIC_GEAR': 'Halbautomatik',
    'CVT': 'CVT'
  };
  return transmissionMap[mobileTransmission] || 'Schaltgetriebe';
}

function mapBodyType(mobileCategory: string): any {
  const bodyTypeMap: Record<string, any> = {
    'SEDAN': 'Limousine',
    'STATION_WAGON': 'Kombi',
    'SUV': 'SUV',
    'COUPE': 'Coupe',
    'CONVERTIBLE': 'Cabrio',
    'SMALL_CAR': 'Kleinwagen',
    'COMPACT': 'Kompaktklasse',
    'VAN': 'Van',
    'PICKUP': 'Pickup',
    'SPORTS_CAR': 'Sportwagen'
  };
  return bodyTypeMap[mobileCategory] || 'Andere';
}

function mapConditionType(mobileCondition: string): any {
  const conditionMap: Record<string, any> = {
    'NEW': 'Neuwagen',
    'USED': 'Gebrauchtwagen',
    'DEMONSTRATION': 'Vorführwagen',
    'EMPLOYEE_CAR': 'Jahreswagen',
    'ACCIDENT': 'Unfallfahrzeug'
  };
  return conditionMap[mobileCondition] || 'Gebrauchtwagen';
}

// API Service Class
export class MobileDeApiService {
  private apiKey: string;
  private sellerId: string;

  constructor(apiKey: string, sellerId: string) {
    this.apiKey = apiKey;
    this.sellerId = sellerId;
  }

  // Fetch vehicles from mobile.de seller API
  async fetchVehicles(): Promise<Vehicle[]> {
    try {
      // Note: This is a mock implementation
      // In a real implementation, you would make actual API calls to mobile.de
      console.log('Fetching vehicles from mobile.de API...');
      
      // For now, return mock data
      // In production, replace this with actual API call:
      // const response = await fetch(`${MOBILE_SELLER_API_URL}/vehicles`, {
      //   headers: {
      //     'Authorization': `Bearer ${this.apiKey}`,
      //     'Content-Type': 'application/json'
      //   }
      // });
      
      // Mock response simulation
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Return mock vehicles (in production, this would be converted from API response)
      return this.getMockVehicles();
      
    } catch (error) {
      console.error('Error fetching vehicles from mobile.de:', error);
      throw new Error('Failed to fetch vehicles from mobile.de API');
    }
  }

  // Mock data for development
  private getMockVehicles(): Vehicle[] {
    // This would be replaced with actual API data conversion
    return [
      // Mock vehicles based on the current website data
      // In production, this would use convertMobileDeVehicle()
    ];
  }

  // Sync vehicles with mobile.de
  async syncVehicles(): Promise<{ success: boolean; count: number; errors?: string[] }> {
    try {
      const vehicles = await this.fetchVehicles();
      
      // Here you would typically save the vehicles to your database
      console.log(`Successfully synced ${vehicles.length} vehicles from mobile.de`);
      
      return {
        success: true,
        count: vehicles.length
      };
    } catch (error) {
      console.error('Error syncing vehicles:', error);
      return {
        success: false,
        count: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }
}

// Export a default instance (would use environment variables in production)
export const mobileDeApi = new MobileDeApiService(
  process.env.MOBILE_DE_API_KEY || 'mock-api-key',
  process.env.MOBILE_DE_SELLER_ID || 'mock-seller-id'
);
