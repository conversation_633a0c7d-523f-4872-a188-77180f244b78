export interface Vehicle {
  id: string;
  make: string;
  model: string;
  variant?: string;
  year: number;
  firstRegistration: string;
  mileage: number;
  price: number;
  netPrice?: number;
  vatIncluded?: boolean;
  power: {
    kw: number;
    ps: number;
  };
  fuel: FuelType;
  transmission: TransmissionType;
  bodyType: BodyType;
  condition: VehicleCondition;
  images: VehicleImage[];
  features: string[];
  description?: string;
  exteriorColor?: string;
  interiorColor?: string;
  doors?: number;
  seats?: number;
  previousOwners?: number;
  co2Emissions?: number;
  fuelConsumption?: {
    combined?: number;
    city?: number;
    highway?: number;
  };
  energyEfficiencyClass?: string;
  vin?: string;
  licensePlate?: string;
  location?: {
    city: string;
    postalCode: string;
    country: string;
  };
  dealer?: DealerInfo;
  createdAt: string;
  updatedAt: string;
  isAvailable: boolean;
  isFeatured?: boolean;
  priceRating?: 'sehr guter Preis' | 'guter Preis' | 'fairer Preis';
}

export interface VehicleImage {
  id: string;
  url: string;
  alt: string;
  isPrimary: boolean;
  order: number;
}

export interface DealerInfo {
  name: string;
  address: {
    street: string;
    city: string;
    postalCode: string;
    country: string;
  };
  contact: {
    phone: string;
    email: string;
    website?: string;
  };
  openingHours?: {
    [key: string]: string;
  };
}

export type FuelType = 
  | 'Benzin'
  | 'Diesel'
  | 'Elektro'
  | 'Hybrid'
  | 'Plug-in-Hybrid'
  | 'Erdgas'
  | 'Autogas'
  | 'Wasserstoff'
  | 'Andere';

export type TransmissionType = 
  | 'Schaltgetriebe'
  | 'Automatik'
  | 'Halbautomatik'
  | 'CVT';

export type BodyType = 
  | 'Limousine'
  | 'Kombi'
  | 'SUV'
  | 'Coupe'
  | 'Cabrio'
  | 'Kleinwagen'
  | 'Kompaktklasse'
  | 'Van'
  | 'Pickup'
  | 'Sportwagen'
  | 'Andere';

export type VehicleCondition = 
  | 'Neuwagen'
  | 'Gebrauchtwagen'
  | 'Jahreswagen'
  | 'Vorführwagen'
  | 'Unfallfahrzeug';

export interface VehicleFilter {
  make?: string[];
  model?: string[];
  priceMin?: number;
  priceMax?: number;
  yearMin?: number;
  yearMax?: number;
  mileageMax?: number;
  fuel?: FuelType[];
  transmission?: TransmissionType[];
  bodyType?: BodyType[];
  condition?: VehicleCondition[];
  powerMin?: number;
  powerMax?: number;
  features?: string[];
}

export interface VehicleSearchParams {
  query?: string;
  filter?: VehicleFilter;
  sortBy?: 'price' | 'year' | 'mileage' | 'power' | 'created';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface VehicleSearchResult {
  vehicles: Vehicle[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  filters: {
    makes: { name: string; count: number }[];
    models: { name: string; count: number }[];
    priceRange: { min: number; max: number };
    yearRange: { min: number; max: number };
    fuelTypes: { type: FuelType; count: number }[];
    bodyTypes: { type: BodyType; count: number }[];
  };
}
